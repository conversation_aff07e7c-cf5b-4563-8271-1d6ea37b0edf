﻿function New-PSFTempFile {
<#
	.SYNOPSIS
		Creates a temporary file.
	
	.DESCRIPTION
		Creates a temporary file.
	
	.PARAMETER Name
		Name of the temporary file item.
		Note: The actual filename will be autogenerated and ignore this value.
	
	.PARAMETER FileName
		Specify the exact name of the temporary file to generate.
		By default, a generic name is autogenerated instead.
	
	.PARAMETER Extension
		The extension the temporary file is supposed to have.
		Defaults to "tmp"
	
	.PARAMETER Timeout
		How long into the future this item is valid.
		Expired temporary items can be conveniently cleaned up using Remove-PSFTempItem.
	
	.PARAMETER ModuleName
		Name of the module the temp item belongs to.
		If called from within a module, this value will be detected automatically and needs not be specified.
	
	.EXAMPLE
		PS C:\> New-PSFTempFile -Name 'report' -Extension csv
		
		Creates a temporary file with the csv extension.
#>
	[Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSUseShouldProcessForStateChangingFunctions", "")]
	[CmdletBinding()]
	param (
		[Parameter(Mandatory = $true)]
		[PsfValidateScript('PSFramework.Validate.SafeName', ErrorString = 'PSFramework.Validate.SafeName')]
		[string]
		$Name,
		
		[string]
		$FileName,
		
		[string]
		$Extension = 'tmp',
		
		[PSFDateTime]
		$Timeout,
		
		[string]
		$ModuleName = [PSFramework.Utility.UtilityHost]::GetCallerInfo(1).CallerModule
	)
	
	begin {
		$tempPath = Get-PSFPath -Name Temp
	}
	process {
		$newPath = Join-Path -Path $tempPath -ChildPath "PSF_Temp_$(New-Guid).$($Extension)"
		if ($FileName) { Join-Path -Path $tempPath -ChildPath $FileName }
		try { $null = New-Item -Path $newPath -ItemType File -Force -ErrorAction Stop }
		catch { $PSCmdlet.ThrowTerminatingError($_) }
		
		$tempItem = [PSFramework.Temp.TempItemFile]::new($Name, $ModuleName, $newPath, $script:tempItems)
		if ($Timeout) { $tempItem.Timeout = $Timeout }
		$newPath
	}
}