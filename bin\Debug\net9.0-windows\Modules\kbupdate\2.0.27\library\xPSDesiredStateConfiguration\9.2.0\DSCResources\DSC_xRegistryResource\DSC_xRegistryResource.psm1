$errorActionPreference = 'Stop'
Set-StrictMode -Version 'Latest'

$modulePath = Join-Path -Path (Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent) -ChildPath 'Modules'

# Import the shared modules
Import-Module -Name (Join-Path -Path $modulePath `
    -ChildPath (Join-Path -Path 'xPSDesiredStateConfiguration.Common' `
        -ChildPath 'xPSDesiredStateConfiguration.Common.psm1'))

Import-Module -Name (Join-Path -Path $modulePath -ChildPath 'DscResource.Common')

# Import Localization Strings
$script:localizedData = Get-LocalizedData -DefaultUICulture 'en-US'

$script:registryDriveRoots = @{
    'HKCC' = 'HKEY_CURRENT_CONFIG'
    'HKCR' = 'HKEY_CLASSES_ROOT'
    'HKCU' = 'HKEY_CURRENT_USER'
    'HKLM' = 'HKEY_LOCAL_MACHINE'
    'HKUS' = 'HKEY_USERS'
}

<#
    .SYNOPSIS
        Retrieves the current state of the Registry resource with the given Key.

    .PARAMETER Key
        The path of the registry key to retrieve the state of.
        This path must include the registry hive.

    .PARAMETER ValueName
        The name of the registry value to retrieve the state of.

    .PARAMETER ValueData
        Used only as a boolean flag (along with ValueType) to determine if the target entity is the
        Default Value or the key itself.

    .PARAMETER ValueType
        Used only as a boolean flag (along with ValueData) to determine if the target entity is the
        Default Value or the key itself.
#>
function Get-TargetResource
{
    [CmdletBinding()]
    [OutputType([System.Collections.Hashtable])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Key,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [System.String]
        [AllowEmptyString()]
        $ValueName,

        [Parameter()]
        [System.String[]]
        $ValueData,

        [Parameter()]
        [ValidateSet('String', 'Binary', 'DWord', 'QWord', 'MultiString', 'ExpandString')]
        [System.String]
        $ValueType
    )

    Write-Verbose -Message ($script:localizedData.GetTargetResourceStartMessage -f $Key)

    $registryResource = @{
        Key = $Key
        Ensure = 'Absent'
        ValueName = $null
        ValueType = $null
        ValueData = $null
    }

    # Retrieve the registry key at the specified path
    $registryKey = Get-RegistryKey -RegistryKeyPath $Key

    # Check if the registry key exists
    if ($null -eq $registryKey)
    {
        Write-Verbose -Message ($script:localizedData.RegistryKeyDoesNotExist -f $Key)
    }
    else
    {
        Write-Verbose -Message ($script:localizedData.RegistryKeyExists -f $Key)

        # Check if the user specified a value name to retrieve
        $valueNameSpecified = (-not [System.String]::IsNullOrEmpty($ValueName)) -or $PSBoundParameters.ContainsKey('ValueType') -or $PSBoundParameters.ContainsKey('ValueData')

        if ($valueNameSpecified)
        {
            $valueDisplayName = Get-RegistryKeyValueDisplayName -RegistryKeyValueName $ValueName
            $registryResource['ValueName'] = $valueDisplayName

            # If a value name was specified, retrieve the value with the specified name from the retrieved registry key
            $registryKeyValue = Get-RegistryKeyValue -RegistryKey $registryKey -RegistryKeyValueName $ValueName

            # Check if the registry key value exists
            if ($null -eq $registryKeyValue)
            {
                Write-Verbose -Message ($script:localizedData.RegistryKeyValueDoesNotExist -f $Key, $valueDisplayName)
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.RegistryKeyValueExists -f $Key, $valueDisplayName)

                # If the registry key value exists, retrieve its type
                $actualValueType = Get-RegistryKeyValueType -RegistryKey $registryKey -RegistryKeyValueName $ValueName

                # If the registry key value exists, convert it to a readable string
                $registryKeyValueAsReadableString = @() + (ConvertTo-ReadableString -RegistryKeyValue $registryKeyValue -RegistryKeyValueType $actualValueType)

                $registryResource['Ensure'] = 'Present'
                $registryResource['ValueType'] = $actualValueType
                $registryResource['ValueData'] = $registryKeyValueAsReadableString
            }
        }
        else
        {
            $registryResource['Ensure'] = 'Present'
        }
    }

    Write-Verbose -Message ($script:localizedData.GetTargetResourceEndMessage -f $Key)

    return $registryResource
}

<#
    .SYNOPSIS
        Sets the Registry resource with the given Key to the specified state.

    .PARAMETER Key
        The path of the registry key to set the state of.
        This path must include the registry hive.

    .PARAMETER ValueName
        The name of the registry value to set.

        To add or remove a registry key, specify this property as an empty string without
        specifying ValueType or ValueData. To modify or remove the default value of a registry key,
        specify this property as an empty string while also specifying ValueType or ValueData.

    .PARAMETER Ensure
        Specifies whether or not the registry key with the given path and the registry key value with the given name should exist.

        To ensure that the registry key and value exists, set this property to Present.
        To ensure that the registry key and value do not exist, set this property to Absent.

        The default value is Present.

    .PARAMETER ValueData
        The data to set as the registry key value.

    .PARAMETER ValueType
        The type of the value to set.

        The supported types are:
            String (REG_SZ)
            Binary (REG-BINARY)
            Dword 32-bit (REG_DWORD)
            Qword 64-bit (REG_QWORD)
            Multi-string (REG_MULTI_SZ)
            Expandable string (REG_EXPAND_SZ)

    .PARAMETER Hex
        Specifies whether or not the value data should be expressed in hexadecimal format.

        If specified, DWORD/QWORD value data is presented in hexadecimal format.
        Not valid for other value types.

        The default value is $false.

    .PARAMETER Force
        Specifies whether or not to overwrite the registry key with the given path with the new
        value if it is already present.
#>
function Set-TargetResource
{
    [CmdletBinding(SupportsShouldProcess = $true)]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Key,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [System.String]
        [AllowEmptyString()]
        $ValueName,

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present',

        [Parameter()]
        [ValidateNotNull()]
        [System.String[]]
        $ValueData = @(),

        [Parameter()]
        [ValidateSet('String', 'Binary', 'DWord', 'QWord', 'MultiString', 'ExpandString')]
        [System.String]
        $ValueType = 'String',

        [Parameter()]
        [System.Boolean]
        $Hex = $false,

        [Parameter()]
        [System.Boolean]
        $Force = $false
    )

    Write-Verbose -Message ($script:localizedData.SetTargetResourceStartMessage -f $Key)

    # Retrieve the registry key at the specified path
    $registryKey = Get-RegistryKey -RegistryKeyPath $Key -WriteAccessAllowed

    # Check if the registry key exists
    if ($null -eq $registryKey)
    {
        Write-Verbose -Message ($script:localizedData.RegistryKeyDoesNotExist -f $Key)

        # Check if the user wants the registry key to exist
        if ($Ensure -eq 'Present')
        {
            Write-Verbose -Message ($script:localizedData.CreatingRegistryKey -f $Key)
            $registryKey = New-RegistryKey -RegistryKeyPath $Key
        }
    }

    # Check if the registry key exists
    if ($null -ne $registryKey)
    {
        Write-Verbose -Message ($script:localizedData.RegistryKeyExists -f $Key)

        $valueNameSpecified = (-not [System.String]::IsNullOrEmpty($ValueName)) -or $PSBoundParameters.ContainsKey('ValueType') -or $PSBoundParameters.ContainsKey('ValueData')

        # Check if the user wants to set a registry key value
        if ($valueNameSpecified)
        {
            # Retrieve the display name of the specified registry key value
            $valueDisplayName = Get-RegistryKeyValueDisplayName -RegistryKeyValueName $ValueName

            # Retrieve the existing registry key value
            $actualRegistryKeyValue = Get-RegistryKeyValue -RegistryKey $registryKey -RegistryKeyValueName $ValueName

            # Check if the user wants to add/modify or remove the registry key value
            if ($Ensure -eq 'Present')
            {
                # Convert the specified registry key value to the specified type
                $expectedRegistryKeyValue = switch ($ValueType)
                {
                    'Binary' { ConvertTo-Binary  -RegistryKeyValue $ValueData; break }
                    'DWord' { ConvertTo-DWord -RegistryKeyValue $ValueData -Hex $Hex; break }
                    'MultiString' { ConvertTo-MultiString -RegistryKeyValue $ValueData; break }
                    'QWord' { ConvertTo-QWord -RegistryKeyValue $ValueData -Hex $Hex; break }
                    default { ConvertTo-String -RegistryKeyValue $ValueData}
                }

                # Retrieve the name of the registry key
                $registryKeyName = Get-RegistryKeyName -RegistryKey $registryKey

                # Check if the registry key value exists
                if ($null -eq $actualRegistryKeyValue)
                {
                    # If the registry key value does not exist, set the new value
                    Write-Verbose -Message ($script:localizedData.SettingRegistryKeyValue -f $valueDisplayName, $Key)
                    $null = Set-RegistryKeyValue -RegistryKeyName $registryKeyName -RegistryKeyValueName $ValueName -RegistryKeyValue $expectedRegistryKeyValue -ValueType $ValueType
                }
                else
                {
                    # If the registry key value exists, retrieve its type
                    $actualValueType = Get-RegistryKeyValueType -RegistryKey $registryKey -RegistryKeyValueName $ValueName

                    $ShouldOverwriteRegistryKeyValue = $false

                    # Check if the specified type of the registry key value matches the retrieved type of the registry key value
                    if ($PSBoundParameters.ContainsKey('ValueType') -and ($ValueType -ne $actualValueType))
                    {
                        # If the specified type of the registry key value does not matches the retrieved type of the registry key value, should overwrite the value
                        $ShouldOverwriteRegistryKeyValue = $true
                    }
                    # Check if the specified registry key value matches the retrieved registry key value
                    elseif (-not (Test-RegistryKeyValuesMatch -ExpectedRegistryKeyValue $expectedRegistryKeyValue -ActualRegistryKeyValue $actualRegistryKeyValue -RegistryKeyValueType $ValueType))
                    {
                        # If the specified registry key value does not matches the retrieved registry key value, should overwrite the value
                        $ShouldOverwriteRegistryKeyValue = $true
                    }

                    # Check if the registry key value should be overwritten
                    if ($false -eq $ShouldOverwriteRegistryKeyValue)
                    {
                        # No change is needed
                        Write-Verbose -Message ($script:localizedData.RegistryKeyValueAlreadySet -f $valueDisplayName, $Key)
                    }
                    else
                    {
                        # Check if the user wants to overwrite the value
                        if (-not $Force)
                        {
                            # If the user does not want to overwrite the value, throw an error
                            New-InvalidOperationException -Message ($script:localizedData.CannotOverwriteExistingRegistryKeyValueWithoutForce -f $Key, $valueDisplayName)
                        }
                        else
                        {
                            # If the user does want to overwrite the value, overwrite the value
                            Write-Verbose -Message ($script:localizedData.OverwritingRegistryKeyValue -f $valueDisplayName, $Key)
                            $null = Set-RegistryKeyValue -RegistryKeyName $registryKeyName -RegistryKeyValueName $ValueName -RegistryKeyValue $expectedRegistryKeyValue -ValueType $ValueType
                        }
                    }
                }
            }
            else
            {
                # Check if the registry key value exists
                if ($null -ne $actualRegistryKeyValue)
                {
                    Write-Verbose -Message ($script:localizedData.RemovingRegistryKeyValue -f $valueDisplayName, $Key)

                    # If the specified registry key value exists, check if the user specified a registry key value with a name to remove
                    if (-not [System.String]::IsNullOrEmpty($ValueName))
                    {
                        # If the user specified a registry key value with a name to remove, remove the registry key value with the specified name
                        $null = Remove-RegistryKeyValue -RegistryKey $registryKey -RegistryKeyValueName $ValueName
                    }
                    else
                    {
                        # If the user did not specify a registry key value with a name to remove, remove the default registry key value
                        $null = Remove-DefaultRegistryKeyValue -RegistryKey $registryKey
                    }
                }
            }
        }
        else
        {
            # Check if the user wants to remove the registry key
            if ($Ensure -eq 'Absent')
            {
                # Retrieve the number of subkeys the registry key has
                $registryKeySubKeyCount = Get-RegistryKeySubKeyCount -RegistryKey $registryKey

                # Check if the registry key has subkeys and the user does not want to forcibly remove the registry key
                if ($registryKeySubKeyCount -gt 0 -and -not $Force)
                {
                    New-InvalidOperationException -Message ($script:localizedData.CannotRemoveExistingRegistryKeyWithSubKeysWithoutForce -f $Key)
                }
                else
                {
                    # Remove the registry key
                    Write-Verbose -Message ($script:localizedData.RemovingRegistryKey -f $Key)
                    $null = Remove-RegistryKey -RegistryKey $registryKey
                }
            }
        }
    }

    Write-Verbose -Message ($script:localizedData.SetTargetResourceEndMessage -f $Key)
}

<#
    .SYNOPSIS
        Tests if the Registry resource with the given key is in the specified state.

    .PARAMETER Key
        The path of the registry key to test the state of.
        This path must include the registry hive.

    .PARAMETER ValueName
        The name of the registry value to check for.
        Specify this property as an empty string ('') to check the default value of the registry key.

    .PARAMETER Ensure
        Specifies whether or not the registry key and value should exist.

        To test that they exist, set this property to "Present".
        To test that they do not exist, set the property to "Absent".
        The default value is "Present".

    .PARAMETER ValueData
        The data the registry key value should have.

    .PARAMETER ValueType
        The type of the value.

        The supported types are:
            String (REG_SZ)
            Binary (REG-BINARY)
            Dword 32-bit (REG_DWORD)
            Qword 64-bit (REG_QWORD)
            Multi-string (REG_MULTI_SZ)
            Expandable string (REG_EXPAND_SZ)

    .PARAMETER Hex
        Not used in Test-TargetResource.

    .PARAMETER Force
        Not used in Test-TargetResource.
#>
function Test-TargetResource
{
    [CmdletBinding()]
    [OutputType([System.Boolean])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Key,

        [Parameter(Mandatory = $true)]
        [AllowEmptyString()]
        [ValidateNotNull()]
        [System.String]
        $ValueName,

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present',

        [Parameter()]
        [ValidateNotNull()]
        [System.String[]]
        $ValueData = @(),

        [Parameter()]
        [ValidateSet('String', 'Binary', 'DWord', 'QWord', 'MultiString', 'ExpandString')]
        [System.String]
        $ValueType = 'String',

        [Parameter()]
        [System.Boolean]
        $Hex = $false,

        [Parameter()]
        [System.Boolean]
        $Force = $false
    )

    Write-Verbose -Message ($script:localizedData.TestTargetResourceStartMessage -f $Key)

    $registryResourceInDesiredState = $false

    $getTargetResourceParameters = @{
        Key = $Key
        ValueName = $ValueName
    }

    if ($PSBoundParameters.ContainsKey('ValueType'))
    {
        $getTargetResourceParameters['ValueType'] = $ValueType
    }

    if ($PSBoundParameters.ContainsKey('ValueData'))
    {
        $getTargetResourceParameters['ValueData'] = $ValueData
    }

    $registryResource = Get-TargetResource @getTargetResourceParameters

    # Check if the user specified a value name to retrieve
    $valueNameSpecified = (-not [System.String]::IsNullOrEmpty($ValueName)) -or $PSBoundParameters.ContainsKey('ValueType') -or $PSBoundParameters.ContainsKey('ValueData')

    if ($valueNameSpecified)
    {
        $valueDisplayName = Get-RegistryKeyValueDisplayName -RegistryKeyValueName $ValueName

        if ($registryResource.Ensure -eq 'Absent')
        {
            Write-Verbose -Message ($script:localizedData.RegistryKeyValueDoesNotExist -f $Key, $valueDisplayName)
            $registryResourceInDesiredState = $Ensure -eq 'Absent'
        }
        else
        {
            Write-Verbose -Message ($script:localizedData.RegistryKeyValueExists -f $Key, $valueDisplayName)

            if ($Ensure -eq 'Absent')
            {
                $registryResourceInDesiredState = $false
            }
            elseif ($PSBoundParameters.ContainsKey('ValueType') -and $ValueType -ne $registryResource.ValueType)
            {
                Write-Verbose -Message ($script:localizedData.RegistryKeyValueTypeDoesNotMatch -f $valueDisplayName, $Key, $ValueType, $registryResource.ValueType)

                $registryResourceInDesiredState = $false
            }
            elseif ($PSBoundParameters.ContainsKey('ValueData'))
            {
                # Need to get the actual registry key value since Get-TargetResource returns
                $registryKey = Get-RegistryKey -RegistryKeyPath $Key
                $actualRegistryKeyValue = Get-RegistryKeyValue -RegistryKey $registryKey -RegistryKeyValueName $ValueName

                if (-not $PSBoundParameters.ContainsKey('ValueType') -and $null -ne $registryResource.ValueType)
                {
                    $ValueType = $registryResource.ValueType
                }

                # Convert the specified registry key value to the specified type
                $expectedRegistryKeyValue = switch ($ValueType)
                {
                    'Binary' { ConvertTo-Binary  -RegistryKeyValue $ValueData; break }
                    'DWord' { ConvertTo-DWord -RegistryKeyValue $ValueData -Hex $Hex; break }
                    'MultiString' { ConvertTo-MultiString -RegistryKeyValue $ValueData; break }
                    'QWord' { ConvertTo-QWord -RegistryKeyValue $ValueData -Hex $Hex; break }
                    default { ConvertTo-String -RegistryKeyValue $ValueData; break }
                }

                if (-not (Test-RegistryKeyValuesMatch -ExpectedRegistryKeyValue $expectedRegistryKeyValue -ActualRegistryKeyValue $actualRegistryKeyValue -RegistryKeyValueType $ValueType))
                {
                    Write-Verbose -Message ($script:localizedData.RegistryKeyValueDoesNotMatch -f $valueDisplayName, $Key, $ValueData, $registryResource.ValueData)

                    $registryResourceInDesiredState = $false
                }
                else
                {
                    $registryResourceInDesiredState = $true
                }
            }
            else
            {
                $registryResourceInDesiredState = $true
            }
        }
    }
    else
    {
        if ($registryResource.Ensure -eq 'Present')
        {
            Write-Verbose -Message ($script:localizedData.RegistryKeyExists -f $Key)
            $registryResourceInDesiredState = $Ensure -eq 'Present'
        }
        else
        {
            Write-Verbose -Message ($script:localizedData.RegistryKeyDoesNotExist -f $Key)
            $registryResourceInDesiredState = $Ensure -eq 'Absent'
        }
    }

    Write-Verbose -Message ($script:localizedData.TestTargetResourceEndMessage -f $Key)

    return $registryResourceInDesiredState
}

<#
    .SYNOPSIS
        Retrieves the root of the specified path.

    .PARAMETER Path
        The path to retrieve the root of.
#>
function Get-PathRoot
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path
    )

    if ($Path.Contains('\'))
    {
        $pathRoot = $Path.Split('\')[0]
    }
    else
    {
        $pathRoot = $Path
    }

    return $pathRoot
}

<#
    .SYNOPSIS
        Converts the specified registry drive root to its corresponding registry drive name.

    .PARAMETER RegistryDriveRoot
        The registry drive root to convert.
#>
function ConvertTo-RegistryDriveName
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $RegistryDriveRoot
    )

    $registryDriveName = $null

    if ($script:registryDriveRoots.ContainsValue($RegistryDriveRoot))
    {
        foreach ($registryDriveRootsKey in $script:registryDriveRoots.Keys)
        {
            if ($script:registryDriveRoots[$registryDriveRootsKey] -ieq $RegistryDriveRoot)
            {
                $registryDriveName = $registryDriveRootsKey
                break
            }
        }
    }

    return $registryDriveName
}

<#
    .SYNOPSIS
        Retrieves the name of the registry drive at the root of the the specified registry key path.

    .PARAMETER RegistryKeyPath
        The registry key path to retrieve the registry drive name from.
#>
function Get-RegistryDriveName
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $RegistryKeyPath
    )

    $registryKeyPathRoot = Get-PathRoot -Path $RegistryKeyPath
    $registryKeyPathRoot = $registryKeyPathRoot.TrimEnd('\')

    if ($registryKeyPathRoot.Contains(':'))
    {
        $registryDriveName = $registryKeyPathRoot.TrimEnd(':')

        if (-not $script:registryDriveRoots.ContainsKey($registryDriveName))
        {
            New-InvalidArgumentException -ArgumentName 'Key' -Message ($script:localizedData.InvalidRegistryDrive -f $registryDriveName)
        }
    }
    else
    {
        $registryDriveName = ConvertTo-RegistryDriveName -RegistryDriveRoot $registryKeyPathRoot

        if ([System.String]::IsNullOrEmpty($registryDriveName))
        {
            New-InvalidArgumentException -ArgumentName 'Key' -Message ($script:localizedData.InvalidRegistryDrive -f $registryKeyPathRoot)
        }
    }

    return $registryDriveName
}

<#
    .SYNOPSIS
        Mounts the registry drive with the specified name.

    .PARAMETER RegistryKeyName
        The name of the registry drive to mount.
#>
function Mount-RegistryDrive
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $RegistryDriveName
    )

    $registryDriveInfo = Get-PSDrive -Name $RegistryDriveName -ErrorAction 'SilentlyContinue'

    if ($null -eq $registryDriveInfo)
    {
        $newPSDriveParameters = @{
            Name = $RegistryDriveName
            Root = $script:registryDriveRoots[$RegistryDriveName]
            PSProvider = 'Registry'
            Scope = 'Script'
        }

        $registryDriveInfo = New-PSDrive @newPSDriveParameters
    }

    # Validate that the specified PSDrive is valid
    if (($null -eq $registryDriveInfo) -or ($null -eq $registryDriveInfo.Provider) -or ($registryDriveInfo.Provider.Name -ine 'Registry'))
    {
        New-InvalidOperationException -Message ($script:localizedData.RegistryDriveCouldNotBeMounted -f $RegistryDriveName)
    }
}

<#
    .SYNOPSIS
        Opens the specified registry sub key under the specified registry parent key.
        This is a wrapper function for unit testing.

    .PARAMETER ParentKey
        The parent registry key which contains the sub key to open.

    .PARAMETER SubKey
        The sub key to open.

    .PARAMETER WriteAccessAllowed
        Specifies whether or not to open the sub key with permissions to write to it.
#>
function Open-RegistrySubKey
{
    [OutputType([Microsoft.Win32.RegistryKey])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $ParentKey,

        [Parameter(Mandatory = $true)]
        [System.String]
        [AllowEmptyString()]
        $SubKey,

        [Parameter()]
        [System.Management.Automation.SwitchParameter]
        $WriteAccessAllowed
    )

    return $ParentKey.OpenSubKey($SubKey, $WriteAccessAllowed)
}

<#
    .SYNOPSIS
        Opens and retrieves the registry key at the specified path.

    .PARAMETER RegistryKeyPath
        The path to the registry key to open.
        The path must include the registry drive.

    .PARAMETER WriteAccessAllowed
        Specifies whether or not to open the key with permissions to write to it.

    .NOTES
        This method is used instead of Get-Item so that there is no ambiguity between
        forward slashes as path separators vs literal characters in a key name
        (which is valid in the registry).

#>
function Get-RegistryKey
{
    [OutputType([Microsoft.Win32.RegistryKey])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $RegistryKeyPath,

        [Parameter()]
        [System.Management.Automation.SwitchParameter]
        $WriteAccessAllowed
    )

    # Parse the registry drive from the specified registry key path
    $registryDriveName = Get-RegistryDriveName -RegistryKeyPath $RegistryKeyPath

    # Mount the registry drive if needed
    Mount-RegistryDrive -RegistryDriveName $registryDriveName

    # Retrieve the registry drive key
    $registryDriveKey = Get-Item -LiteralPath ($registryDriveName + ':')

    # Parse the registry drive subkey from the specified registry key path
    $indexOfBackSlashInPath = $RegistryKeyPath.IndexOf('\')
    if ($indexOfBackSlashInPath -ge 0 -and $indexOfBackSlashInPath -lt ($RegistryKeyPath.Length - 1))
    {
        $registryDriveSubKey = $RegistryKeyPath.Substring($RegistryKeyPath.IndexOf('\') + 1)
    }
    else
    {
        $registryDriveSubKey = ''
    }

    # Open the registry drive subkey
    $registryKey = Open-RegistrySubKey -ParentKey $registryDriveKey -SubKey $registryDriveSubKey -WriteAccessAllowed:$WriteAccessAllowed

    # Return the opened registry key
    return $registryKey
}

<#
    .SYNOPSIS
        Retrieves the display name of the default registry key value if needed.

    .PARAMETER RegistryKeyValueName
        The name of the registry key value to retrieve the display name of.
#>
function Get-RegistryKeyValueDisplayName
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [AllowNull()]
        [AllowEmptyString()]
        [System.String]
        $RegistryKeyValueName
    )

    $registryKeyValueDisplayName = $RegistryKeyValueName

    if ([System.String]::IsNullOrEmpty($RegistryKeyValueName))
    {
        $registryKeyValueDisplayName = $script:localizedData.DefaultValueDisplayName
    }

    return $registryKeyValueDisplayName
}

<#
    .SYNOPSIS
        Retrieves the registry key value with the specified name from the specified registry key.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKey
        The registry key to retrieve the value from.

    .PARAMETER RegistryKeyValueName
        The name of the registry key value to retrieve.
#>
function Get-RegistryKeyValue
{
    [OutputType([System.Object[]])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [AllowEmptyString()]
        [System.String]
        $RegistryKeyValueName
    )

    $registryValueOptions = [Microsoft.Win32.RegistryValueOptions]::DoNotExpandEnvironmentNames
    $registryKeyValue = $RegistryKey.GetValue($RegistryKeyValueName, $null, $registryValueOptions)
    return ,$registryKeyValue
}

<#
    .SYNOPSIS
        Retrieves the type of the registry key value with the specified name from the the specified
        registry key.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKey
        The registry key to retrieve the type of the value from.

    .PARAMETER RegistryKeyValueName
        The name of the registry key value to retrieve the type of.
#>
function Get-RegistryKeyValueType
{
    [OutputType([System.Type])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [AllowEmptyString()]
        [System.String]
        $RegistryKeyValueName
    )

    return $RegistryKey.GetValueKind($RegistryKeyValueName)
}

<#
    .SYNOPSIS
        Converts the specified byte array to a hex string.

    .PARAMETER ByteArray
        The byte array to convert.
#>
function Convert-ByteArrayToHexString
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [AllowEmptyCollection()]
        [System.Object[]]
        $ByteArray
    )

    $hexString = ''

    foreach ($byte in $ByteArray)
    {
        $hexString += ('{0:x2}' -f $byte)
    }

    return $hexString
}

<#
    .SYNOPSIS
        Converts the specified registry key value to a readable string.

    .PARAMETER RegistryKeyValue
        The registry key value to convert.

    .PARAMETER RegistryKeyValueType
        The type of the registry key value to convert.
#>
function ConvertTo-ReadableString
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [AllowNull()]
        [AllowEmptyCollection()]
        [System.Object[]]
        $RegistryKeyValue,

        [Parameter(Mandatory = $true)]
        [ValidateSet('String', 'Binary', 'DWord', 'QWord', 'MultiString', 'ExpandString')]
        [System.String]
        $RegistryKeyValueType
    )

    $registryKeyValueAsString = [System.String]::Empty

    if ($null -ne $RegistryKeyValue)
    {
        # For Binary type data, convert the received bytes back to a readable hex-string
        if ($RegistryKeyValueType -eq 'Binary')
        {
            $RegistryKeyValue = Convert-ByteArrayToHexString -ByteArray $RegistryKeyValue
        }

        if ($RegistryKeyValueType -ne 'MultiString')
        {
            $RegistryKeyValue = [System.String[]] @() + $RegistryKeyValue
        }

        if ($RegistryKeyValue.Count -eq 1 -and -not [System.String]::IsNullOrEmpty($RegistryKeyValue[0]))
        {
            $registryKeyValueAsString = $RegistryKeyValue[0].ToString()
        }
        elseif ($RegistryKeyValue.Count -gt 1)
        {
            $registryKeyValueAsString = "($($RegistryKeyValue -join ', '))"
        }
    }

    return $registryKeyValueAsString
}

<#
    .SYNOPSIS
        Creates a new subkey with the specified name under the specified registry key.
        This is a wrapper function for unit testing.

    .PARAMETER ParentRegistryKey
        The parent registry key to create the new subkey under.

    .PARAMETER SubKeyName
        The name of the new subkey to create.
#>
function New-RegistrySubKey
{
    [OutputType([Microsoft.Win32.RegistryKey])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $ParentRegistryKey,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $SubKeyName
    )

    return $ParentRegistryKey.CreateSubKey($SubKeyName)
}

<#
    .SYNOPSIS
        Creates a new registry key at the specified registry key path.

    .PARAMETER RegistryKeyPath
        The path at which to create the registry key.
        This path must include the registry drive.
#>
function New-RegistryKey
{
    [OutputType([Microsoft.Win32.RegistryKey])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $RegistryKeyPath
    )

    # registry key names can contain forward slashes, so we can't use Split-Path here (it will split on /)
    $lastSep = $RegistryKeyPath.LastIndexOf('\')
    $parentRegistryKeyPath = $RegistryKeyPath.Substring(0, $lastSep)
    $newRegistryKeyName = $RegistryKeyPath.Substring($lastSep + 1)

    $parentRegistryKey = Get-RegistryKey -RegistryKeyPath $parentRegistryKeyPath -WriteAccessAllowed

    if ($null -eq $parentRegistryKey)
    {
        # If the parent registry key does not exist, create it
        $parentRegistryKey = New-RegistryKey -RegistryKeyPath $parentRegistryKeyPath
    }

    $newRegistryKey = New-RegistrySubKey -ParentRegistryKey $parentRegistryKey -SubKeyName $newRegistryKeyName

    return $newRegistryKey
}

<#
    .SYNOPSIS
        Retrieves the name of the specified registry key.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKey
        The registry key to retrieve the name of.
#>
function Get-RegistryKeyName
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey
    )

    return $RegistryKey.Name
}

<#
    .SYNOPSIS
        Converts the specified registry key value to a byte array for the Binary registry type.

    .PARAMETER RegistryKeyValue
        The registry key value to convert.
#>
function ConvertTo-Binary
{
    [OutputType([System.Byte[]])]
    [CmdletBinding()]
    param
    (
        [Parameter()]
        [AllowNull()]
        [AllowEmptyCollection()]
        [System.String[]]
        $RegistryKeyValue
    )

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -gt 1))
    {
        New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.ArrayNotAllowedForExpectedType -f 'Binary')
    }

    $binaryRegistryKeyValue = [System.Byte[]] @()

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -eq 1) -and (-not [System.String]::IsNullOrEmpty($RegistryKeyValue[0])))
    {
        $singleRegistryKeyValue = $RegistryKeyValue[0]

        if ($singleRegistryKeyValue.StartsWith('0x'))
        {
            $singleRegistryKeyValue = $singleRegistryKeyValue.Substring('0x'.Length)
        }

        if (($singleRegistryKeyValue.Length % 2) -ne 0)
        {
            $singleRegistryKeyValue = $singleRegistryKeyValue.PadLeft($singleRegistryKeyValue.Length + 1, '0')
        }

        try
        {
            for ($singleRegistryKeyValueIndex = 0 ; $singleRegistryKeyValueIndex -lt ($singleRegistryKeyValue.Length - 1) ; $singleRegistryKeyValueIndex = $singleRegistryKeyValueIndex + 2)
            {
                $binaryRegistryKeyValue += [System.Byte]::Parse($singleRegistryKeyValue.Substring($singleRegistryKeyValueIndex, 2), 'HexNumber')
            }
        }
        catch
        {
            New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.BinaryDataNotInHexFormat -f $singleRegistryKeyValue)
        }
    }

    return $binaryRegistryKeyValue
}

<#
    .SYNOPSIS
        Converts the specified registry key value to an Int32 for the DWord registry type.

    .PARAMETER RegistryKeyValue
        The registry key value to convert.
#>
function ConvertTo-DWord
{
    [OutputType([System.Int32])]
    [CmdletBinding()]
    param
    (
        [Parameter()]
        [AllowNull()]
        [AllowEmptyCollection()]
        [System.String[]]
        $RegistryKeyValue,

        [Parameter()]
        [System.Boolean]
        $Hex = $false
    )

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -gt 1))
    {
        New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.ArrayNotAllowedForExpectedType -f 'Dword')
    }

    $dwordRegistryKeyValue = [System.Int32] 0

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -eq 1) -and (-not [System.String]::IsNullOrEmpty($RegistryKeyValue[0])))
    {
        $singleRegistryKeyValue = $RegistryKeyValue[0]

        if ($Hex)
        {
            if ($singleRegistryKeyValue.StartsWith('0x'))
            {
                $singleRegistryKeyValue = $singleRegistryKeyValue.Substring('0x'.Length)
            }

            $currentCultureInfo = [System.Globalization.CultureInfo]::CurrentCulture
            $referenceValue = $null

            if ([System.Int32]::TryParse($singleRegistryKeyValue, 'HexNumber', $currentCultureInfo, [ref] $referenceValue))
            {
                $dwordRegistryKeyValue = $referenceValue
            }
            else
            {
                New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.DWordDataNotInHexFormat -f $singleRegistryKeyValue)
            }
        }
        else
        {
            $dwordRegistryKeyValue = [System.Int32]::Parse($singleRegistryKeyValue)
        }
    }

    return $dwordRegistryKeyValue
}

<#
    .SYNOPSIS
        Converts the specified registry key value to a string array for the MultiString registry type.

    .PARAMETER RegistryKeyValue
        The registry key value to convert.
#>
function ConvertTo-MultiString
{
    [OutputType([System.String[]])]
    [CmdletBinding()]
    param
    (
        [Parameter()]
        [AllowNull()]
        [AllowEmptyCollection()]
        [System.String[]]
        $RegistryKeyValue
    )

    $multiStringRegistryKeyValue = [System.String[]] @()

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Length -gt 0))
    {
        $multiStringRegistryKeyValue = [System.String[]] $RegistryKeyValue
    }

    return $multiStringRegistryKeyValue
}

<#
    .SYNOPSIS
        Converts the specified registry key value to an Int64 for the QWord registry type.

    .PARAMETER RegistryKeyValue
        The registry key value to convert.
#>
function ConvertTo-QWord
{
    [OutputType([System.Int64])]
    [CmdletBinding()]
    param
    (
        [Parameter()]
        [AllowNull()]
        [AllowEmptyCollection()]
        [System.String[]]
        $RegistryKeyValue,

        [Parameter()]
        [System.Boolean]
        $Hex = $false
    )

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -gt 1))
    {
        New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.ArrayNotAllowedForExpectedType -f 'Qword')
    }

    $qwordRegistryKeyValue = [System.Int64] 0

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -eq 1) -and (-not [System.String]::IsNullOrEmpty($RegistryKeyValue[0])))
    {
        $singleRegistryKeyValue = $RegistryKeyValue[0]

        if ($Hex)
        {
            if ($singleRegistryKeyValue.StartsWith('0x'))
            {
                $singleRegistryKeyValue = $singleRegistryKeyValue.Substring('0x'.Length)
            }

            $currentCultureInfo = [System.Globalization.CultureInfo]::CurrentCulture
            $referenceValue = $null

            if ([System.Int64]::TryParse($singleRegistryKeyValue, 'HexNumber', $currentCultureInfo, [ref] $referenceValue))
            {
                $qwordRegistryKeyValue = $referenceValue
            }
            else
            {
                New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.QWordDataNotInHexFormat -f $singleRegistryKeyValue)
            }
        }
        else
        {
            $qwordRegistryKeyValue = [System.Int64]::Parse($singleRegistryKeyValue)
        }
    }

    return $qwordRegistryKeyValue
}

<#
    .SYNOPSIS
        Converts the specified registry key value to a string for the String or ExpandString registry types.

    .PARAMETER RegistryKeyValue
        The registry key value to convert.
#>
function ConvertTo-String
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter()]
        [AllowNull()]
        [AllowEmptyCollection()]
        [System.String[]]
        $RegistryKeyValue
    )

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -gt 1))
    {
        New-InvalidArgumentException -ArgumentName 'ValueData' -Message ($script:localizedData.ArrayNotAllowedForExpectedType -f 'String or ExpandString')
    }

    $registryKeyValueAsString = [System.String]::Empty

    if (($null -ne $RegistryKeyValue) -and ($RegistryKeyValue.Count -eq 1))
    {
        $registryKeyValueAsString = [System.String] $RegistryKeyValue[0]
    }

    return $registryKeyValueAsString
}

<#
    .SYNOPSIS
        Sets the specified registry key value with the specified name to the specified value.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKeyName
        The name of the registry key that the value to set is under.

    .PARAMETER RegistryKeyValueName
        The name of the registry key value to set.

    .PARAMETER RegistryKeyValue
        The new value to set the registry key value to.

    .PARAMETER RegistryKeyValueType
        The type of the new value to set the registry key value to.
#>
function Set-RegistryKeyValue
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $RegistryKeyName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [AllowEmptyString()]
        [System.String]
        $RegistryKeyValueName,

        [Parameter(Mandatory = $true)]
        [AllowNull()]
        [System.Object]
        $RegistryKeyValue,

        [Parameter(Mandatory = $true)]
        [ValidateSet('String', 'Binary', 'DWord', 'QWord', 'MultiString', 'ExpandString')]
        [System.String]
        $ValueType
    )

    if ($ValueType -eq 'Binary')
    {
        $RegistryKeyValue = [System.Byte[]] $RegistryKeyValue
    }
    elseif ($ValueType -eq 'MultiString')
    {
        $RegistryKeyValue = [System.String[]] $RegistryKeyValue
    }

    $null = [Microsoft.Win32.Registry]::SetValue($RegistryKeyName, $RegistryKeyValueName, $RegistryKeyValue, $ValueType)
}

<#
    .SYNOPSIS
        Tests if the actual registry key value matches the expected registry key value.

    .PARAMETER ExpectedRegistryKeyValue
        The expected registry key value to test against.

    .PARAMETER ActualRegistryKeyValue
        The actual registry key value to test.

    .PARAMETER RegistryKeyValueType
        The type of the registry key values.
#>
function Test-RegistryKeyValuesMatch
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [AllowNull()]
        [System.Object]
        $ExpectedRegistryKeyValue,

        [Parameter(Mandatory = $true)]
        [AllowNull()]
        [System.Object]
        $ActualRegistryKeyValue,

        [Parameter(Mandatory = $true)]
        [ValidateSet('String', 'Binary', 'DWord', 'QWord', 'MultiString', 'ExpandString')]
        [System.String]
        $RegistryKeyValueType
    )

    $registryKeyValuesMatch = $true

    if ($RegistryKeyValueType -eq 'Multistring' -or $RegistryKeyValueType -eq 'Binary')
    {
        if ($null -eq $ExpectedRegistryKeyValue)
        {
            $ExpectedRegistryKeyValue = @()
        }

        if ($null -eq $ActualRegistryKeyValue)
        {
            $ActualRegistryKeyValue = @()
        }

        $registryKeyValuesMatch = $null -eq (Compare-Object -ReferenceObject $ExpectedRegistryKeyValue -DifferenceObject $ActualRegistryKeyValue)
    }
    else
    {
        if ($null -eq $ExpectedRegistryKeyValue)
        {
            $ExpectedRegistryKeyValue = ''
        }

        if ($null -eq $ActualRegistryKeyValue)
        {
            $ActualRegistryKeyValue = ''
        }

        $registryKeyValuesMatch = $ExpectedRegistryKeyValue -ieq $ActualRegistryKeyValue
    }

    return $registryKeyValuesMatch
}

<#
    .SYNOPSIS
        Removes the specified registry key and child subkeys recursively.

    .PARAMETER RegistryKey
        The registry key to remove.
#>
function Remove-RegistryKey
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey
    )

    $parentKeyName = Split-Path -Path $RegistryKey.Name -Parent
    $targetKeyName = Split-Path -Path $RegistryKey.Name -Leaf

    $parentRegistryKey = Get-RegistryKey -RegistryKeyPath $parentKeyName -WriteAccessAllowed

    $null = $parentRegistryKey.DeleteSubKeyTree($targetKeyName)
}

<#
    .SYNOPSIS
        Removes the specified value of the specified registry key.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKey
        The registry key to remove the default value of.
#>
function Remove-RegistryKeyValue
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [AllowEmptyString()]
        [System.String]
        $RegistryKeyValueName
    )

    $null = $RegistryKey.DeleteValue($RegistryKeyValueName)
}

<#
    .SYNOPSIS
        Removes the default value of the specified registry key.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKey
        The registry key to remove the default value of.
#>
function Remove-DefaultRegistryKeyValue
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey
    )

    $null = $RegistryKey.DeleteValue('')
}

<#
    .SYNOPSIS
        Retrieves the number of subkeys under the specified registry key.
        This is a wrapper function for unit testing.

    .PARAMETER RegistryKey
        The registry key to retrieve the subkeys of.
#>
function Get-RegistryKeySubKeyCount
{
    [OutputType([System.Int32])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [Microsoft.Win32.RegistryKey]
        $RegistryKey
    )

    return $RegistryKey.SubKeyCount
}

# SIG # Begin signature block
# MIIjYAYJKoZIhvcNAQcCoIIjUTCCI00CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCMHQmQO4IOt/FM
# r5S2CVpxKsBRpneXYhLQHJWwiN6AK6CCHVkwggUaMIIEAqADAgECAhADBbuGIbCh
# Y1+/3q4SBOdtMA0GCSqGSIb3DQEBCwUAMHIxCzAJBgNVBAYTAlVTMRUwEwYDVQQK
# EwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xMTAvBgNV
# BAMTKERpZ2lDZXJ0IFNIQTIgQXNzdXJlZCBJRCBDb2RlIFNpZ25pbmcgQ0EwHhcN
# MjAwNTEyMDAwMDAwWhcNMjMwNjA4MTIwMDAwWjBXMQswCQYDVQQGEwJVUzERMA8G
# A1UECBMIVmlyZ2luaWExDzANBgNVBAcTBlZpZW5uYTERMA8GA1UEChMIZGJhdG9v
# bHMxETAPBgNVBAMTCGRiYXRvb2xzMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
# CgKCAQEAvL9je6vjv74IAbaY5rXqHxaNeNJO9yV0ObDg+kC844Io2vrHKGD8U5hU
# iJp6rY32RVprnAFrA4jFVa6P+sho7F5iSVAO6A+QZTHQCn7oquOefGATo43NAadz
# W2OWRro3QprMPZah0QFYpej9WaQL9w/08lVaugIw7CWPsa0S/YjHPGKQ+bYgI/kr
# EUrk+asD7lvNwckR6pGieWAyf0fNmSoevQBTV6Cd8QiUfj+/qWvLW3UoEX9ucOGX
# 2D8vSJxL7JyEVWTHg447hr6q9PzGq+91CO/c9DWFvNMjf+1c5a71fEZ54h1mNom/
# XoWZYoKeWhKnVdv1xVT1eEimibPEfQIDAQABo4IBxTCCAcEwHwYDVR0jBBgwFoAU
# WsS5eyoKo6XqcQPAYPkt9mV1DlgwHQYDVR0OBBYEFPDAoPu2A4BDTvsJ193ferHL
# 454iMA4GA1UdDwEB/wQEAwIHgDATBgNVHSUEDDAKBggrBgEFBQcDAzB3BgNVHR8E
# cDBuMDWgM6Axhi9odHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc2hhMi1hc3N1cmVk
# LWNzLWcxLmNybDA1oDOgMYYvaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NoYTIt
# YXNzdXJlZC1jcy1nMS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAwEwKjAoBggr
# BgEFBQcCARYcaHR0cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBBAEw
# gYQGCCsGAQUFBwEBBHgwdjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNl
# cnQuY29tME4GCCsGAQUFBzAChkJodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20v
# RGlnaUNlcnRTSEEyQXNzdXJlZElEQ29kZVNpZ25pbmdDQS5jcnQwDAYDVR0TAQH/
# BAIwADANBgkqhkiG9w0BAQsFAAOCAQEAj835cJUMH9Y2pBKspjznNJwcYmOxeBcH
# Ji+yK0y4bm+j44OGWH4gu/QJM+WjZajvkydJKoJZH5zrHI3ykM8w8HGbYS1WZfN4
# oMwi51jKPGZPw9neGS2PXrBcKjzb7rlQ6x74Iex+gyf8z1ZuRDitLJY09FEOh0BM
# LaLh+UvJ66ghmfIyjP/g3iZZvqwgBhn+01fObqrAJ+SagxJ/21xNQJchtUOWIlxR
# kuUn9KkuDYrMO70a2ekHODcAbcuHAGI8wzw4saK1iPPhVTlFijHS+7VfIt/d/18p
# MLHHArLQQqe1Z0mTfuL4M4xCUKpebkH8rI3Fva62/6osaXLD0ymERzCCBTAwggQY
# oAMCAQICEAQJGBtf1btmdVNDtW+VUAgwDQYJKoZIhvcNAQELBQAwZTELMAkGA1UE
# BhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2lj
# ZXJ0LmNvbTEkMCIGA1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4X
# DTEzMTAyMjEyMDAwMFoXDTI4MTAyMjEyMDAwMFowcjELMAkGA1UEBhMCVVMxFTAT
# BgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEx
# MC8GA1UEAxMoRGlnaUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBD
# QTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPjTsxx/DhGvZ3cH0wsx
# SRnP0PtFmbE620T1f+Wondsy13Hqdp0FLreP+pJDwKX5idQ3Gde2qvCchqXYJawO
# eSg6funRZ9PG+yknx9N7I5TkkSOWkHeC+aGEI2YSVDNQdLEoJrskacLCUvIUZ4qJ
# RdQtoaPpiCwgla4cSocI3wz14k1gGL6qxLKucDFmM3E+rHCiq85/6XzLkqHlOzEc
# z+ryCuRXu0q16XTmK/5sy350OTYNkO/ktU6kqepqCquE86xnTrXE94zRICUj6whk
# PlKWwfIPEvTFjg/BougsUfdzvL2FsWKDc0GCB+Q4i2pzINAPZHM8np+mM6n9Gd8l
# k9ECAwEAAaOCAc0wggHJMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYDVR0PAQH/BAQD
# AgGGMBMGA1UdJQQMMAoGCCsGAQUFBwMDMHkGCCsGAQUFBwEBBG0wazAkBggrBgEF
# BQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAChjdodHRw
# Oi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0Eu
# Y3J0MIGBBgNVHR8EejB4MDqgOKA2hjRodHRwOi8vY3JsNC5kaWdpY2VydC5jb20v
# RGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsMDqgOKA2hjRodHRwOi8vY3JsMy5k
# aWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsME8GA1UdIARI
# MEYwOAYKYIZIAYb9bAACBDAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdp
# Y2VydC5jb20vQ1BTMAoGCGCGSAGG/WwDMB0GA1UdDgQWBBRaxLl7KgqjpepxA8Bg
# +S32ZXUOWDAfBgNVHSMEGDAWgBRF66Kv9JLLgjEtUYunpyGd823IDzANBgkqhkiG
# 9w0BAQsFAAOCAQEAPuwNWiSz8yLRFcgsfCUpdqgdXRwtOhrE7zBh134LYP3DPQ/E
# r4v97yrfIFU3sOH20ZJ1D1G0bqWOWuJeJIFOEKTuP3GOYw4TS63XX0R58zYUBor3
# nEZOXP+QsRsHDpEV+7qvtVHCjSSuJMbHJyqhKSgaOnEoAjwukaPAJRHinBRHoXpo
# aK+bp1wgXNlxsQyPu6j4xRJon89Ay0BEpRPw5mQMJQhCMrI2iiQC/i9yfhzXSUWW
# 6Fkd6fp0ZGuy62ZD2rOwjNXpDd32ASDOmTFjPQgaGLOBm0/GkxAG/AeB+ova+YJJ
# 92JuoVP6EpQYhS6SkepobEQysmah5xikmmRR7zCCBY0wggR1oAMCAQICEA6bGI75
# 0C3n79tQ4ghAGFowDQYJKoZIhvcNAQEMBQAwZTELMAkGA1UEBhMCVVMxFTATBgNV
# BAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEkMCIG
# A1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4XDTIyMDgwMTAwMDAw
# MFoXDTMxMTEwOTIzNTk1OVowYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGln
# aUNlcnQgVHJ1c3RlZCBSb290IEc0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAv+aQc2jeu+RdSjwwIjBpM+zCpyUuySE98orYWcLhKac9WKt2ms2uexuE
# DcQwH/MbpDgW61bGl20dq7J58soR0uRf1gU8Ug9SH8aeFaV+vp+pVxZZVXKvaJNw
# wrK6dZlqczKU0RBEEC7fgvMHhOZ0O21x4i0MG+4g1ckgHWMpLc7sXk7Ik/ghYZs0
# 6wXGXuxbGrzryc/NrDRAX7F6Zu53yEioZldXn1RYjgwrt0+nMNlW7sp7XeOtyU9e
# 5TXnMcvak17cjo+A2raRmECQecN4x7axxLVqGDgDEI3Y1DekLgV9iPWCPhCRcKtV
# gkEy19sEcypukQF8IUzUvK4bA3VdeGbZOjFEmjNAvwjXWkmkwuapoGfdpCe8oU85
# tRFYF/ckXEaPZPfBaYh2mHY9WV1CdoeJl2l6SPDgohIbZpp0yt5LHucOY67m1O+S
# kjqePdwA5EUlibaaRBkrfsCUtNJhbesz2cXfSwQAzH0clcOP9yGyshG3u3/y1Yxw
# LEFgqrFjGESVGnZifvaAsPvoZKYz0YkH4b235kOkGLimdwHhD5QMIR2yVCkliWzl
# DlJRR3S+Jqy2QXXeeqxfjT/JvNNBERJb5RBQ6zHFynIWIgnffEx1P2PsIV/EIFFr
# b7GrhotPwtZFX50g/KEexcCPorF+CiaZ9eRpL5gdLfXZqbId5RsCAwEAAaOCATow
# ggE2MA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFOzX44LScV1kTN8uZz/nupiu
# HA9PMB8GA1UdIwQYMBaAFEXroq/0ksuCMS1Ri6enIZ3zbcgPMA4GA1UdDwEB/wQE
# AwIBhjB5BggrBgEFBQcBAQRtMGswJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRp
# Z2ljZXJ0LmNvbTBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQu
# Y29tL0RpZ2lDZXJ0QXNzdXJlZElEUm9vdENBLmNydDBFBgNVHR8EPjA8MDqgOKA2
# hjRodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290
# Q0EuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQwFAAOCAQEAcKC/
# Q1xV5zhfoKN0Gz22Ftf3v1cHvZqsoYcs7IVeqRq7IviHGmlUIu2kiHdtvRoU9BNK
# ei8ttzjv9P+Aufih9/Jy3iS8UgPITtAq3votVs/59PesMHqai7Je1M/RQ0SbQyHr
# lnKhSLSZy51PpwYDE3cnRNTnf+hZqPC/Lwum6fI0POz3A8eHqNJMQBk1RmppVLC4
# oVaO7KTVPeix3P0c2PR3WlxUjG/voVA9/HYJaISfb8rbII01YBwCA8sgsKxYoA5A
# Y8WYIsGyWfVVa88nq2x2zm8jLfR+cWojayL/ErhULSd+2DrZ8LaHlv1b0VysGMNN
# n3O3AamfV6peKOK5lDCCBq4wggSWoAMCAQICEAc2N7ckVHzYR6z9KGYqXlswDQYJ
# KoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IElu
# YzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGlnaUNlcnQg
# VHJ1c3RlZCBSb290IEc0MB4XDTIyMDMyMzAwMDAwMFoXDTM3MDMyMjIzNTk1OVow
# YzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQD
# EzJEaWdpQ2VydCBUcnVzdGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGlu
# ZyBDQTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMaGNQZJs8E9cklR
# VcclA8TykTepl1Gh1tKD0Z5Mom2gsMyD+Vr2EaFEFUJfpIjzaPp985yJC3+dH54P
# Mx9QEwsmc5Zt+FeoAn39Q7SE2hHxc7Gz7iuAhIoiGN/r2j3EF3+rGSs+QtxnjupR
# PfDWVtTnKC3r07G1decfBmWNlCnT2exp39mQh0YAe9tEQYncfGpXevA3eZ9drMvo
# hGS0UvJ2R/dhgxndX7RUCyFobjchu0CsX7LeSn3O9TkSZ+8OpWNs5KbFHc02DVzV
# 5huowWR0QKfAcsW6Th+xtVhNef7Xj3OTrCw54qVI1vCwMROpVymWJy71h6aPTnYV
# VSZwmCZ/oBpHIEPjQ2OAe3VuJyWQmDo4EbP29p7mO1vsgd4iFNmCKseSv6De4z6i
# c/rnH1pslPJSlRErWHRAKKtzQ87fSqEcazjFKfPKqpZzQmiftkaznTqj1QPgv/Ci
# PMpC3BhIfxQ0z9JMq++bPf4OuGQq+nUoJEHtQr8FnGZJUlD0UfM2SU2LINIsVzV5
# K6jzRWC8I41Y99xh3pP+OcD5sjClTNfpmEpYPtMDiP6zj9NeS3YSUZPJjAw7W4oi
# qMEmCPkUEBIDfV8ju2TjY+Cm4T72wnSyPx4JduyrXUZ14mCjWAkBKAAOhFTuzuld
# yF4wEr1GnrXTdrnSDmuZDNIztM2xAgMBAAGjggFdMIIBWTASBgNVHRMBAf8ECDAG
# AQH/AgEAMB0GA1UdDgQWBBS6FtltTYUvcyl2mi91jGogj57IbzAfBgNVHSMEGDAW
# gBTs1+OC0nFdZEzfLmc/57qYrhwPTzAOBgNVHQ8BAf8EBAMCAYYwEwYDVR0lBAww
# CgYIKwYBBQUHAwgwdwYIKwYBBQUHAQEEazBpMCQGCCsGAQUFBzABhhhodHRwOi8v
# b2NzcC5kaWdpY2VydC5jb20wQQYIKwYBBQUHMAKGNWh0dHA6Ly9jYWNlcnRzLmRp
# Z2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRSb290RzQuY3J0MEMGA1UdHwQ8MDow
# OKA2oDSGMmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRS
# b290RzQuY3JsMCAGA1UdIAQZMBcwCAYGZ4EMAQQCMAsGCWCGSAGG/WwHATANBgkq
# hkiG9w0BAQsFAAOCAgEAfVmOwJO2b5ipRCIBfmbW2CFC4bAYLhBNE88wU86/GPvH
# UF3iSyn7cIoNqilp/GnBzx0H6T5gyNgL5Vxb122H+oQgJTQxZ822EpZvxFBMYh0M
# CIKoFr2pVs8Vc40BIiXOlWk/R3f7cnQU1/+rT4osequFzUNf7WC2qk+RZp4snuCK
# rOX9jLxkJodskr2dfNBwCnzvqLx1T7pa96kQsl3p/yhUifDVinF2ZdrM8HKjI/rA
# J4JErpknG6skHibBt94q6/aesXmZgaNWhqsKRcnfxI2g55j7+6adcq/Ex8HBanHZ
# xhOACcS2n82HhyS7T6NJuXdmkfFynOlLAlKnN36TU6w7HQhJD5TNOXrd/yVjmScs
# PT9rp/Fmw0HNT7ZAmyEhQNC3EyTN3B14OuSereU0cZLXJmvkOHOrpgFPvT87eK1M
# rfvElXvtCl8zOYdBeHo46Zzh3SP9HSjTx/no8Zhf+yvYfvJGnXUsHicsJttvFXse
# GYs2uJPU5vIXmVnKcPA3v5gA3yAWTyf7YGcWoWa63VXAOimGsJigK+2VQbc61RWY
# MbRiCQ8KvYHZE/6/pNHzV9m8BPqC3jLfBInwAM1dwvnQI38AC+R2AibZ8GV2QqYp
# hwlHK+Z/GqSFD/yYlvZVVCsfgPrA8g4r5db7qS9EFUrnEw4d2zc4GqEr9u3WfPww
# ggbAMIIEqKADAgECAhAMTWlyS5T6PCpKPSkHgD1aMA0GCSqGSIb3DQEBCwUAMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwHhcNMjIwOTIxMDAwMDAwWhcNMzMxMTIxMjM1OTU5WjBGMQswCQYDVQQGEwJV
# UzERMA8GA1UEChMIRGlnaUNlcnQxJDAiBgNVBAMTG0RpZ2lDZXJ0IFRpbWVzdGFt
# cCAyMDIyIC0gMjCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAM/spSY6
# xqnya7uNwQ2a26HoFIV0MxomrNAcVR4eNm28klUMYfSdCXc9FZYIL2tkpP0GgxbX
# kZI4HDEClvtysZc6Va8z7GGK6aYo25BjXL2JU+A6LYyHQq4mpOS7eHi5ehbhVsbA
# umRTuyoW51BIu4hpDIjG8b7gL307scpTjUCDHufLckkoHkyAHoVW54Xt8mG8qjoH
# ffarbuVm3eJc9S/tjdRNlYRo44DLannR0hCRRinrPibytIzNTLlmyLuqUDgN5YyU
# XRlav/V7QG5vFqianJVHhoV5PgxeZowaCiS+nKrSnLb3T254xCg/oxwPUAY3ugjZ
# Naa1Htp4WB056PhMkRCWfk3h3cKtpX74LRsf7CtGGKMZ9jn39cFPcS6JAxGiS7uY
# v/pP5Hs27wZE5FX/NurlfDHn88JSxOYWe1p+pSVz28BqmSEtY+VZ9U0vkB8nt9Kr
# FOU4ZodRCGv7U0M50GT6Vs/g9ArmFG1keLuY/ZTDcyHzL8IuINeBrNPxB9Thvdld
# S24xlCmL5kGkZZTAWOXlLimQprdhZPrZIGwYUWC6poEPCSVT8b876asHDmoHOWIZ
# ydaFfxPZjXnPYsXs4Xu5zGcTB5rBeO3GiMiwbjJ5xwtZg43G7vUsfHuOy2SJ8bHE
# uOdTXl9V0n0ZKVkDTvpd6kVzHIR+187i1Dp3AgMBAAGjggGLMIIBhzAOBgNVHQ8B
# Af8EBAMCB4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAg
# BgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZ
# bU2FL3MpdpovdYxqII+eyG8wHQYDVR0OBBYEFGKK3tBh/I8xFO2XC809KpQU31Kc
# MFoGA1UdHwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdp
# Q2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAG
# CCsGAQUFBwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2Vy
# dC5jb20wWAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9E
# aWdpQ2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQw
# DQYJKoZIhvcNAQELBQADggIBAFWqKhrzRvN4Vzcw/HXjT9aFI/H8+ZU5myXm93KK
# mMN31GT8Ffs2wklRLHiIY1UJRjkA/GnUypsp+6M/wMkAmxMdsJiJ3HjyzXyFzVOd
# r2LiYWajFCpFh0qYQitQ/Bu1nggwCfrkLdcJiXn5CeaIzn0buGqim8FTYAnoo7id
# 160fHLjsmEHw9g6A++T/350Qp+sAul9Kjxo6UrTqvwlJFTU2WZoPVNKyG39+Xgmt
# dlSKdG3K0gVnK3br/5iyJpU4GYhEFOUKWaJr5yI+RCHSPxzAm+18SLLYkgyRTzxm
# lK9dAlPrnuKe5NMfhgFknADC6Vp0dQ094XmIvxwBl8kZI4DXNlpflhaxYwzGRkA7
# zl011Fk+Q5oYrsPJy8P7mxNfarXH4PMFw1nfJ2Ir3kHJU7n/NBBn9iYymHv+XEKU
# gZSCnawKi8ZLFUrTmJBFYDOA4CPe+AOk9kVH5c64A0JH6EE2cXet/aLol3ROLtoe
# HYxayB6a1cLwxiKoT5u92ByaUcQvmvZfpyeXupYuhVfAYOd4Vn9q78KVmksRAsiC
# nMkaBXy6cbVOepls9Oie1FqYyJ+/jbsYXEP10Cro4mLueATbvdH7WwqocH7wl4R4
# 4wgDXUcsY6glOJcB0j862uXl9uab3H4szP8XTE0AotjWAQ64i+7m4HJViSwnGWH2
# dwGMMYIFXTCCBVkCAQEwgYYwcjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTExMC8GA1UEAxMoRGln
# aUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBDQQIQAwW7hiGwoWNf
# v96uEgTnbTANBglghkgBZQMEAgEFAKCBhDAYBgorBgEEAYI3AgEMMQowCKACgACh
# AoAAMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAM
# BgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCCKTEngUVwbsyFfdddHoPQR1SS0
# puklhahoITfIEZXzEzANBgkqhkiG9w0BAQEFAASCAQAVGw0u0WK33UxpWd9Rll2N
# 4QK2ny50+QKFNp0W2JxOiorHgNG3yqexW5Tkpr31HGi/kELWfxb+Q/egRER2fp4X
# NiRgOjmBmdg0+iZLrhUc+Ihzp+wA9R27QfIjoIct++/WmvRbFFQ2x46h/2DfES56
# Z/5cRzut9kGsKrcYMaxLtj8EfOmINNImPd4JMwi8gJq3aEcO0Lcw5JQOaUD+7i/i
# h2itwibt1981JiCdhif3N6Y/nwi5rT0SuI2gKvORXpF0NvW/5SGwl8dJmrXDX9q6
# 4+2HUaFCRqlqNYGt2VmpFTxj+tCQv7aRQ6IPvn1ApiRZxkCOmnu2F6Y3Z+Ec3ySw
# oYIDIDCCAxwGCSqGSIb3DQEJBjGCAw0wggMJAgEBMHcwYzELMAkGA1UEBhMCVVMx
# FzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQDEzJEaWdpQ2VydCBUcnVz
# dGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGluZyBDQQIQDE1pckuU+jwq
# Sj0pB4A9WjANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0B
# BwEwHAYJKoZIhvcNAQkFMQ8XDTIzMDUxMjAwMjUyN1owLwYJKoZIhvcNAQkEMSIE
# IEnmjwYZNVWc2nmmrE7XMX+gbQDN3gl2JKBQFMFVuYqLMA0GCSqGSIb3DQEBAQUA
# BIICAEOmBqrwcVODVlqLwB50stZkXSlfHNzoZIiFsJ5y6MUKBW15v9uNo0PjKKkQ
# zo/VVS6LXZSbsxAec0jT9YL72Hw7xmpdOdMoJ/uunkXZeNfMt3foNE+VmYYARvVg
# cWW6kCZDc1obzSrMHEQtyNzSj8QrESjETVyvfu0JQqAxUbLrH8AEJ6uFQ5ftJj0i
# maZpMEQupsZfKsQY5s1wYoy+25R5NMjHie8rhVcvBSCwbRdbf2/Y26rjGzIPU+q5
# WU78rqF0+JSB3FcWxixH+HUMJ+mTC5EytkOSmHQDAUd4nhEvIUSYtGmIOpwn9za3
# mLB7LEx3JfuHJf0aaLLbKe4Rruez0bLmuPuj+DNe1/GGEerJ8hodWNvcAKWeqDMm
# CiRYfwpiy0Qhl14muJV42Q0ypgp0SXKzVdrhvGNDgVyxeSuOMlw6/i7G1/pz4WIZ
# 2SghsUwbYyDCgz1HgoalJHC9Ee+I4kv3Q9ZJVqEgoCUejY9S016b112pQiEXcQc6
# 922HtUoUFPbAh58vHEJR12j2a5Vo+k71tcBEvH2R0DvmhZi66qt4am0zagviE87P
# cbI1zWZBx6uymjsAgL8Oiz2y/LubPlQG/FWysRwVpstkerLRn54pXijmwTxvJhXr
# TYNvMwUKH1XHn6vmZuzq5ME7/DuOJj4yCO/b5XkEBU0gTddG
# SIG # End signature block
