using System.Net.Http;
using Newtonsoft.Json.Linq;

namespace WindowsUpdateTool
{
    /// <summary>
    /// Simple test class to verify API functionality
    /// </summary>
    public static class ApiTest
    {
        public static async Task TestUupDumpApi()
        {
            try
            {
                using var client = new HttpClient();
                var url = "https://api.uupdump.net/listid.php?search=windows+11&ring=retail&arch=x64&latest=true";
                
                Console.WriteLine($"Testing API: {url}");
                var response = await client.GetStringAsync(url);
                
                Console.WriteLine("API Response received successfully!");
                Console.WriteLine($"Response length: {response.Length} characters");
                
                var data = JObject.Parse(response);
                var builds = data["response"]?["builds"];
                
                if (builds != null)
                {
                    int count = 0;
                    foreach (var build in builds)
                    {
                        count++;
                    }
                    Console.WriteLine($"Found {count} builds in response");
                }
                else
                {
                    Console.WriteLine("No builds found in response");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API Test failed: {ex.Message}");
            }
        }
    }
} 