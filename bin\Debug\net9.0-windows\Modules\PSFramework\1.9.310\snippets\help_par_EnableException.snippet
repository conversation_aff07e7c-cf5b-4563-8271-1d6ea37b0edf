<CodeSnippets xmlns="http://schemas.microsoft.com/VisualStudio/2005/CodeSnippet">
	<CodeSnippet Format="1.0.0">
		<Header>
			<Title>help_par_EnableException</Title>
			<Shortcut></Shortcut>
			<Description>Help for the EnableException parameter</Description>
			<Author>Infernal Associates Ltd.</Author>
			<SnippetTypes>
				<SnippetType>Expansion</SnippetType>
			</SnippetTypes>
		</Header>
		<Snippet>
			<Code Language="PowerShell">
				<![CDATA[.PARAMETER EnableException
	This parameters disables user-friendly warnings and enables the throwing of exceptions.
	This is less user friendly, but allows catching exceptions in calling scripts.]]>
			</Code>
		</Snippet>
	</CodeSnippet>
</CodeSnippets>