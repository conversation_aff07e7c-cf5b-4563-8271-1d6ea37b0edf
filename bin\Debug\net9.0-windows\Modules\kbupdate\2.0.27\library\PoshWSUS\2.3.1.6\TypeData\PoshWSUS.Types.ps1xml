﻿<Types>
	<Type>
    <Name>Microsoft.UpdateServices.Internal.BaseApi.Update</Name>
    <Members>
      <ScriptProperty>
        <Name>UpdateID</Name>
          <GetScriptBlock>
            $this.ID.UpdateID.GUID
          </GetScriptBlock>
      </ScriptProperty>
    </Members>
  </Type>
	<Type>
    <Name>Microsoft.UpdateServices.Internal.BaseApi.ComputerTargetGroup</Name>
    <Members>
      <ScriptProperty>
        <Name>WSUSServer</Name>
        <GetScriptBlock>
          $this.UpdateServer.Name
        </GetScriptBlock>		  
      </ScriptProperty>
	  <ScriptProperty>
		<Name>ParentGroup</Name>
			<GetScriptBlock>
				$this.GetParentTargetGroup().Name
			</GetScriptBlock>
	  </ScriptProperty>		 
    </Members>
  </Type>
	<Type>
    <Name>Microsoft.UpdateServices.Internal.BaseApi.ComputerTarget</Name>
    <Members>
      <ScriptProperty>
        <Name>ComputerGroup</Name>
        <GetScriptBlock>
          $This.GetComputerTargetGroups() | Select -Expand Name
        </GetScriptBlock>
      </ScriptProperty>
    </Members>
  </Type>
	<Type>
    <Name>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Update</Name>
    <Members>
        <AliasProperty>
            <Name>
                Installed                
            </Name>
            <ReferencedMemberName>
                InstalledCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Downloaded                
            </Name>
            <ReferencedMemberName>
                DownloadedCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Failed                
            </Name>
            <ReferencedMemberName>
                FailedCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                PendingReboot                
            </Name>
            <ReferencedMemberName>
                InstalledPendingRebootCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                NotApplicable                
            </Name>
            <ReferencedMemberName>
                NotApplicableCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                NotInstalled                
            </Name>
            <ReferencedMemberName>
                NotInstalledCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Unknown                
            </Name>
            <ReferencedMemberName>
                UnknownCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Needed                
            </Name>
            <ReferencedMemberName>
                NeededCount
            </ReferencedMemberName>
        </AliasProperty>
		<ScriptProperty>
			<Name>UpdateTitle</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetUpdate([guid]$This.UpdateId) | Select -Expand Title
			</GetScriptBlock>
		</ScriptProperty>
		<ScriptProperty>
			<Name>UpdateKB</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetUpdate([guid]$This.UpdateId) | Select -Expand KnowledgebaseArticles
			</GetScriptBlock>
		</ScriptProperty>
		<ScriptProperty>
			<Name>NeededCount</Name>
			<GetScriptBlock>
				($This.DownloadedCount + $This.NotInstalledCount)
			</GetScriptBlock>
		</ScriptProperty>		
	</Members>
  </Type>
	<Type>
		<Name>Microsoft.UpdateServices.Internal.BaseApi.UpdateInstallationInfo</Name>
		<Members>
			<ScriptProperty>
				<Name>UpdateTitle</Name>
				<GetScriptBlock>
					$This.GetUpdate().Title
				</GetScriptBlock>
			</ScriptProperty>
			<ScriptProperty>
				<Name>UpdateKB</Name>
				<GetScriptBlock>
					$This.GetUpdate().KnowledgeBaseArticles
				</GetScriptBlock>
			</ScriptProperty>
			<ScriptProperty>
				<Name>Computername</Name>
				<GetScriptBlock>
					$This.GetComputerTarget().FullDomainName
				</GetScriptBlock>
			</ScriptProperty>
			<ScriptProperty>
				<Name>UpdateServerName</Name>
				<GetScriptBlock>
					$wsus = Get-PSWSUSServer
					$wsus.Name
				</GetScriptBlock>
			</ScriptProperty>
		</Members>
	</Type>
	<Type>
		<Name>Microsoft.UpdateServices.Internal.BaseApi.UpdateApproval</Name>
		<Members>
			<ScriptProperty>
				<Name>UpdateTitle</Name>
				<GetScriptBlock>
					$This.GetUpdate().Title
				</GetScriptBlock>
			</ScriptProperty>
			<ScriptProperty>
				<Name>UpdateKB</Name>
				<GetScriptBlock>
					$This.GetUpdate().KnowledgeBaseArticles
				</GetScriptBlock>
			</ScriptProperty>
			<ScriptProperty>
				<Name>TargetGroup</Name>
				<GetScriptBlock>
					$This.GetComputerTargetGroup().Name
				</GetScriptBlock>
			</ScriptProperty>
			<ScriptProperty>
				<Name>UpdateServerName</Name>
				<GetScriptBlock>
					$wsus = Get-PSWSUSServer
					$wsus.Name
				</GetScriptBlock>
			</ScriptProperty>
		</Members>
	</Type>	
	<Type>
    <Name>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Group</Name>
    <Members>
        <AliasProperty>
            <Name>
                Installed                
            </Name>
            <ReferencedMemberName>
                InstalledCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Downloaded                
            </Name>
            <ReferencedMemberName>
                DownloadedCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Failed                
            </Name>
            <ReferencedMemberName>
                FailedCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                PendingReboot                
            </Name>
            <ReferencedMemberName>
                InstalledPendingRebootCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                NotApplicable                
            </Name>
            <ReferencedMemberName>
                NotApplicableCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                NotInstalled                
            </Name>
            <ReferencedMemberName>
                NotInstalledCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Unknown                
            </Name>
            <ReferencedMemberName>
                UnknownCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Needed                
            </Name>
            <ReferencedMemberName>
                NeededCount
            </ReferencedMemberName>
        </AliasProperty>
      <ScriptProperty>
        <Name>ComputerGroup</Name>
        <GetScriptBlock>
          $wsus = Get-PSWSUSServer
          $Wsus.GetComputerTargetGroup([guid]$This.ComputerTargetGroupId).Name
        </GetScriptBlock>
      </ScriptProperty>
		<ScriptProperty>
			<Name>UpdateTitle</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetUpdate([guid]$This.UpdateId) | Select -Expand Title
			</GetScriptBlock>
		</ScriptProperty>
		<ScriptProperty>
			<Name>UpdateKB</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetUpdate([guid]$This.UpdateId) | Select -Expand KnowledgebaseArticles
			</GetScriptBlock>
		</ScriptProperty>
		<ScriptProperty>
			<Name>NeededCount</Name>
			<GetScriptBlock>
				($This.DownloadedCount + $This.NotInstalledCount)
			</GetScriptBlock>
		</ScriptProperty>		
	</Members>
  </Type>
  	<Type>
    <Name>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Client</Name>
    <Members>
        <AliasProperty>
            <Name>
                Installed                
            </Name>
            <ReferencedMemberName>
                InstalledCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Downloaded                
            </Name>
            <ReferencedMemberName>
                DownloadedCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Failed                
            </Name>
            <ReferencedMemberName>
                FailedCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                PendingReboot                
            </Name>
            <ReferencedMemberName>
                InstalledPendingRebootCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                NotApplicable                
            </Name>
            <ReferencedMemberName>
                NotApplicableCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                NotInstalled                
            </Name>
            <ReferencedMemberName>
                NotInstalledCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Unknown                
            </Name>
            <ReferencedMemberName>
                UnknownCount
            </ReferencedMemberName>
        </AliasProperty>
        <AliasProperty>
            <Name>
                Needed                
            </Name>
            <ReferencedMemberName>
                NeededCount
            </ReferencedMemberName>
        </AliasProperty>
		<ScriptProperty>
			<Name>Computer</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetComputerTarget($This.ComputerTargetID) | Select -Expand FullDomainName
			</GetScriptBlock>
		</ScriptProperty>
		<ScriptProperty>
			<Name>NeededCount</Name>
			<GetScriptBlock>
				($This.DownloadedCount + $This.NotInstalledCount)
			</GetScriptBlock>
		</ScriptProperty>		
	</Members>
  </Type>
	<Type>
    <Name>Microsoft.UpdateServices.Administrator.UpdateRevisionID</Name>
    <Members>
		<ScriptProperty>
			<Name>UpdateTitle</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetUpdate([guid]$This.UpdateId) | Select -Expand Title
			</GetScriptBlock>
		</ScriptProperty>
		<ScriptProperty>
			<Name>UpdateKB</Name>
			<GetScriptBlock>
				$wsus = Get-PSWSUSServer
				$wsus.GetUpdate([guid]$This.UpdateId) | Select -Expand KnowledgebaseArticles
			</GetScriptBlock>
		</ScriptProperty>	
	</Members>
  </Type>
</Types>