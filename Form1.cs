using System.Net.Http;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using System.IO.Compression;
using System.Diagnostics;

namespace WindowsUpdateTool
{
    public partial class Form1 : Form
    {
        private readonly HttpClient _httpClient;
        private List<UpdateFilter> _filters = new();
        private const string FILTER_FILE = "update_filters.json";

        public Form1()
        {
            InitializeComponent();
            _httpClient = new HttpClient();
            InitializeFilterControls();
            InitializeDownloadControls();
            LoadFilters();
        }

        private void InitializeDownloadControls()
        {
            // Add download button
            var btnDownload = new Button
            {
                Text = "Download Selected",
                Location = new Point(520, 52),
                Size = new Size(120, 25)
            };
            btnDownload.Click += BtnDownload_Click;

            // Add test button
            var btnTestDownload = new Button
            {
                Text = "Test Download URL",
                Location = new Point(650, 52),
                Size = new Size(120, 25)
            };
            btnTestDownload.Click += BtnTestDownload_Click;

            // Add install updates button
            var btnConvertToIso = new Button
            {
                Text = "Install Updates",
                Location = new Point(780, 52),
                Size = new Size(120, 25)
            };
            btnConvertToIso.Click += BtnConvertToIso_Click;

            panel1.Controls.Add(btnDownload);
            panel1.Controls.Add(btnTestDownload);
            panel1.Controls.Add(btnConvertToIso);

            // Add context menu for right-click download
            var contextMenu = new ContextMenuStrip();
            var downloadMenuItem = new ToolStripMenuItem("Download Update");
            downloadMenuItem.Click += DownloadMenuItem_Click;
            contextMenu.Items.Add(downloadMenuItem);

            lstUpdates.ContextMenuStrip = contextMenu;
        }

        private void BtnDownload_Click(object sender, EventArgs e)
        {
            DownloadSelectedUpdate();
        }

        private void BtnTestDownload_Click(object sender, EventArgs e)
        {
            if (lstUpdates.SelectedItems.Count == 0)
            {
                MessageBox.Show("Please select an update to test.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedItem = lstUpdates.SelectedItems[0];
            var updateId = selectedItem.SubItems[1].Text; // UUID is in the second column

            if (string.IsNullOrEmpty(updateId) || updateId == "N/A")
            {
                MessageBox.Show("Cannot test this update - no valid UUID found.", "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            TestDownloadUrl(updateId);
        }

        private void DownloadMenuItem_Click(object sender, EventArgs e)
        {
            DownloadSelectedUpdate();
        }

        private void DownloadSelectedUpdate()
        {
            if (lstUpdates.SelectedItems.Count == 0)
            {
                MessageBox.Show("Please select an update to download.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedItem = lstUpdates.SelectedItems[0];
            var updateId = selectedItem.SubItems[1].Text; // UUID is in the second column
            var title = selectedItem.SubItems[0].Text;
            var buildNumber = selectedItem.SubItems[3].Text; // Build number is in the fourth column

            if (string.IsNullOrEmpty(updateId) || updateId == "N/A")
            {
                MessageBox.Show("Cannot download this update - no valid UUID found.", "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Check if aria2c.exe is available
            if (!IsAria2Available())
            {
                ShowAria2SetupInstructions();
                return;
            }

            try
            {
                // Create download URL - try different endpoints
                var downloadUrl = $"https://api.uupdump.net/get.php?id={updateId}";
                
                // Show download information
                var result = MessageBox.Show(
                    $"Download Information:\n\n" +
                    $"Title: {title}\n" +
                    $"Build: {buildNumber}\n" +
                    $"UUID: {updateId}\n" +
                    $"URL: {downloadUrl}\n\n" +
                    $"This will download the update package using aria2.\n" +
                    $"The file may be large (several hundred MB).\n\n" +
                    $"Do you want to continue?",
                    "Confirm Download",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Show save dialog
                    using var saveDialog = new SaveFileDialog
                    {
                        FileName = $"{title.Replace(" ", "_").Replace("/", "_").Replace("\\", "_").Replace(":", "_")}.zip",
                        Filter = "ZIP files (*.zip)|*.zip|All files (*.*)|*.*",
                        Title = "Save Update Package"
                    };

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        DownloadUpdateAsync(downloadUrl, saveDialog.FileName, title);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error preparing download: {ex.Message}", "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DownloadUpdateAsync(string downloadUrl, string savePath, string title)
        {
            try
            {
                // Update UI to show download progress
                btnFetchUpdates.Enabled = false;
                lblStatus.Text = $"Getting download info for {title}...";
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                // Step 1: Get metadata from UUP Dump API
                var metadataUrl = downloadUrl + "&type=download&autodl=1";
                lblStatus.Text = $"Fetching metadata from UUP Dump...";

                using var metadataResponse = await _httpClient.GetAsync(metadataUrl);
                metadataResponse.EnsureSuccessStatusCode();

                var metadataJson = await metadataResponse.Content.ReadAsStringAsync();
                var metadata = JObject.Parse(metadataJson);

                // Step 2: Check if we have aria2c.exe available
                var aria2cPath = Path.Combine(Application.StartupPath, "aria2c.exe");
                if (!File.Exists(aria2cPath))
                {
                    throw new Exception("aria2c.exe not found. Please ensure aria2c.exe is in the application directory.");
                }

                // Step 3: Use aria2 to download the files
                var downloadSuccess = await DownloadWithAria2(metadata, savePath, title);
                if (!downloadSuccess)
                {
                    // Fallback to direct download if aria2 fails
                    lblStatus.Text = "aria2 download failed, trying direct download...";
                    var downloadInfo = ParseDownloadMetadata(metadata, title);
                    if (downloadInfo != null)
                    {
                        await DownloadFileFromUrl(downloadInfo.DownloadUrl, savePath, downloadInfo.FileName, downloadInfo.FileSize, title);
                    }
                    else
                    {
                        throw new Exception("Failed to parse download information from UUP Dump response");
                    }
                }
                else
                {
                    lblStatus.Text = $"Download completed: {Path.GetFileName(savePath)}";
                    MessageBox.Show($"Update downloaded successfully using aria2!\n\nFile: {Path.GetFileName(savePath)}\nLocation: {savePath}", 
                        "Download Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (HttpRequestException ex)
            {
                lblStatus.Text = "Download failed - network error.";
                MessageBox.Show($"Download failed: {ex.Message}\n\nPlease check your internet connection and try again.", 
                    "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Download failed.";
                MessageBox.Show($"Download failed: {ex.Message}", "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Reset UI state
                btnFetchUpdates.Enabled = true;
                progressBar.Visible = false;
                progressBar.Style = ProgressBarStyle.Marquee;
            }
        }

        private async Task<bool> DownloadWithAria2(JObject metadata, string savePath, string title)
        {
            try
            {
                lblStatus.Text = "Using aria2 to download UUP files...";

                // Create a temporary directory for aria2 downloads
                var tempDir = Path.Combine(Path.GetDirectoryName(savePath), "aria2_temp");
                Directory.CreateDirectory(tempDir);

                // Generate aria2 input file from metadata
                var aria2InputFile = Path.Combine(tempDir, "download.txt");
                var aria2cPath = Path.Combine(Application.StartupPath, "aria2c.exe");

                // Parse metadata to get download URLs
                var downloadUrls = ParseAria2Urls(metadata);
                if (downloadUrls.Count == 0)
                {
                    return false;
                }

                // Write aria2 input file
                await File.WriteAllLinesAsync(aria2InputFile, downloadUrls);

                // Run aria2c
                var startInfo = new ProcessStartInfo
                {
                    FileName = aria2cPath,
                    Arguments = $"--input-file=\"{aria2InputFile}\" --dir=\"{tempDir}\" --continue=true --max-concurrent-downloads=5 --max-connection-per-server=5 --min-split-size=1M --split=5",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = new Process { StartInfo = startInfo };
                process.Start();

                // Monitor aria2 progress
                var lastProgressUpdate = DateTime.Now;
                while (!process.HasExited)
                {
                    var output = await process.StandardOutput.ReadLineAsync();
                    if (output != null)
                    {
                        // Parse aria2 progress output
                        if (output.Contains("[#") && output.Contains("%"))
                        {
                            var percentMatch = System.Text.RegularExpressions.Regex.Match(output, @"(\d+\.?\d*)%");
                            if (percentMatch.Success && double.TryParse(percentMatch.Groups[1].Value, out double percent))
                            {
                                lblStatus.Text = $"Downloading with aria2... {percent:F1}%";
                                progressBar.Value = (int)percent;
                            }
                        }

                        // Update progress every 500ms
                        if ((DateTime.Now - lastProgressUpdate).TotalMilliseconds > 500)
                        {
                            lblStatus.Text = $"Downloading with aria2... {output}";
                            lastProgressUpdate = DateTime.Now;
                        }
                    }

                    await Task.Delay(100);
                }

                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    // Move downloaded files to final location
                    var downloadedFiles = Directory.GetFiles(tempDir, "*.*", SearchOption.AllDirectories);
                    if (downloadedFiles.Length > 0)
                    {
                        // Find the main UUP file (usually the largest)
                        var mainFile = downloadedFiles.OrderByDescending(f => new FileInfo(f).Length).First();
                        File.Copy(mainFile, savePath, true);

                        // Clean up temp directory
                        Directory.Delete(tempDir, true);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"aria2 download failed: {ex.Message}");
                return false;
            }
        }

        private List<string> ParseAria2Urls(JObject metadata)
        {
            var urls = new List<string>();

            try
            {
                // Look for files in the metadata
                var files = metadata["files"] as JObject;
                if (files == null)
                {
                    files = metadata["response"]?["files"] as JObject;
                }

                if (files != null)
                {
                    foreach (var file in files)
                    {
                        var fileInfo = file.Value as JObject;
                        if (fileInfo != null)
                        {
                            var url = fileInfo["url"]?.ToString();
                            if (!string.IsNullOrEmpty(url))
                            {
                                urls.Add(url);
                            }
                        }
                    }
                }

                // If no files found, try direct download URL
                if (urls.Count == 0)
                {
                    var downloadUrl = metadata["downloadUrl"]?.ToString();
                    if (!string.IsNullOrEmpty(downloadUrl))
                    {
                        urls.Add(downloadUrl);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to parse aria2 URLs: {ex.Message}");
            }

            return urls;
        }

        private DownloadInfo? ParseDownloadMetadata(JObject metadata, string title)
        {
            try
            {
                // Look for the largest file in the response
                var files = metadata["files"] as JObject;
                if (files == null)
                {
                    // Try alternative structure
                    files = metadata["response"]?["files"] as JObject;
                }

                if (files == null)
                {
                    // Try to find direct download links
                    var downloadUrl = metadata["downloadUrl"]?.ToString();
                    var fileName = metadata["fileName"]?.ToString();
                    var fileSize = metadata["fileSize"]?.Value<long>() ?? 0;

                    if (!string.IsNullOrEmpty(downloadUrl))
                    {
                        return new DownloadInfo
                        {
                            FileName = fileName ?? "download.file",
                            DownloadUrl = downloadUrl,
                            FileSize = fileSize
                        };
                    }

                    throw new Exception("No files found in metadata response");
                }

                // Find the largest file (usually the main update file)
                var largestFile = "";
                var largestSize = 0L;
                var largestDownloadUrl = "";

                foreach (var file in files)
                {
                    var fileInfo = file.Value as JObject;
                    if (fileInfo != null)
                    {
                        var size = fileInfo["size"]?.Value<long>() ?? 0;
                        var url = fileInfo["url"]?.ToString() ?? "";

                        if (size > largestSize && !string.IsNullOrEmpty(url))
                        {
                            largestSize = size;
                            largestFile = file.Key;
                            largestDownloadUrl = url;
                        }
                    }
                }

                if (string.IsNullOrEmpty(largestDownloadUrl))
                {
                    throw new Exception("No valid download URL found in metadata");
                }

                return new DownloadInfo
                {
                    FileName = largestFile,
                    DownloadUrl = largestDownloadUrl,
                    FileSize = largestSize
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to parse download metadata: {ex.Message}");
            }
        }

        private async Task DownloadFileFromUrl(string downloadUrl, string savePath, string fileName, long expectedSize, string title = "")
        {
            using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();

            var downloadedBytes = 0L;
            var lastProgressUpdate = DateTime.Now;

            using var contentStream = await response.Content.ReadAsStreamAsync();
            using var fileStream = new FileStream(savePath, FileMode.Create, FileAccess.Write, FileShare.None);
            
            var buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;

                // Update progress every 500ms to avoid UI lag
                if ((DateTime.Now - lastProgressUpdate).TotalMilliseconds > 500)
                {
                    var mbDownloaded = downloadedBytes / 1024.0 / 1024.0;
                    var mbTotal = expectedSize / 1024.0 / 1024.0;
                    var percent = expectedSize > 0 ? (downloadedBytes * 100.0 / expectedSize) : 0;
                    
                    lblStatus.Text = $"Downloading {fileName}... {mbDownloaded:F1}MB / {mbTotal:F1}MB ({percent:F1}%)";
                    lastProgressUpdate = DateTime.Now;
                }
            }

            // Ensure all data is written
            await fileStream.FlushAsync();

            // Validate download size
            if (expectedSize > 0 && downloadedBytes < expectedSize * 0.9) // Allow 10% tolerance
            {
                throw new Exception($"Download appears incomplete. Expected {expectedSize / 1024.0 / 1024.0:F1}MB, got {downloadedBytes / 1024.0 / 1024.0:F1}MB");
            }

            // After successful download, offer to install if it's a Windows Update package
            // Check for various Windows Update file types and patterns
            var shouldInstall = fileName.EndsWith(".zip") ||
                               fileName.EndsWith(".esd") ||
                               fileName.EndsWith(".cab") ||
                               fileName.EndsWith(".wim") ||
                               fileName.EndsWith(".msu") ||
                               fileName.EndsWith(".msix") ||
                               savePath.Contains("UUP") ||
                               savePath.Contains("uup") ||
                               title.Contains("Windows") ||
                               title.Contains("Update");

            // Debug information
            Console.WriteLine($"Download completed: {fileName}");
            Console.WriteLine($"File path: {savePath}");
            Console.WriteLine($"Title: {title}");
            Console.WriteLine($"Should offer installation: {shouldInstall}");

            if (shouldInstall)
            {
                var result = MessageBox.Show(
                    $"Download completed successfully!\n\n" +
                    $"File: {fileName}\n" +
                    $"Location: {savePath}\n\n" +
                    $"Would you like to install these Windows Updates now?\n\n" +
                    $"Note: Installation requires Administrator privileges and may require a restart.",
                    "Install Updates?",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    lblStatus.Text = "Starting update installation...";
                    await InstallUpdates(savePath, fileName);
                }
                else
                {
                    lblStatus.Text = $"Download completed: {fileName}";
                }
            }
            else
            {
                lblStatus.Text = $"Download completed: {fileName}";
                MessageBox.Show($"Download completed successfully!\n\nFile: {fileName}\nLocation: {savePath}",
                    "Download Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async Task InstallUpdates(string updateFilePath, string fileName)
        {
            try
            {
                lblStatus.Text = "Installing Windows Updates...";
                progressBar.Style = ProgressBarStyle.Marquee;

                // Create Updates directory for the installer
                var updatesDir = Path.Combine(Path.GetDirectoryName(updateFilePath), "Updates");
                Directory.CreateDirectory(updatesDir);

                // Extract update files to the Updates directory
                await ExtractUupFiles(updateFilePath, updatesDir);

                // Use Windows Update installer
                var installer = new WindowsUpdateInstaller(updatesDir, status =>
                {
                    // Update UI on main thread
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() => lblStatus.Text = status));
                    }
                    else
                    {
                        lblStatus.Text = status;
                    }
                });

                var result = await installer.InstallUpdatesAsync();

                if (result.Success)
                {
                    lblStatus.Text = "Windows Updates installed successfully!";

                    var detailsText = result.Details.Count > 0
                        ? $"\n\nInstallation Details:\n{string.Join("\n", result.Details)}"
                        : "";

                    MessageBox.Show($"{result.Message}{detailsText}\n\nA system restart may be required to complete the installation.",
                        "Installation Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    throw new Exception(result.Message);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Update installation failed";
                MessageBox.Show($"Failed to install Windows Updates: {ex.Message}\n\nYou may need to run this application as Administrator or install updates manually.",
                    "Installation Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            finally
            {
                progressBar.Style = ProgressBarStyle.Continuous;
            }
        }

        private async Task ExtractUupFiles(string uupFilePath, string uupsDir)
        {
            try
            {
                lblStatus.Text = "Extracting UUP files...";

                // Check if the file is actually a valid archive
                var fileInfo = new FileInfo(uupFilePath);
                if (fileInfo.Length < 1024) // Less than 1KB is probably not a valid file
                {
                    throw new Exception($"Downloaded file is too small ({fileInfo.Length} bytes). The download may have failed.");
                }

                // If it's a ZIP file, extract it
                if (uupFilePath.EndsWith(".zip"))
                {
                    try
                    {
                        using var archive = ZipFile.OpenRead(uupFilePath);
                        foreach (var entry in archive.Entries)
                        {
                            var extractPath = Path.Combine(uupsDir, entry.FullName);
                            var extractDir = Path.GetDirectoryName(extractPath);
                            if (!string.IsNullOrEmpty(extractDir))
                            {
                                Directory.CreateDirectory(extractDir);
                            }
                            entry.ExtractToFile(extractPath, true);
                        }
                    }
                    catch (InvalidDataException ex)
                    {
                        throw new Exception($"Invalid ZIP file: {ex.Message}. The download may be corrupted or incomplete.");
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Failed to extract ZIP file: {ex.Message}");
                    }
                }
                else if (uupFilePath.EndsWith(".esd") || uupFilePath.EndsWith(".cab") || uupFilePath.EndsWith(".wim"))
                {
                    // These are already in the correct format, just copy them
                    var destPath = Path.Combine(uupsDir, Path.GetFileName(uupFilePath));
                    File.Copy(uupFilePath, destPath, true);
                }
                else
                {
                    // For unknown file types, try to determine if it's a valid file
                    var firstBytes = new byte[4];
                    using (var stream = File.OpenRead(uupFilePath))
                    {
                        stream.Read(firstBytes, 0, 4);
                    }

                    // Check for common file signatures
                    var isZip = firstBytes[0] == 0x50 && firstBytes[1] == 0x4B; // PK
                    var isCab = firstBytes[0] == 0x4D && firstBytes[1] == 0x53 && firstBytes[2] == 0x43 && firstBytes[3] == 0x46; // MSCF

                    if (isZip)
                    {
                        // Try to extract as ZIP even if extension is wrong
                        try
                        {
                            using var archive = ZipFile.OpenRead(uupFilePath);
                            foreach (var entry in archive.Entries)
                            {
                                var extractPath = Path.Combine(uupsDir, entry.FullName);
                                var extractDir = Path.GetDirectoryName(extractPath);
                                if (!string.IsNullOrEmpty(extractDir))
                                {
                                    Directory.CreateDirectory(extractDir);
                                }
                                entry.ExtractToFile(extractPath, true);
                            }
                        }
                        catch (Exception ex)
                        {
                            throw new Exception($"File appears to be ZIP but extraction failed: {ex.Message}");
                        }
                    }
                    else if (isCab)
                    {
                        // Copy CAB file directly
                        var destPath = Path.Combine(uupsDir, Path.GetFileName(uupFilePath));
                        File.Copy(uupFilePath, destPath, true);
                    }
                    else
                    {
                        // Copy the file to UUPs directory as-is
                        var destPath = Path.Combine(uupsDir, Path.GetFileName(uupFilePath));
                        File.Copy(uupFilePath, destPath, true);
                    }
                }

                // Verify that we have some files in the UUPs directory
                var files = Directory.GetFiles(uupsDir, "*.*", SearchOption.AllDirectories);
                if (files.Length == 0)
                {
                    throw new Exception("No files were extracted to the UUPs directory. The download may be corrupted.");
                }

                lblStatus.Text = $"Extracted {files.Length} files to UUPs directory";
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to extract UUP files: {ex.Message}");
            }
        }

        private class DownloadInfo
        {
            public string FileName { get; set; } = "";
            public string DownloadUrl { get; set; } = "";
            public long FileSize { get; set; } = 0;
        }

        private async void TestDownloadUrl(string updateId)
        {
            try
            {
                var downloadUrl = $"https://api.uupdump.net/get.php?id={updateId}&type=download&autodl=1";
                lblStatus.Text = $"Testing URL: {downloadUrl}";

                using var response = await _httpClient.GetAsync(downloadUrl);
                
                var contentType = response.Content.Headers.ContentType?.MediaType ?? "unknown";
                var contentLength = response.Content.Headers.ContentLength;
                var statusCode = response.StatusCode;

                var info = $"Status: {statusCode}\nContent-Type: {contentType}\nContent-Length: {contentLength} bytes\nURL: {downloadUrl}";

                // Read the full response to see the metadata structure
                var responseContent = await response.Content.ReadAsStringAsync();
                
                info += $"\n\nFull Response (first 2000 chars):\n{responseContent.Substring(0, Math.Min(2000, responseContent.Length))}";

                if (responseContent.Length > 2000)
                {
                    info += $"\n\n... (truncated, total length: {responseContent.Length} characters)";
                }

                MessageBox.Show(info, "Download URL Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Test failed: {ex.Message}", "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeFilterControls()
        {
            // Remove the old API source controls
            var controlsToRemove = new List<Control>();
            foreach (Control control in panel1.Controls)
            {
                if (control is Label lbl && lbl.Text == "API Source:")
                    controlsToRemove.Add(lbl);
                if (control is ComboBox)
                    controlsToRemove.Add(control);
            }
            foreach (var control in controlsToRemove)
            {
                panel1.Controls.Remove(control);
            }

            // Add filter controls
            var lblFilters = new Label
            {
                Text = "Filters:",
                Location = new Point(180, 55),
                AutoSize = true
            };

            var btnAddFilter = new Button
            {
                Text = "Add Filter",
                Location = new Point(250, 52),
                Size = new Size(80, 25)
            };
            btnAddFilter.Click += BtnAddFilter_Click;

            var btnSaveFilters = new Button
            {
                Text = "Save Filters",
                Location = new Point(340, 52),
                Size = new Size(80, 25)
            };
            btnSaveFilters.Click += BtnSaveFilters_Click;

            var btnLoadFilters = new Button
            {
                Text = "Load Filters",
                Location = new Point(430, 52),
                Size = new Size(80, 25)
            };
            btnLoadFilters.Click += BtnLoadFilters_Click;

            panel1.Controls.Add(lblFilters);
            panel1.Controls.Add(btnAddFilter);
            panel1.Controls.Add(btnSaveFilters);
            panel1.Controls.Add(btnLoadFilters);
        }

        private void BtnAddFilter_Click(object sender, EventArgs e)
        {
            using var filterForm = new FilterForm();
            if (filterForm.ShowDialog() == DialogResult.OK)
            {
                _filters.Add(filterForm.Filter);
                UpdateFilterDisplay();
            }
        }

        private void BtnSaveFilters_Click(object sender, EventArgs e)
        {
            try
            {
                var json = JsonSerializer.Serialize(_filters, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(FILTER_FILE, json);
                MessageBox.Show("Filters saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving filters: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnLoadFilters_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists(FILTER_FILE))
                {
                    var json = File.ReadAllText(FILTER_FILE);
                    _filters = JsonSerializer.Deserialize<List<UpdateFilter>>(json) ?? new List<UpdateFilter>();
                    UpdateFilterDisplay();
                    MessageBox.Show("Filters loaded successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("No saved filters found.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading filters: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateFilterDisplay()
        {
            // Update status to show active filters
            var filterText = _filters.Count > 0 
                ? $"Active filters: {string.Join(", ", _filters.Select(f => f.Name))}"
                : "No filters active - showing all updates";
            lblStatus.Text = filterText;
        }

        private void LoadFilters()
        {
            try
            {
                if (File.Exists(FILTER_FILE))
                {
                    var json = File.ReadAllText(FILTER_FILE);
                    _filters = JsonSerializer.Deserialize<List<UpdateFilter>>(json) ?? new List<UpdateFilter>();
                    UpdateFilterDisplay();
                }
            }
            catch (Exception ex)
            {
                // Silently handle load errors
                _filters = new List<UpdateFilter>();
            }
        }

        private async void btnFetchUpdates_Click(object sender, EventArgs e)
        {
            await FetchUpdatesAsync();
        }

        private async Task FetchUpdatesAsync()
        {
            try
            {
                // Update UI to show loading state
                btnFetchUpdates.Enabled = false;
                lblStatus.Text = "Fetching updates...";
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                lstUpdates.Items.Clear();

                var url = "https://api.uupdump.net/listid.php?search=windows+11&ring=retail&arch=x64&latest=true";

                var json = await _httpClient.GetStringAsync(url);
                var data = JObject.Parse(json);

                bool success = ParseAndFilterUpdates(data, out int count);

                if (success)
                {
                    lblStatus.Text = $"Found {count} updates after filtering.";
                }
                else
                {
                    lblStatus.Text = "Failed to get update info.";
                    MessageBox.Show("Failed to retrieve update information. Please check your internet connection and try again.", 
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (HttpRequestException ex)
            {
                lblStatus.Text = "Network error occurred.";
                MessageBox.Show($"Network error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "An error occurred.";
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Reset UI state
                btnFetchUpdates.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private bool ParseAndFilterUpdates(JObject data, out int count)
        {
            count = 0;
            
            if (data["response"]?["builds"] != null)
            {
                var builds = data["response"]?["builds"];
                
                if (builds != null)
                {
                    foreach (JProperty build in builds)
                    {
                        var buildData = build.Value;
                        if (buildData != null)
                        {
                            // Apply filters
                            if (ShouldIncludeUpdate(buildData))
                            {
                                var item = new ListViewItem(buildData["title"]?.ToString() ?? "N/A");
                                item.SubItems.Add(buildData["uuid"]?.ToString() ?? "N/A");
                                
                                // Convert Unix timestamp to readable date
                                var createdTimestamp = buildData["created"]?.Value<long?>();
                                var createdDate = createdTimestamp.HasValue 
                                    ? DateTimeOffset.FromUnixTimeSeconds(createdTimestamp.Value).DateTime.ToString("yyyy-MM-dd HH:mm:ss")
                                    : "N/A";
                                item.SubItems.Add(createdDate);
                                
                                item.SubItems.Add(buildData["build"]?.ToString() ?? "N/A");

                                lstUpdates.Items.Add(item);
                                count++;
                            }
                        }
                    }
                }
                return true;
            }
            return false;
        }

        private bool ShouldIncludeUpdate(JToken buildData)
        {
            if (_filters.Count == 0) return true; // No filters = show all

            var title = buildData["title"]?.ToString() ?? "";
            var build = buildData["build"]?.ToString() ?? "";
            var arch = buildData["arch"]?.ToString() ?? "";

            foreach (var filter in _filters)
            {
                bool matches = true;

                // Check title filters
                if (!string.IsNullOrEmpty(filter.TitleContains) && !title.Contains(filter.TitleContains, StringComparison.OrdinalIgnoreCase))
                    matches = false;

                if (!string.IsNullOrEmpty(filter.TitleExcludes) && title.Contains(filter.TitleExcludes, StringComparison.OrdinalIgnoreCase))
                    matches = false;

                // Check build number filters
                if (!string.IsNullOrEmpty(filter.BuildContains) && !build.Contains(filter.BuildContains))
                    matches = false;

                if (!string.IsNullOrEmpty(filter.BuildExcludes) && build.Contains(filter.BuildExcludes))
                    matches = false;

                // Check architecture filters
                if (!string.IsNullOrEmpty(filter.Architecture) && !arch.Equals(filter.Architecture, StringComparison.OrdinalIgnoreCase))
                    matches = false;

                // If any filter doesn't match, exclude this update
                if (!matches) return false;
            }

            return true; // All filters passed
        }

        private void BtnConvertToIso_Click(object sender, EventArgs e)
        {
            // Show file dialog to select a downloaded file for installation
            var openDialog = new OpenFileDialog
            {
                Title = "Select Windows Update package to install",
                Filter = "ZIP files (*.zip)|*.zip|Update files (*.msix;*.cab;*.wim;*.msu)|*.msix;*.cab;*.wim;*.msu|All files (*.*)|*.*",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (openDialog.ShowDialog() == DialogResult.OK)
            {
                var filePath = openDialog.FileName;
                var fileName = Path.GetFileName(filePath);

                // Show confirmation dialog
                var result = MessageBox.Show(
                    $"This will install Windows Updates from:\n{fileName}\n\n" +
                    "IMPORTANT:\n" +
                    "• This requires Administrator privileges\n" +
                    "• A system restart may be required\n" +
                    "• Make sure to close other applications\n\n" +
                    "Do you want to continue?",
                    "Install Windows Updates",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Start installation
                    _ = InstallUpdates(filePath, fileName);
                }
            }
        }

        private bool IsAria2Available()
        {
            var aria2cPath = Path.Combine(Application.StartupPath, "aria2c.exe");
            return File.Exists(aria2cPath);
        }

        private void ShowAria2SetupInstructions()
        {
            var message = @"aria2c.exe is required for downloading UUP files.

Setup Instructions:
1. Download aria2c.exe from: https://uupdump.net/misc/aria2c.exe
2. Place aria2c.exe in the same directory as this application
3. Restart the application

Alternatively, you can use the 'Install Updates' button to manually install downloaded update files.";
            
            MessageBox.Show(message, "aria2c.exe Required", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class UpdateFilter
    {
        public string Name { get; set; } = "";
        public string TitleContains { get; set; } = "";
        public string TitleExcludes { get; set; } = "";
        public string BuildContains { get; set; } = "";
        public string BuildExcludes { get; set; } = "";
        public string Architecture { get; set; } = "";
    }
}
