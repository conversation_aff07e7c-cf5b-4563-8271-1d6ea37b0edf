using System.Net.Http;
using Newtonsoft.Json.Linq;
using System.Text.Json;
using System.IO.Compression;
using System.Diagnostics;

namespace WindowsUpdateTool
{
    public partial class Form1 : Form
    {
        private readonly HttpClient _httpClient;
        private List<UpdateFilter> _filters = new();
        private const string FILTER_FILE = "update_filters.json";

        public Form1()
        {
            InitializeComponent();
            _httpClient = new HttpClient();
            InitializeFilterControls();
            InitializeDownloadControls();
            LoadFilters();
        }

        private void InitializeDownloadControls()
        {
            // Add download button
            var btnDownload = new Button
            {
                Text = "Download Selected",
                Location = new Point(520, 52),
                Size = new Size(120, 25)
            };
            btnDownload.Click += BtnDownload_Click;

            // Add test button
            var btnTestDownload = new Button
            {
                Text = "Test Download URL",
                Location = new Point(650, 52),
                Size = new Size(120, 25)
            };
            btnTestDownload.Click += BtnTestDownload_Click;

            // Add install updates button
            var btnConvertToIso = new Button
            {
                Text = "Install Updates",
                Location = new Point(780, 52),
                Size = new Size(120, 25)
            };
            btnConvertToIso.Click += BtnConvertToIso_Click;

            panel1.Controls.Add(btnDownload);
            panel1.Controls.Add(btnTestDownload);
            panel1.Controls.Add(btnConvertToIso);

            // Add context menu for right-click download
            var contextMenu = new ContextMenuStrip();
            var downloadMenuItem = new ToolStripMenuItem("Download Update");
            downloadMenuItem.Click += DownloadMenuItem_Click;
            contextMenu.Items.Add(downloadMenuItem);

            lstUpdates.ContextMenuStrip = contextMenu;
        }

        private void BtnDownload_Click(object sender, EventArgs e)
        {
            DownloadSelectedUpdate();
        }

        private void BtnTestDownload_Click(object sender, EventArgs e)
        {
            if (lstUpdates.SelectedItems.Count == 0)
            {
                MessageBox.Show("Please select an update to test.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedItem = lstUpdates.SelectedItems[0];
            var updateId = selectedItem.SubItems[1].Text; // UUID is in the second column

            if (string.IsNullOrEmpty(updateId) || updateId == "N/A")
            {
                MessageBox.Show("Cannot test this update - no valid UUID found.", "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            TestDownloadUrl(updateId);
        }

        private void DownloadMenuItem_Click(object sender, EventArgs e)
        {
            DownloadSelectedUpdate();
        }

        private void DownloadSelectedUpdate()
        {
            if (lstUpdates.SelectedItems.Count == 0)
            {
                MessageBox.Show("Please select an update to download.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedItem = lstUpdates.SelectedItems[0];
            var update = selectedItem.Tag as WindowsUpdate;

            if (update == null)
            {
                MessageBox.Show("Invalid update selection.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                // Show download information
                var result = MessageBox.Show(
                    $"Download Information:\n\n" +
                    $"Title: {update.Title}\n" +
                    $"KB: {update.UpdateId}\n" +
                    $"Build: {update.BuildNumber}\n" +
                    $"Architecture: {update.Architecture}\n" +
                    $"Size: {(update.Size > 0 ? $"{update.Size / (1024 * 1024):F1} MB" : "Unknown")}\n\n" +
                    $"This will download the actual MSU/EXE update file from Microsoft.\n" +
                    $"The downloaded file can be installed directly.\n\n" +
                    $"Do you want to continue?",
                    "Confirm Download",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Show save dialog
                    using var saveDialog = new SaveFileDialog
                    {
                        FileName = $"{update.UpdateId}_{update.Architecture}.msu",
                        Filter = "Windows Update files (*.msu;*.exe)|*.msu;*.exe|All files (*.*)|*.*",
                        Title = "Save Update Package"
                    };

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        DownloadUpdateWithKbUpdate(update, saveDialog.FileName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error preparing download: {ex.Message}", "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DownloadUpdateWithKbUpdate(WindowsUpdate update, string savePath)
        {
            try
            {
                lblStatus.Text = $"Downloading {update.UpdateId}...";
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                var appPath = Application.StartupPath;
                var modulesPath = Path.Combine(appPath, "Modules");

                var script = $@"
                    try {{
                        # Add bundled modules path to PSModulePath if it exists
                        $bundledModulesPath = '{modulesPath}'
                        if (Test-Path $bundledModulesPath) {{
                            $env:PSModulePath = '$bundledModulesPath;' + $env:PSModulePath
                        }}

                        Import-Module kbupdate -ErrorAction Stop

                        # Download the update using kbupdate
                        $update = Get-KbUpdate -Name '{update.UpdateId}' -Architecture '{update.Architecture}' | Select-Object -First 1
                        if ($update) {{
                            $result = Save-KbUpdate -InputObject $update -Path '{Path.GetDirectoryName(savePath)}' -PassThru
                            if ($result) {{
                                Write-Output ""SUCCESS:$($result.FullName)""
                            }} else {{
                                Write-Output ""ERROR: Failed to download update""
                            }}
                        }} else {{
                            Write-Output ""ERROR: Update not found in catalog""
                        }}
                    }}
                    catch {{
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }}
                ";

                var result = await ExecutePowerShellScript(script);
                var lines = result.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                string downloadedFile = null;
                foreach (var line in lines)
                {
                    if (line.StartsWith("SUCCESS:"))
                    {
                        downloadedFile = line.Substring("SUCCESS:".Length).Trim();
                        break;
                    }
                    else if (line.StartsWith("ERROR:"))
                    {
                        throw new Exception(line.Replace("ERROR:", "").Trim());
                    }
                }

                if (!string.IsNullOrEmpty(downloadedFile) && File.Exists(downloadedFile))
                {
                    // Move the file to the desired location
                    if (downloadedFile != savePath)
                    {
                        File.Move(downloadedFile, savePath, true);
                    }

                    lblStatus.Text = "Download completed successfully!";

                    var installResult = MessageBox.Show(
                        $"Download completed successfully!\n\n" +
                        $"File: {Path.GetFileName(savePath)}\n" +
                        $"Location: {savePath}\n\n" +
                        $"Would you like to install this update now?\n\n" +
                        $"Note: Installation requires Administrator privileges and may require a restart.",
                        "Install Update?",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (installResult == DialogResult.Yes)
                    {
                        await InstallMsuFile(savePath);
                    }
                }
                else
                {
                    throw new Exception("Download completed but file was not found");
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Download failed";
                MessageBox.Show($"Failed to download update: {ex.Message}", "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async void DownloadUpdateAsync(string downloadUrl, string savePath, string title)
        {
            try
            {
                // Update UI to show download progress
                btnFetchUpdates.Enabled = false;
                lblStatus.Text = $"Getting download info for {title}...";
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                // Step 1: Get metadata from UUP Dump API
                var metadataUrl = downloadUrl + "&type=download&autodl=1";
                lblStatus.Text = $"Fetching metadata from UUP Dump...";

                using var metadataResponse = await _httpClient.GetAsync(metadataUrl);
                metadataResponse.EnsureSuccessStatusCode();

                var metadataJson = await metadataResponse.Content.ReadAsStringAsync();
                var metadata = JObject.Parse(metadataJson);

                // Step 2: Check if we have aria2c.exe available
                var aria2cPath = Path.Combine(Application.StartupPath, "aria2c.exe");
                if (!File.Exists(aria2cPath))
                {
                    throw new Exception("aria2c.exe not found. Please ensure aria2c.exe is in the application directory.");
                }

                // Step 3: Use aria2 to download the files
                var downloadSuccess = await DownloadWithAria2(metadata, savePath, title);
                if (!downloadSuccess)
                {
                    // Fallback to direct download if aria2 fails
                    lblStatus.Text = "aria2 download failed, trying direct download...";
                    var downloadInfo = ParseDownloadMetadata(metadata, title);
                    if (downloadInfo != null)
                    {
                        await DownloadFileFromUrl(downloadInfo.DownloadUrl, savePath, downloadInfo.FileName, downloadInfo.FileSize, title);
                    }
                    else
                    {
                        throw new Exception("Failed to parse download information from UUP Dump response");
                    }
                }
                else
                {
                    lblStatus.Text = $"Download completed: {Path.GetFileName(savePath)}";
                    MessageBox.Show($"Update downloaded successfully using aria2!\n\nFile: {Path.GetFileName(savePath)}\nLocation: {savePath}", 
                        "Download Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (HttpRequestException ex)
            {
                lblStatus.Text = "Download failed - network error.";
                MessageBox.Show($"Download failed: {ex.Message}\n\nPlease check your internet connection and try again.", 
                    "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Download failed.";
                MessageBox.Show($"Download failed: {ex.Message}", "Download Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Reset UI state
                btnFetchUpdates.Enabled = true;
                progressBar.Visible = false;
                progressBar.Style = ProgressBarStyle.Marquee;
            }
        }

        private async Task<bool> DownloadWithAria2(JObject metadata, string savePath, string title)
        {
            try
            {
                lblStatus.Text = "Using aria2 to download UUP files...";

                // Create a temporary directory for aria2 downloads
                var tempDir = Path.Combine(Path.GetDirectoryName(savePath), "aria2_temp");
                Directory.CreateDirectory(tempDir);

                // Generate aria2 input file from metadata
                var aria2InputFile = Path.Combine(tempDir, "download.txt");
                var aria2cPath = Path.Combine(Application.StartupPath, "aria2c.exe");

                // Parse metadata to get download URLs
                var downloadUrls = ParseAria2Urls(metadata);
                if (downloadUrls.Count == 0)
                {
                    return false;
                }

                // Write aria2 input file
                await File.WriteAllLinesAsync(aria2InputFile, downloadUrls);

                // Run aria2c
                var startInfo = new ProcessStartInfo
                {
                    FileName = aria2cPath,
                    Arguments = $"--input-file=\"{aria2InputFile}\" --dir=\"{tempDir}\" --continue=true --max-concurrent-downloads=5 --max-connection-per-server=5 --min-split-size=1M --split=5",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = new Process { StartInfo = startInfo };
                process.Start();

                // Monitor aria2 progress
                var lastProgressUpdate = DateTime.Now;
                while (!process.HasExited)
                {
                    var output = await process.StandardOutput.ReadLineAsync();
                    if (output != null)
                    {
                        // Parse aria2 progress output
                        if (output.Contains("[#") && output.Contains("%"))
                        {
                            var percentMatch = System.Text.RegularExpressions.Regex.Match(output, @"(\d+\.?\d*)%");
                            if (percentMatch.Success && double.TryParse(percentMatch.Groups[1].Value, out double percent))
                            {
                                lblStatus.Text = $"Downloading with aria2... {percent:F1}%";
                                progressBar.Value = (int)percent;
                            }
                        }

                        // Update progress every 500ms
                        if ((DateTime.Now - lastProgressUpdate).TotalMilliseconds > 500)
                        {
                            lblStatus.Text = $"Downloading with aria2... {output}";
                            lastProgressUpdate = DateTime.Now;
                        }
                    }

                    await Task.Delay(100);
                }

                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    // Move downloaded files to final location
                    var downloadedFiles = Directory.GetFiles(tempDir, "*.*", SearchOption.AllDirectories);
                    if (downloadedFiles.Length > 0)
                    {
                        // Find the main UUP file (usually the largest)
                        var mainFile = downloadedFiles.OrderByDescending(f => new FileInfo(f).Length).First();
                        File.Copy(mainFile, savePath, true);

                        // Clean up temp directory
                        Directory.Delete(tempDir, true);
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"aria2 download failed: {ex.Message}");
                return false;
            }
        }

        private List<string> ParseAria2Urls(JObject metadata)
        {
            var urls = new List<string>();

            try
            {
                // Look for files in the metadata
                var files = metadata["files"] as JObject;
                if (files == null)
                {
                    files = metadata["response"]?["files"] as JObject;
                }

                if (files != null)
                {
                    foreach (var file in files)
                    {
                        var fileInfo = file.Value as JObject;
                        if (fileInfo != null)
                        {
                            var url = fileInfo["url"]?.ToString();
                            if (!string.IsNullOrEmpty(url))
                            {
                                urls.Add(url);
                            }
                        }
                    }
                }

                // If no files found, try direct download URL
                if (urls.Count == 0)
                {
                    var downloadUrl = metadata["downloadUrl"]?.ToString();
                    if (!string.IsNullOrEmpty(downloadUrl))
                    {
                        urls.Add(downloadUrl);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to parse aria2 URLs: {ex.Message}");
            }

            return urls;
        }

        private DownloadInfo? ParseDownloadMetadata(JObject metadata, string title)
        {
            try
            {
                // Look for the largest file in the response
                var files = metadata["files"] as JObject;
                if (files == null)
                {
                    // Try alternative structure
                    files = metadata["response"]?["files"] as JObject;
                }

                if (files == null)
                {
                    // Try to find direct download links
                    var downloadUrl = metadata["downloadUrl"]?.ToString();
                    var fileName = metadata["fileName"]?.ToString();
                    var fileSize = metadata["fileSize"]?.Value<long>() ?? 0;

                    if (!string.IsNullOrEmpty(downloadUrl))
                    {
                        return new DownloadInfo
                        {
                            FileName = fileName ?? "download.file",
                            DownloadUrl = downloadUrl,
                            FileSize = fileSize
                        };
                    }

                    throw new Exception("No files found in metadata response");
                }

                // Find the largest file (usually the main update file)
                var largestFile = "";
                var largestSize = 0L;
                var largestDownloadUrl = "";

                foreach (var file in files)
                {
                    var fileInfo = file.Value as JObject;
                    if (fileInfo != null)
                    {
                        var size = fileInfo["size"]?.Value<long>() ?? 0;
                        var url = fileInfo["url"]?.ToString() ?? "";

                        if (size > largestSize && !string.IsNullOrEmpty(url))
                        {
                            largestSize = size;
                            largestFile = file.Key;
                            largestDownloadUrl = url;
                        }
                    }
                }

                if (string.IsNullOrEmpty(largestDownloadUrl))
                {
                    throw new Exception("No valid download URL found in metadata");
                }

                return new DownloadInfo
                {
                    FileName = largestFile,
                    DownloadUrl = largestDownloadUrl,
                    FileSize = largestSize
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to parse download metadata: {ex.Message}");
            }
        }

        private async Task DownloadFileFromUrl(string downloadUrl, string savePath, string fileName, long expectedSize, string title = "")
        {
            using var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();

            var downloadedBytes = 0L;
            var lastProgressUpdate = DateTime.Now;

            using var contentStream = await response.Content.ReadAsStreamAsync();
            using var fileStream = new FileStream(savePath, FileMode.Create, FileAccess.Write, FileShare.None);
            
            var buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;

                // Update progress every 500ms to avoid UI lag
                if ((DateTime.Now - lastProgressUpdate).TotalMilliseconds > 500)
                {
                    var mbDownloaded = downloadedBytes / 1024.0 / 1024.0;
                    var mbTotal = expectedSize / 1024.0 / 1024.0;
                    var percent = expectedSize > 0 ? (downloadedBytes * 100.0 / expectedSize) : 0;
                    
                    lblStatus.Text = $"Downloading {fileName}... {mbDownloaded:F1}MB / {mbTotal:F1}MB ({percent:F1}%)";
                    lastProgressUpdate = DateTime.Now;
                }
            }

            // Ensure all data is written
            await fileStream.FlushAsync();

            // Validate download size
            if (expectedSize > 0 && downloadedBytes < expectedSize * 0.9) // Allow 10% tolerance
            {
                throw new Exception($"Download appears incomplete. Expected {expectedSize / 1024.0 / 1024.0:F1}MB, got {downloadedBytes / 1024.0 / 1024.0:F1}MB");
            }

            // After successful download, offer to install if it's a Windows Update package
            // Check for various Windows Update file types and patterns
            var shouldInstall = fileName.EndsWith(".zip") ||
                               fileName.EndsWith(".esd") ||
                               fileName.EndsWith(".cab") ||
                               fileName.EndsWith(".wim") ||
                               fileName.EndsWith(".msu") ||
                               fileName.EndsWith(".msix") ||
                               savePath.Contains("UUP") ||
                               savePath.Contains("uup") ||
                               title.Contains("Windows") ||
                               title.Contains("Update");

            // Debug information
            Console.WriteLine($"Download completed: {fileName}");
            Console.WriteLine($"File path: {savePath}");
            Console.WriteLine($"Title: {title}");
            Console.WriteLine($"Should offer installation: {shouldInstall}");

            if (shouldInstall)
            {
                var result = MessageBox.Show(
                    $"Download completed successfully!\n\n" +
                    $"File: {fileName}\n" +
                    $"Location: {savePath}\n\n" +
                    $"Would you like to install these Windows Updates now?\n\n" +
                    $"Note: Installation requires Administrator privileges and may require a restart.",
                    "Install Updates?",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    lblStatus.Text = "Starting update installation...";
                    await InstallUpdates(savePath, fileName);
                }
                else
                {
                    lblStatus.Text = $"Download completed: {fileName}";
                }
            }
            else
            {
                lblStatus.Text = $"Download completed: {fileName}";
                MessageBox.Show($"Download completed successfully!\n\nFile: {fileName}\nLocation: {savePath}",
                    "Download Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async Task InstallUpdates(string updateFilePath, string fileName)
        {
            try
            {
                lblStatus.Text = "Installing Windows Updates...";
                progressBar.Style = ProgressBarStyle.Marquee;

                // Create Updates directory for the installer
                var updatesDir = Path.Combine(Path.GetDirectoryName(updateFilePath), "Updates");
                Directory.CreateDirectory(updatesDir);

                // Extract update files to the Updates directory
                await ExtractUupFiles(updateFilePath, updatesDir);

                // Use Windows Update installer
                var installer = new WindowsUpdateInstaller(updatesDir, status =>
                {
                    // Update UI on main thread
                    if (InvokeRequired)
                    {
                        Invoke(new Action(() => lblStatus.Text = status));
                    }
                    else
                    {
                        lblStatus.Text = status;
                    }
                });

                var result = await installer.InstallUpdatesAsync();

                if (result.Success)
                {
                    lblStatus.Text = "Windows Updates installed successfully!";

                    var detailsText = result.Details.Count > 0
                        ? $"\n\nInstallation Details:\n{string.Join("\n", result.Details)}"
                        : "";

                    MessageBox.Show($"{result.Message}{detailsText}\n\nA system restart may be required to complete the installation.",
                        "Installation Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    throw new Exception(result.Message);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Update installation failed";
                MessageBox.Show($"Failed to install Windows Updates: {ex.Message}\n\nYou may need to run this application as Administrator or install updates manually.",
                    "Installation Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            finally
            {
                progressBar.Style = ProgressBarStyle.Continuous;
            }
        }

        private async Task ExtractUupFiles(string uupFilePath, string uupsDir)
        {
            try
            {
                lblStatus.Text = "Extracting UUP files...";

                // Check if the file is actually a valid archive
                var fileInfo = new FileInfo(uupFilePath);
                if (fileInfo.Length < 1024) // Less than 1KB is probably not a valid file
                {
                    throw new Exception($"Downloaded file is too small ({fileInfo.Length} bytes). The download may have failed.");
                }

                // If it's a ZIP file, extract it
                if (uupFilePath.EndsWith(".zip"))
                {
                    try
                    {
                        using var archive = ZipFile.OpenRead(uupFilePath);
                        foreach (var entry in archive.Entries)
                        {
                            var extractPath = Path.Combine(uupsDir, entry.FullName);
                            var extractDir = Path.GetDirectoryName(extractPath);
                            if (!string.IsNullOrEmpty(extractDir))
                            {
                                Directory.CreateDirectory(extractDir);
                            }
                            entry.ExtractToFile(extractPath, true);
                        }
                    }
                    catch (InvalidDataException ex)
                    {
                        throw new Exception($"Invalid ZIP file: {ex.Message}. The download may be corrupted or incomplete.");
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"Failed to extract ZIP file: {ex.Message}");
                    }
                }
                else if (uupFilePath.EndsWith(".esd") || uupFilePath.EndsWith(".cab") || uupFilePath.EndsWith(".wim"))
                {
                    // These are already in the correct format, just copy them
                    var destPath = Path.Combine(uupsDir, Path.GetFileName(uupFilePath));
                    File.Copy(uupFilePath, destPath, true);
                }
                else
                {
                    // For unknown file types, try to determine if it's a valid file
                    var firstBytes = new byte[4];
                    using (var stream = File.OpenRead(uupFilePath))
                    {
                        stream.Read(firstBytes, 0, 4);
                    }

                    // Check for common file signatures
                    var isZip = firstBytes[0] == 0x50 && firstBytes[1] == 0x4B; // PK
                    var isCab = firstBytes[0] == 0x4D && firstBytes[1] == 0x53 && firstBytes[2] == 0x43 && firstBytes[3] == 0x46; // MSCF

                    if (isZip)
                    {
                        // Try to extract as ZIP even if extension is wrong
                        try
                        {
                            using var archive = ZipFile.OpenRead(uupFilePath);
                            foreach (var entry in archive.Entries)
                            {
                                var extractPath = Path.Combine(uupsDir, entry.FullName);
                                var extractDir = Path.GetDirectoryName(extractPath);
                                if (!string.IsNullOrEmpty(extractDir))
                                {
                                    Directory.CreateDirectory(extractDir);
                                }
                                entry.ExtractToFile(extractPath, true);
                            }
                        }
                        catch (Exception ex)
                        {
                            throw new Exception($"File appears to be ZIP but extraction failed: {ex.Message}");
                        }
                    }
                    else if (isCab)
                    {
                        // Copy CAB file directly
                        var destPath = Path.Combine(uupsDir, Path.GetFileName(uupFilePath));
                        File.Copy(uupFilePath, destPath, true);
                    }
                    else
                    {
                        // Copy the file to UUPs directory as-is
                        var destPath = Path.Combine(uupsDir, Path.GetFileName(uupFilePath));
                        File.Copy(uupFilePath, destPath, true);
                    }
                }

                // Verify that we have some files in the UUPs directory
                var files = Directory.GetFiles(uupsDir, "*.*", SearchOption.AllDirectories);
                if (files.Length == 0)
                {
                    throw new Exception("No files were extracted to the UUPs directory. The download may be corrupted.");
                }

                lblStatus.Text = $"Extracted {files.Length} files to UUPs directory";
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to extract UUP files: {ex.Message}");
            }
        }

        private class DownloadInfo
        {
            public string FileName { get; set; } = "";
            public string DownloadUrl { get; set; } = "";
            public long FileSize { get; set; } = 0;
        }

        private async void TestDownloadUrl(string updateId)
        {
            try
            {
                var downloadUrl = $"https://api.uupdump.net/get.php?id={updateId}&type=download&autodl=1";
                lblStatus.Text = $"Testing URL: {downloadUrl}";

                using var response = await _httpClient.GetAsync(downloadUrl);
                
                var contentType = response.Content.Headers.ContentType?.MediaType ?? "unknown";
                var contentLength = response.Content.Headers.ContentLength;
                var statusCode = response.StatusCode;

                var info = $"Status: {statusCode}\nContent-Type: {contentType}\nContent-Length: {contentLength} bytes\nURL: {downloadUrl}";

                // Read the full response to see the metadata structure
                var responseContent = await response.Content.ReadAsStringAsync();
                
                info += $"\n\nFull Response (first 2000 chars):\n{responseContent.Substring(0, Math.Min(2000, responseContent.Length))}";

                if (responseContent.Length > 2000)
                {
                    info += $"\n\n... (truncated, total length: {responseContent.Length} characters)";
                }

                MessageBox.Show(info, "Download URL Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Test failed: {ex.Message}", "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeFilterControls()
        {
            // Remove the old API source controls
            var controlsToRemove = new List<Control>();
            foreach (Control control in panel1.Controls)
            {
                if (control is Label lbl && lbl.Text == "API Source:")
                    controlsToRemove.Add(lbl);
                if (control is ComboBox)
                    controlsToRemove.Add(control);
            }
            foreach (var control in controlsToRemove)
            {
                panel1.Controls.Remove(control);
            }

            // Add filter controls
            var lblFilters = new Label
            {
                Text = "Filters:",
                Location = new Point(180, 55),
                AutoSize = true
            };

            var btnAddFilter = new Button
            {
                Text = "Add Filter",
                Location = new Point(250, 52),
                Size = new Size(80, 25)
            };
            btnAddFilter.Click += BtnAddFilter_Click;

            var btnSaveFilters = new Button
            {
                Text = "Save Filters",
                Location = new Point(340, 52),
                Size = new Size(80, 25)
            };
            btnSaveFilters.Click += BtnSaveFilters_Click;

            var btnLoadFilters = new Button
            {
                Text = "Load Filters",
                Location = new Point(430, 52),
                Size = new Size(80, 25)
            };
            btnLoadFilters.Click += BtnLoadFilters_Click;

            panel1.Controls.Add(lblFilters);
            panel1.Controls.Add(btnAddFilter);
            panel1.Controls.Add(btnSaveFilters);
            panel1.Controls.Add(btnLoadFilters);
        }

        private void BtnAddFilter_Click(object sender, EventArgs e)
        {
            using var filterForm = new FilterForm();
            if (filterForm.ShowDialog() == DialogResult.OK)
            {
                _filters.Add(filterForm.Filter);
                UpdateFilterDisplay();
            }
        }

        private void BtnSaveFilters_Click(object sender, EventArgs e)
        {
            try
            {
                var json = JsonSerializer.Serialize(_filters, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(FILTER_FILE, json);
                MessageBox.Show("Filters saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving filters: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnLoadFilters_Click(object sender, EventArgs e)
        {
            try
            {
                if (File.Exists(FILTER_FILE))
                {
                    var json = File.ReadAllText(FILTER_FILE);
                    _filters = JsonSerializer.Deserialize<List<UpdateFilter>>(json) ?? new List<UpdateFilter>();
                    UpdateFilterDisplay();
                    MessageBox.Show("Filters loaded successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("No saved filters found.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading filters: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateFilterDisplay()
        {
            // Update status to show active filters
            var filterText = _filters.Count > 0 
                ? $"Active filters: {string.Join(", ", _filters.Select(f => f.Name))}"
                : "No filters active - showing all updates";
            lblStatus.Text = filterText;
        }

        private void LoadFilters()
        {
            try
            {
                if (File.Exists(FILTER_FILE))
                {
                    var json = File.ReadAllText(FILTER_FILE);
                    _filters = JsonSerializer.Deserialize<List<UpdateFilter>>(json) ?? new List<UpdateFilter>();
                    UpdateFilterDisplay();
                }
            }
            catch (Exception ex)
            {
                // Silently handle load errors
                _filters = new List<UpdateFilter>();
            }
        }

        private async void btnFetchUpdates_Click(object sender, EventArgs e)
        {
            await FetchUpdatesAsync();
        }

        private async Task FetchUpdatesAsync()
        {
            try
            {
                // Update UI to show loading state
                btnFetchUpdates.Enabled = false;
                lblStatus.Text = "Checking for kbupdate module...";
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                lstUpdates.Items.Clear();

                // Check if kbupdate module is available
                var moduleAvailable = await CheckKbUpdateModule();
                if (!moduleAvailable)
                {
                    ShowKbUpdateSetupInstructions();
                    return;
                }

                lblStatus.Text = "Fetching Windows updates from Microsoft Update Catalog...";

                // Fetch updates from Microsoft Update Catalog using kbupdate
                var updates = await FetchUpdatesFromMicrosoftCatalog();

                if (updates != null && updates.Count > 0)
                {
                    // Apply filters
                    var filteredUpdates = ApplyFilters(updates);

                    foreach (var update in filteredUpdates)
                    {
                        var item = new ListViewItem(update.Title);
                        item.SubItems.Add(update.UpdateId);
                        item.SubItems.Add(update.CreatedDate);
                        item.SubItems.Add(update.BuildNumber);
                        item.Tag = update;
                        lstUpdates.Items.Add(item);
                    }

                    lblStatus.Text = $"Found {filteredUpdates.Count} updates (filtered from {updates.Count} total)";
                }
                else
                {
                    lblStatus.Text = "No updates found";
                    MessageBox.Show("No updates were found. This could be due to:\n\n" +
                        "• Network connectivity issues\n" +
                        "• Microsoft Update Catalog being temporarily unavailable\n" +
                        "• No new cumulative updates available\n\n" +
                        "Please try again later.", "No Updates Found", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Error fetching updates";
                MessageBox.Show($"Error fetching updates: {ex.Message}\n\nPlease check your internet connection and try again.",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Reset UI state
                btnFetchUpdates.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private async Task<bool> CheckKbUpdateModule()
        {
            try
            {
                // First, try to import the module normally
                var script = @"
                    try {
                        Import-Module kbupdate -ErrorAction Stop
                        Write-Output 'SUCCESS: kbupdate module is available'
                    }
                    catch {
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }
                ";

                var result = await ExecutePowerShellScript(script);
                if (result.Contains("SUCCESS:"))
                {
                    return true;
                }

                // If not available, try to install it automatically
                lblStatus.Text = "Installing kbupdate module...";
                return await InstallKbUpdateModule();
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> InstallKbUpdateModule()
        {
            try
            {
                // Create a modules directory in the application folder
                var appPath = Application.StartupPath;
                var modulesPath = Path.Combine(appPath, "Modules");
                var kbUpdatePath = Path.Combine(modulesPath, "kbupdate");

                // Check if we already have the module bundled
                if (Directory.Exists(kbUpdatePath))
                {
                    lblStatus.Text = "Loading bundled kbupdate module...";
                    var script = $@"
                        try {{
                            $env:PSModulePath = '{modulesPath};' + $env:PSModulePath
                            Import-Module kbupdate -ErrorAction Stop
                            Write-Output 'SUCCESS: Bundled kbupdate module loaded'
                        }}
                        catch {{
                            Write-Output ""ERROR: $($_.Exception.Message)""
                        }}
                    ";

                    var result = await ExecutePowerShellScript(script);
                    if (result.Contains("SUCCESS:"))
                    {
                        return true;
                    }
                }

                // Try to download and install the module
                lblStatus.Text = "Downloading kbupdate module from PowerShell Gallery...";

                var installScript = $@"
                    try {{
                        # Create modules directory
                        $modulesPath = '{modulesPath}'
                        if (-not (Test-Path $modulesPath)) {{
                            New-Item -Path $modulesPath -ItemType Directory -Force | Out-Null
                        }}

                        # Try to save the module to our local directory
                        Save-Module -Name kbupdate -Path $modulesPath -Force -ErrorAction Stop

                        # Add our modules path to PSModulePath and import
                        $env:PSModulePath = '$modulesPath;' + $env:PSModulePath
                        Import-Module kbupdate -ErrorAction Stop

                        Write-Output 'SUCCESS: kbupdate module downloaded and installed'
                    }}
                    catch {{
                        # Fallback: try to install to current user scope
                        try {{
                            Install-Module kbupdate -Force -Scope CurrentUser -ErrorAction Stop
                            Import-Module kbupdate -ErrorAction Stop
                            Write-Output 'SUCCESS: kbupdate module installed to user scope'
                        }}
                        catch {{
                            Write-Output ""ERROR: $($_.Exception.Message)""
                        }}
                    }}
                ";

                var installResult = await ExecutePowerShellScript(installScript);
                return installResult.Contains("SUCCESS:");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to install kbupdate module: {ex.Message}");
                return false;
            }
        }

        private async Task<List<WindowsUpdate>> FetchUpdatesFromMicrosoftCatalog()
        {
            try
            {
                var updates = new List<WindowsUpdate>();

                // Prepare module path
                var appPath = Application.StartupPath;
                var modulesPath = Path.Combine(appPath, "Modules");

                // Get cumulative updates for Windows 11 from the last 60 days
                var script = $@"
                    try {{
                        # Add bundled modules path to PSModulePath if it exists
                        $bundledModulesPath = '{modulesPath}'
                        if (Test-Path $bundledModulesPath) {{
                            $env:PSModulePath = '$bundledModulesPath;' + $env:PSModulePath
                        }}

                        Import-Module kbupdate -ErrorAction Stop

                        # Get recent cumulative updates for Windows 11
                        $since = (Get-Date).AddDays(-60)
                        $updates = Get-KbUpdate -Since $since -Pattern 'Windows 11' -Architecture x64 |
                                   Where-Object {{ $_.Classification -eq 'Updates' -and $_.Title -like '*Cumulative*' }} |
                                   Select-Object -First 20

                        foreach ($update in $updates) {{
                            $json = @{{
                                Title = $update.Title
                                UpdateId = $update.UpdateId
                                KB = $update.KB
                                Created = $update.Created.ToString('yyyy-MM-dd HH:mm:ss')
                                Build = $update.Build
                                Architecture = $update.Architecture
                                Classification = $update.Classification
                                Link = $update.Link
                                Size = $update.Size
                            }} | ConvertTo-Json -Compress
                            Write-Output ""UPDATE_JSON:$json""
                        }}

                        Write-Output 'SUCCESS: Updates retrieved'
                    }}
                    catch {{
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }}
                ";

                var result = await ExecutePowerShellScript(script);
                var lines = result.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                foreach (var line in lines)
                {
                    if (line.StartsWith("UPDATE_JSON:"))
                    {
                        try
                        {
                            var json = line.Substring("UPDATE_JSON:".Length);
                            var updateData = JObject.Parse(json);

                            var update = new WindowsUpdate
                            {
                                Title = updateData["Title"]?.ToString() ?? "",
                                UpdateId = updateData["UpdateId"]?.ToString() ?? updateData["KB"]?.ToString() ?? "",
                                CreatedDate = updateData["Created"]?.ToString() ?? "",
                                BuildNumber = updateData["Build"]?.ToString() ?? "",
                                Architecture = updateData["Architecture"]?.ToString() ?? "",
                                Classification = updateData["Classification"]?.ToString() ?? "",
                                DownloadUrl = updateData["Link"]?.ToString() ?? "",
                                Size = updateData["Size"]?.Value<long?>() ?? 0
                            };

                            updates.Add(update);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Failed to parse update JSON: {ex.Message}");
                        }
                    }
                    else if (line.StartsWith("ERROR:"))
                    {
                        throw new Exception(line.Replace("ERROR:", "").Trim());
                    }
                }

                return updates;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to fetch updates from Microsoft Update Catalog: {ex.Message}");
            }
        }

        private List<WindowsUpdate> ApplyFilters(List<WindowsUpdate> updates)
        {
            if (_filters.Count == 0) return updates; // No filters = show all

            var filteredUpdates = new List<WindowsUpdate>();

            foreach (var update in updates)
            {
                bool shouldInclude = true;

                foreach (var filter in _filters)
                {
                    bool matches = true;

                    // Check title filters
                    if (!string.IsNullOrEmpty(filter.TitleContains) && !update.Title.Contains(filter.TitleContains, StringComparison.OrdinalIgnoreCase))
                        matches = false;

                    if (!string.IsNullOrEmpty(filter.TitleExcludes) && update.Title.Contains(filter.TitleExcludes, StringComparison.OrdinalIgnoreCase))
                        matches = false;

                    // Check build number filters
                    if (!string.IsNullOrEmpty(filter.BuildContains) && !update.BuildNumber.Contains(filter.BuildContains))
                        matches = false;

                    if (!string.IsNullOrEmpty(filter.BuildExcludes) && update.BuildNumber.Contains(filter.BuildExcludes))
                        matches = false;

                    // Check architecture filters
                    if (!string.IsNullOrEmpty(filter.Architecture) && !update.Architecture.Equals(filter.Architecture, StringComparison.OrdinalIgnoreCase))
                        matches = false;

                    // If any filter doesn't match, exclude this update
                    if (!matches)
                    {
                        shouldInclude = false;
                        break;
                    }
                }

                if (shouldInclude)
                {
                    filteredUpdates.Add(update);
                }
            }

            return filteredUpdates;
        }

        private bool ParseAndFilterUpdates(JObject data, out int count)
        {
            count = 0;
            
            if (data["response"]?["builds"] != null)
            {
                var builds = data["response"]?["builds"];
                
                if (builds != null)
                {
                    foreach (JProperty build in builds)
                    {
                        var buildData = build.Value;
                        if (buildData != null)
                        {
                            // Apply filters
                            if (ShouldIncludeUpdate(buildData))
                            {
                                var item = new ListViewItem(buildData["title"]?.ToString() ?? "N/A");
                                item.SubItems.Add(buildData["uuid"]?.ToString() ?? "N/A");
                                
                                // Convert Unix timestamp to readable date
                                var createdTimestamp = buildData["created"]?.Value<long?>();
                                var createdDate = createdTimestamp.HasValue 
                                    ? DateTimeOffset.FromUnixTimeSeconds(createdTimestamp.Value).DateTime.ToString("yyyy-MM-dd HH:mm:ss")
                                    : "N/A";
                                item.SubItems.Add(createdDate);
                                
                                item.SubItems.Add(buildData["build"]?.ToString() ?? "N/A");

                                lstUpdates.Items.Add(item);
                                count++;
                            }
                        }
                    }
                }
                return true;
            }
            return false;
        }

        private bool ShouldIncludeUpdate(JToken buildData)
        {
            if (_filters.Count == 0) return true; // No filters = show all

            var title = buildData["title"]?.ToString() ?? "";
            var build = buildData["build"]?.ToString() ?? "";
            var arch = buildData["arch"]?.ToString() ?? "";

            foreach (var filter in _filters)
            {
                bool matches = true;

                // Check title filters
                if (!string.IsNullOrEmpty(filter.TitleContains) && !title.Contains(filter.TitleContains, StringComparison.OrdinalIgnoreCase))
                    matches = false;

                if (!string.IsNullOrEmpty(filter.TitleExcludes) && title.Contains(filter.TitleExcludes, StringComparison.OrdinalIgnoreCase))
                    matches = false;

                // Check build number filters
                if (!string.IsNullOrEmpty(filter.BuildContains) && !build.Contains(filter.BuildContains))
                    matches = false;

                if (!string.IsNullOrEmpty(filter.BuildExcludes) && build.Contains(filter.BuildExcludes))
                    matches = false;

                // Check architecture filters
                if (!string.IsNullOrEmpty(filter.Architecture) && !arch.Equals(filter.Architecture, StringComparison.OrdinalIgnoreCase))
                    matches = false;

                // If any filter doesn't match, exclude this update
                if (!matches) return false;
            }

            return true; // All filters passed
        }

        private void BtnConvertToIso_Click(object sender, EventArgs e)
        {
            // Show file dialog to select a downloaded file for installation
            var openDialog = new OpenFileDialog
            {
                Title = "Select Windows Update package to install",
                Filter = "ZIP files (*.zip)|*.zip|Update files (*.msix;*.cab;*.wim;*.msu)|*.msix;*.cab;*.wim;*.msu|All files (*.*)|*.*",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (openDialog.ShowDialog() == DialogResult.OK)
            {
                var filePath = openDialog.FileName;
                var fileName = Path.GetFileName(filePath);

                // Show confirmation dialog
                var result = MessageBox.Show(
                    $"This will install Windows Updates from:\n{fileName}\n\n" +
                    "IMPORTANT:\n" +
                    "• This requires Administrator privileges\n" +
                    "• A system restart may be required\n" +
                    "• Make sure to close other applications\n\n" +
                    "Do you want to continue?",
                    "Install Windows Updates",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Start installation
                    _ = InstallUpdates(filePath, fileName);
                }
            }
        }

        private async Task InstallMsuFile(string msuFilePath)
        {
            try
            {
                lblStatus.Text = "Installing Windows Update...";
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                var script = $@"
                    try {{
                        # Install MSU file using wusa.exe
                        Start-Process -FilePath 'wusa.exe' -ArgumentList '{msuFilePath}', '/quiet', '/norestart' -Wait -NoNewWindow
                        Write-Output 'SUCCESS: Update installed successfully'
                    }}
                    catch {{
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }}
                ";

                var result = await ExecutePowerShellScript(script);

                if (result.Contains("SUCCESS:"))
                {
                    lblStatus.Text = "Update installed successfully!";
                    MessageBox.Show($"Windows Update installed successfully!\n\nFile: {Path.GetFileName(msuFilePath)}\n\nA system restart may be required to complete the installation.",
                        "Installation Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    throw new Exception(result.Replace("ERROR:", "").Trim());
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = "Installation failed";
                MessageBox.Show($"Failed to install Windows Update: {ex.Message}\n\nYou may need to run this application as Administrator.",
                    "Installation Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async Task<string> ExecutePowerShellScript(string script)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-ExecutionPolicy Bypass -Command \"{script.Replace("\"", "\\\"")}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    if (!string.IsNullOrEmpty(error))
                    {
                        Console.WriteLine($"PowerShell Error: {error}");
                    }

                    return output + (string.IsNullOrEmpty(error) ? "" : $"\nErrors: {error}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to execute PowerShell script: {ex.Message}");
            }
        }

        private bool IsAria2Available()
        {
            var aria2cPath = Path.Combine(Application.StartupPath, "aria2c.exe");
            return File.Exists(aria2cPath);
        }

        private void ShowKbUpdateSetupInstructions()
        {
            var result = MessageBox.Show(
                @"The kbupdate PowerShell module is required but could not be installed automatically.

This could be due to:
• No internet connection
• PowerShell execution policy restrictions
• Network proxy settings

Would you like to try manual installation?

Manual Setup Instructions:
1. Open PowerShell as Administrator
2. Run: Install-Module kbupdate -Force
3. Restart this application

The kbupdate module allows this tool to:
• Search the Microsoft Update Catalog
• Download actual MSU/EXE update files
• Install updates properly on your system",
                "kbupdate Module Required",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // Try the installation process again
                _ = Task.Run(async () =>
                {
                    var success = await InstallKbUpdateModule();
                    if (success)
                    {
                        Invoke(new Action(() =>
                        {
                            lblStatus.Text = "kbupdate module installed successfully!";
                            MessageBox.Show("kbupdate module installed successfully! You can now fetch updates.",
                                "Installation Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }));
                    }
                    else
                    {
                        Invoke(new Action(() =>
                        {
                            lblStatus.Text = "Failed to install kbupdate module";
                            MessageBox.Show("Failed to install kbupdate module. Please install manually.",
                                "Installation Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }));
                    }
                });
            }
        }

        private void ShowAria2SetupInstructions()
        {
            var message = @"aria2c.exe is required for downloading UUP files.

Setup Instructions:
1. Download aria2c.exe from: https://uupdump.net/misc/aria2c.exe
2. Place aria2c.exe in the same directory as this application
3. Restart the application

Alternatively, you can use the 'Install Updates' button to manually install downloaded update files.";

            MessageBox.Show(message, "aria2c.exe Required", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    public class WindowsUpdate
    {
        public string Title { get; set; } = "";
        public string UpdateId { get; set; } = "";
        public string CreatedDate { get; set; } = "";
        public string BuildNumber { get; set; } = "";
        public string Architecture { get; set; } = "";
        public string Classification { get; set; } = "";
        public string DownloadUrl { get; set; } = "";
        public long Size { get; set; }
    }

    public class UpdateFilter
    {
        public string Name { get; set; } = "";
        public string TitleContains { get; set; } = "";
        public string TitleExcludes { get; set; } = "";
        public string BuildContains { get; set; } = "";
        public string BuildExcludes { get; set; } = "";
        public string Architecture { get; set; } = "";
    }
}
