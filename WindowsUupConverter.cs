using System;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Text.Json;

namespace WindowsUpdateTool
{
    public class WindowsUpdateInstaller
    {
        private readonly string _updatesDirectory;
        private readonly Action<string> _statusCallback;

        public WindowsUpdateInstaller(string updatesDirectory, Action<string>? statusCallback = null)
        {
            _updatesDirectory = updatesDirectory;
            _statusCallback = statusCallback ?? (_ => { });
        }

        public async Task<InstallationResult> InstallUpdatesAsync()
        {
            try
            {
                _statusCallback("Analyzing update files...");

                // Step 1: Analyze available update files
                var updateFiles = AnalyzeUpdateFiles();

                if (updateFiles.Count == 0)
                {
                    return new InstallationResult { Success = false, Message = "No installable update files found." };
                }

                _statusCallback($"Found {updateFiles.Count} update files to install...");

                // Step 2: Install updates by type
                var results = new List<string>();

                foreach (var updateFile in updateFiles)
                {
                    var result = await InstallUpdateFile(updateFile);
                    results.Add(result);
                }

                return new InstallationResult
                {
                    Success = true,
                    Message = $"Installation completed. {results.Count} updates processed.",
                    Details = results
                };
            }
            catch (Exception ex)
            {
                return new InstallationResult { Success = false, Message = $"Installation failed: {ex.Message}" };
            }
        }

        private List<UpdateFile> AnalyzeUpdateFiles()
        {
            var updateFiles = new List<UpdateFile>();

            if (!Directory.Exists(_updatesDirectory))
            {
                return updateFiles;
            }

            foreach (var file in Directory.GetFiles(_updatesDirectory, "*.*", SearchOption.AllDirectories))
            {
                var fileName = Path.GetFileName(file).ToLower();
                var fileInfo = new FileInfo(file);

                if (fileName.EndsWith(".msix"))
                {
                    updateFiles.Add(new UpdateFile
                    {
                        FilePath = file,
                        FileName = Path.GetFileName(file),
                        Type = UpdateFileType.MSIX,
                        Size = fileInfo.Length
                    });
                }
                else if (fileName.EndsWith(".cab"))
                {
                    updateFiles.Add(new UpdateFile
                    {
                        FilePath = file,
                        FileName = Path.GetFileName(file),
                        Type = UpdateFileType.CAB,
                        Size = fileInfo.Length
                    });
                }
                else if (fileName.EndsWith(".wim"))
                {
                    updateFiles.Add(new UpdateFile
                    {
                        FilePath = file,
                        FileName = Path.GetFileName(file),
                        Type = UpdateFileType.WIM,
                        Size = fileInfo.Length
                    });
                }
                else if (fileName.EndsWith(".msu"))
                {
                    updateFiles.Add(new UpdateFile
                    {
                        FilePath = file,
                        FileName = Path.GetFileName(file),
                        Type = UpdateFileType.MSU,
                        Size = fileInfo.Length
                    });
                }
                else if (fileName.EndsWith(".psf"))
                {
                    updateFiles.Add(new UpdateFile
                    {
                        FilePath = file,
                        FileName = Path.GetFileName(file),
                        Type = UpdateFileType.PSF,
                        Size = fileInfo.Length
                    });
                }
            }

            return updateFiles;
        }

        private async Task<string> InstallUpdateFile(UpdateFile updateFile)
        {
            try
            {
                _statusCallback($"Installing {updateFile.FileName}...");

                switch (updateFile.Type)
                {
                    case UpdateFileType.MSIX:
                        return await InstallMsixPackage(updateFile);

                    case UpdateFileType.CAB:
                        return await InstallCabFile(updateFile);

                    case UpdateFileType.WIM:
                        return await ProcessWimFile(updateFile);

                    case UpdateFileType.MSU:
                        return await InstallMsuFile(updateFile);

                    case UpdateFileType.PSF:
                        return await ProcessPsfFile(updateFile);

                    default:
                        return $"Skipped {updateFile.FileName} - Unknown file type";
                }
            }
            catch (Exception ex)
            {
                return $"Failed to install {updateFile.FileName}: {ex.Message}";
            }
        }

        private async Task<string> InstallMsixPackage(UpdateFile updateFile)
        {
            try
            {
                var script = $@"
                    try {{
                        Add-AppxPackage -Path '{updateFile.FilePath}' -ForceApplicationShutdown
                        Write-Output 'SUCCESS: MSIX package installed successfully'
                    }}
                    catch {{
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }}
                ";

                var result = await ExecutePowerShellScript(script);

                if (result.Contains("SUCCESS:"))
                {
                    return $"Successfully installed MSIX package: {updateFile.FileName}";
                }
                else if (result.Contains("ERROR:"))
                {
                    return $"Failed to install MSIX package {updateFile.FileName}: {result.Replace("ERROR:", "").Trim()}";
                }
                else
                {
                    return $"MSIX package {updateFile.FileName} installation completed with warnings: {result}";
                }
            }
            catch (Exception ex)
            {
                return $"Failed to install MSIX package {updateFile.FileName}: {ex.Message}";
            }
        }

        private async Task<string> InstallCabFile(UpdateFile updateFile)
        {
            try
            {
                var script = $@"
                    try {{
                        # Use DISM to install CAB file
                        $result = dism /online /add-package /packagepath:'{updateFile.FilePath}' /quiet /norestart
                        if ($LASTEXITCODE -eq 0) {{
                            Write-Output 'SUCCESS: CAB file installed successfully'
                        }} else {{
                            Write-Output ""ERROR: DISM failed with exit code $LASTEXITCODE""
                        }}
                    }}
                    catch {{
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }}
                ";

                var result = await ExecutePowerShellScript(script);

                if (result.Contains("SUCCESS:"))
                {
                    return $"Successfully installed CAB file: {updateFile.FileName}";
                }
                else
                {
                    return $"Failed to install CAB file {updateFile.FileName}: {result.Replace("ERROR:", "").Trim()}";
                }
            }
            catch (Exception ex)
            {
                return $"Failed to install CAB file {updateFile.FileName}: {ex.Message}";
            }
        }

        private async Task<string> ProcessWimFile(UpdateFile updateFile)
        {
            try
            {
                // WIM files typically contain Windows images that need special handling
                // For now, we'll just report that we found them
                return $"Found WIM file: {updateFile.FileName} ({updateFile.Size / (1024 * 1024):F1} MB) - Manual installation may be required";
            }
            catch (Exception ex)
            {
                return $"Failed to process WIM file {updateFile.FileName}: {ex.Message}";
            }
        }

        private async Task<string> InstallMsuFile(UpdateFile updateFile)
        {
            try
            {
                var script = $@"
                    try {{
                        # Use wusa.exe to install MSU file
                        Start-Process -FilePath 'wusa.exe' -ArgumentList '{updateFile.FilePath}', '/quiet', '/norestart' -Wait -NoNewWindow
                        Write-Output 'SUCCESS: MSU file installed successfully'
                    }}
                    catch {{
                        Write-Output ""ERROR: $($_.Exception.Message)""
                    }}
                ";

                var result = await ExecutePowerShellScript(script);

                if (result.Contains("SUCCESS:"))
                {
                    return $"Successfully installed MSU file: {updateFile.FileName}";
                }
                else
                {
                    return $"Failed to install MSU file {updateFile.FileName}: {result.Replace("ERROR:", "").Trim()}";
                }
            }
            catch (Exception ex)
            {
                return $"Failed to install MSU file {updateFile.FileName}: {ex.Message}";
            }
        }

        private async Task<string> ProcessPsfFile(UpdateFile updateFile)
        {
            try
            {
                // PSF files are typically metadata files, not directly installable
                return $"Found PSF metadata file: {updateFile.FileName} - This is a metadata file, not directly installable";
            }
            catch (Exception ex)
            {
                return $"Failed to process PSF file {updateFile.FileName}: {ex.Message}";
            }
        }

        private async Task<string> ExecutePowerShellScript(string script)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-ExecutionPolicy Bypass -Command \"{script.Replace("\"", "\\\"")}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas" // Request admin privileges
                };

                using (var process = new Process { StartInfo = startInfo })
                {
                    process.Start();
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    await process.WaitForExitAsync();

                    if (!string.IsNullOrEmpty(error))
                    {
                        _statusCallback($"PowerShell Warning: {error}");
                    }

                    return output + (string.IsNullOrEmpty(error) ? "" : $"\nErrors: {error}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to execute PowerShell script: {ex.Message}");
            }
        }
    }

    public class UpdateFile
    {
        public string FilePath { get; set; } = "";
        public string FileName { get; set; } = "";
        public UpdateFileType Type { get; set; }
        public long Size { get; set; }
    }

    public enum UpdateFileType
    {
        MSIX,
        CAB,
        WIM,
        MSU,
        PSF,
        Unknown
    }

    public class InstallationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public List<string> Details { get; set; } = new List<string>();
    }
}