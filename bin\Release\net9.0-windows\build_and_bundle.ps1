# PowerShell script to build the Windows Update Tool and bundle it with kbupdate module
# This creates a complete, portable distribution

Write-Host "Building Windows Update Tool with bundled kbupdate module..." -ForegroundColor Green

try {
    # Get the project directory
    $projectPath = Split-Path -Parent $MyInvocation.MyCommand.Path
    $outputPath = Join-Path $projectPath "dist"
    $binPath = Join-Path $projectPath "bin\Debug\net9.0-windows"
    
    Write-Host "Project path: $projectPath" -ForegroundColor Cyan
    Write-Host "Output path: $outputPath" -ForegroundColor Cyan
    
    # Clean and create output directory
    if (Test-Path $outputPath) {
        Write-Host "Cleaning existing distribution..." -ForegroundColor Yellow
        Remove-Item -Path $outputPath -Recurse -Force
    }
    New-Item -Path $outputPath -ItemType Directory -Force | Out-Null
    
    # Build the application
    Write-Host "Building application..." -ForegroundColor Yellow
    Set-Location $projectPath
    $buildResult = dotnet build --configuration Release
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed with exit code $LASTEXITCODE"
    }
    
    Write-Host "✓ Application built successfully!" -ForegroundColor Green
    
    # Copy application files
    Write-Host "Copying application files..." -ForegroundColor Yellow
    $releasePath = Join-Path $projectPath "bin\Release\net9.0-windows"
    
    if (-not (Test-Path $releasePath)) {
        throw "Release build not found at $releasePath"
    }
    
    Copy-Item -Path "$releasePath\*" -Destination $outputPath -Recurse -Force
    
    # Download and bundle kbupdate module
    Write-Host "Bundling kbupdate module..." -ForegroundColor Yellow
    $modulesPath = Join-Path $outputPath "Modules"
    New-Item -Path $modulesPath -ItemType Directory -Force | Out-Null
    
    Save-Module -Name kbupdate -Path $modulesPath -Force
    
    # Verify the module was bundled
    $kbUpdatePath = Join-Path $modulesPath "kbupdate"
    if (Test-Path $kbUpdatePath) {
        $moduleFiles = Get-ChildItem -Path $kbUpdatePath -Recurse | Measure-Object
        Write-Host "✓ kbupdate module bundled!" -ForegroundColor Green
        Write-Host "  Files: $($moduleFiles.Count)" -ForegroundColor Cyan
    } else {
        throw "Failed to bundle kbupdate module"
    }
    
    # Copy additional files
    Write-Host "Copying additional files..." -ForegroundColor Yellow
    $additionalFiles = @(
        "README.md",
        "install_kbupdate.ps1",
        "bundle_kbupdate.ps1"
    )
    
    foreach ($file in $additionalFiles) {
        $sourcePath = Join-Path $projectPath $file
        if (Test-Path $sourcePath) {
            Copy-Item -Path $sourcePath -Destination $outputPath -Force
            Write-Host "  ✓ Copied $file" -ForegroundColor Cyan
        }
    }
    
    # Create a portable launcher script
    $launcherScript = @"
@echo off
echo Starting Windows Update Tool...
echo.
echo This tool includes the kbupdate PowerShell module for accessing
echo the Microsoft Update Catalog and downloading real Windows updates.
echo.
echo If you encounter any issues, please run as Administrator.
echo.
pause
start WindowsUpdateTool.exe
"@
    
    $launcherPath = Join-Path $outputPath "Start_WindowsUpdateTool.bat"
    Set-Content -Path $launcherPath -Value $launcherScript -Encoding ASCII
    
    # Get distribution size
    $distSize = (Get-ChildItem -Path $outputPath -Recurse | Measure-Object -Property Length -Sum).Sum
    $distSizeMB = [math]::Round($distSize / 1MB, 2)
    
    Write-Host "`n🎉 Distribution created successfully!" -ForegroundColor Green
    Write-Host "Location: $outputPath" -ForegroundColor Cyan
    Write-Host "Size: $distSizeMB MB" -ForegroundColor Cyan
    Write-Host "`nContents:" -ForegroundColor Yellow
    Write-Host "• WindowsUpdateTool.exe - Main application" -ForegroundColor White
    Write-Host "• Modules\kbupdate\ - Bundled PowerShell module" -ForegroundColor White
    Write-Host "• Start_WindowsUpdateTool.bat - Launcher script" -ForegroundColor White
    Write-Host "• README.md - Documentation" -ForegroundColor White
    Write-Host "• Supporting DLLs and configuration files" -ForegroundColor White
    
    Write-Host "`n📦 Ready for distribution!" -ForegroundColor Green
    Write-Host "This package can be copied to any Windows system and will work without" -ForegroundColor Green
    Write-Host "requiring separate installation of the kbupdate module." -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error creating distribution: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
