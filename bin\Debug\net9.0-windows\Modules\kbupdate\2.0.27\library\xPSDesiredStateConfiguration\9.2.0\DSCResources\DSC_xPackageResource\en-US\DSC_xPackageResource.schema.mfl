[Description("The xPackage resource is used to install or uninstall a package on the host.") : Amended,AMENDMENT, LOCALE("MS_409")]
class DSC_xPackageResource : OMI_BaseResource
{
  [Key, Description("The name of the package to be added or removed.") : Amended] string Name;
  [Key, Description("The identifying number used to uniquely identify this package.") : Amended] string ProductId;
  [Required, Description("The path, URL or UNC path to the package.") : Amended] string Path;
  [Description("Indicates whether to Ensure that the package is 'Present' or 'Absent'. Defaults to 'Present'.") : Amended] string Ensure;
  [Description("The arguments to be passed to the package during addition or removal. When installing MSI packages, the '/quiet' and '/norestart' arguments are automatically applied.") : Amended] string Arguments;
  [Description("The credentials to be used for mounting the UNC path (if applicable).") : Amended] string Credential;
  [Description("The list of possible valid return codes for this install or removal.") : Amended] uint32 ReturnCode[];
  [Description("The path to log the output of the MSI or EXE.") : Amended] string LogPath;
  [Description("The expected hash value of the file found in the Path location.") : Amended] string FileHash;
  [Description("The algorithm used to generate the FileHash value. Defaults to 'SHA256'.") : Amended] string HashAlgorithm;
  [Description("The subject that must match the signer certificate of the digital signature. Wildcards are allowed.") : Amended] string SignerSubject;
  [Description("The certificate thumbprint which must match the signer certificate of the digital signature.") : Amended] string SignerThumbprint;
  [Description("PowerShell code used to validate SSL certificates of HTTPS url assigned to Path.") : Amended] string ServerCertificateValidationCallback;
  [Description("The hive in which to create the registry key. Defaults to 'LocalMachine'.") : Amended] string InstalledCheckRegHive;
  [Description("The registry key to validate the package is installed.") : Amended] string InstalledCheckRegKey;
  [Description("The registry value name to validate the package is installed.") : Amended] string InstalledCheckRegValueName;
  [Description("The registry value to validate the package is installed.") : Amended] string InstalledCheckRegValueData;
  [Description("Specifies if a registry value should be created when the packages is installed.") : Amended] boolean CreateCheckRegValue;
  [Description("Ignore a pending reboot if requested by package installation. The default value is $false and DSC will try to reboot the system.") : Amended] boolean IgnoreReboot;
  [Description("The credentials under which to run the installation.") : Amended] string RunAsCredential;
  [Description("The description of the identified package.") : Amended] string PackageDescription;
  [Description("The publisher for the identified package.") : Amended] string Publisher;
  [Description("The date that the identified package was last serviced or its install date, whichever is later.") : Amended] string InstalledOn;
  [Description("The size of the identified package.") : Amended] uint32 Size;
  [Description("The version number of the identified package.") : Amended] string Version;
  [Description("Whether the identified package is installed.") : Amended] boolean Installed;
};
