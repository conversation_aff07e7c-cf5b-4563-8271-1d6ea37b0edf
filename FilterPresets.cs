namespace WindowsUpdateTool
{
    /// <summary>
    /// Predefined filter presets for common scenarios
    /// </summary>
    public static class FilterPresets
    {
        public static List<UpdateFilter> GetPresets()
        {
            return new List<UpdateFilter>
            {
                new UpdateFilter
                {
                    Name = "Exclude Insider Builds",
                    TitleExcludes = "Insider"
                },
                new UpdateFilter
                {
                    Name = "Exclude Preview Builds",
                    TitleExcludes = "Preview"
                },
                new UpdateFilter
                {
                    Name = "Windows 11 Only",
                    TitleContains = "Windows 11"
                },
                new UpdateFilter
                {
                    Name = "Windows 10 Only",
                    TitleContains = "Windows 10"
                },
                new UpdateFilter
                {
                    Name = "x64 Architecture Only",
                    Architecture = "amd64"
                },
                new UpdateFilter
                {
                    Name = "ARM64 Architecture Only",
                    Architecture = "arm64"
                },
                new UpdateFilter
                {
                    Name = "Retail Builds Only",
                    TitleExcludes = "Insider"
                },
                new UpdateFilter
                {
                    Name = "Cumulative Updates Only",
                    TitleContains = "Cumulative Update"
                },
                new UpdateFilter
                {
                    Name = "Feature Updates Only",
                    TitleContains = "Feature update"
                },
                new UpdateFilter
                {
                    Name = "Recent Builds (2024+)",
                    BuildContains = "2024"
                }
            };
        }

        public static List<UpdateFilter> GetCommonExclusions()
        {
            return new List<UpdateFilter>
            {
                new UpdateFilter
                {
                    Name = "No Insider Builds",
                    TitleExcludes = "Insider"
                },
                new UpdateFilter
                {
                    Name = "No Preview Builds",
                    TitleExcludes = "Preview"
                },
                new UpdateFilter
                {
                    Name = "No Beta Builds",
                    TitleExcludes = "Beta"
                },
                new UpdateFilter
                {
                    Name = "No .NET Framework Updates",
                    TitleExcludes = ".NET Framework"
                }
            };
        }

        public static string GetFilterDescription(UpdateFilter filter)
        {
            var conditions = new List<string>();

            if (!string.IsNullOrEmpty(filter.TitleContains))
                conditions.Add($"Title contains '{filter.TitleContains}'");

            if (!string.IsNullOrEmpty(filter.TitleExcludes))
                conditions.Add($"Title excludes '{filter.TitleExcludes}'");

            if (!string.IsNullOrEmpty(filter.BuildContains))
                conditions.Add($"Build contains '{filter.BuildContains}'");

            if (!string.IsNullOrEmpty(filter.BuildExcludes))
                conditions.Add($"Build excludes '{filter.BuildExcludes}'");

            if (!string.IsNullOrEmpty(filter.Architecture))
                conditions.Add($"Architecture is '{filter.Architecture}'");

            return string.Join(" AND ", conditions);
        }
    }
} 