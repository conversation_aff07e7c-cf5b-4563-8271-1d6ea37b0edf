#requires -Version 3.0
$script:ModuleRoot = $PSScriptRoot

Import-Module "$PSScriptRoot/library/Microsoft.Deployment.Compression.Cab.dll", "$PSScriptRoot/library/Microsoft.Deployment.Compression.dll"

function Import-ModuleFile {
    <#
		.SYNOPSIS
			Loads files into the module on module import.

		.DESCRIPTION
			This helper function is used during module initialization.
			It should always be dotsourced itself, in order to proper function.

			This provides a central location to react to files being imported, if later desired

		.PARAMETER Path
			The path to the file to load

		.EXAMPLE
			PS C:\> . Import-ModuleFile -File $function.FullName

			Imports the file stored in $function according to import policy
	    #>
    [CmdletBinding()]
    Param (
        [string]
        $Path
    )

    if ($doDotSource) { . $Path }
    else { $ExecutionContext.InvokeCommand.InvokeScript($false, ([scriptblock]::Create([io.file]::ReadAllText($Path))), $null, $null) }
}

# Import all internal functions
foreach ($function in (Get-ChildItem "$ModuleRoot\private" -Filter "*.ps1" -Recurse -ErrorAction Ignore)) {
    . Import-ModuleFile -Path $function.FullName
}

# Import all public functions
foreach ($function in (Get-ChildItem "$ModuleRoot\public" -Filter "*.ps1" -Recurse -ErrorAction Ignore)) {
    . Import-ModuleFile -Path $function.FullName
}

# Setup initial collections
if (-not $script:kbcollection) {
    $script:kbcollection = [hashtable]::Synchronized(@{ })
}

if (-not $script:compcollection) {
    $script:compcollection = [hashtable]::Synchronized(@{ })
}

$script:languages = . "$ModuleRoot\library\languages.ps1"
$script:languagescsv = Import-Csv -Path "$ModuleRoot\library\languages.tsv" -Delimiter `t

$kblibmod = Get-Module -Name kbupdate-library | Select-Object -Last 1
if (-not $kblibmod) {
    throw "Could not find kbupdate-library module"
}
$kblib = Split-Path -Path ($kblibmod).Path
$script:basedb = (Get-ChildItem -Path "$kblib\*.sqlite" -Recurse).FullName

# This will help jobs + instances where kbupdate is not in the psmodulepath
$script:dependencies = @(Get-Module PSFramework, kbupdate-library, PSSQLite).Path
$script:dependencies += "$ModuleRoot\kbupdate.psm1"

if (-not $IsLinux -and -not $IsMacOs) {
    # for those of us who are loading the psm1 directly
    try {
        Import-Module -Name kbupdate-library -ErrorAction Stop
    } catch {
        throw "kbupdate-library is required to import this module"
    }
}

# Register autocompleters
Register-PSFTeppScriptblock -Name Architecture -ScriptBlock { "x64","x86","IA64","ARM64","ARM","ARM32" }
Register-PSFTeppScriptblock -Name OperatingSystem -ScriptBlock { "Windows Server 2022", "Windows Server 2019", "Windows Server 2016", "Windows 10", "Windows 8.1", "Windows Server 2012 R2", "Windows 8", "Windows Server 2012", "Windows Server 2012 Hyper-V", "Windows 7", "Windows Server 2008 R2", "Windows Vista", "Windows Server 2008", "Windows Small Business Server (SBS) 2008", "Windows Server 2003", "Windows Small Business Server (SBS) 2003", "Windows XP", "Windows XP Media Center Edition (MCE)", "Windows XP Tablet PC Edition", "Windows 2000", "Small Business Server (SBS) 2000", "Windows NT 4.0", "Windows Millennium Edition (ME)", "Windows 98 Second Edition (SE)", "Windows 98", "Windows 95", "Microsoft Windows Update", "Windows Embedded Compact 2013", "Windows Embedded Compact 7", "Windows Embedded CE 6.0", "Windows CE 5.0", "Windows CE .NET 4.2", "Windows CE .NET 4.1" }
Register-PSFTeppScriptblock -Name Product -ScriptBlock { "Exchange Server 2019", "Exchange Server 2016", "Exchange Server 2013", "Exchange Server 2010", "Exchange Server 2007", "Exchange Server 2003", "Exchange Server 2000", "Exchange Server 5.5", "Exchange Server 5.0", "Exchange Server 4.0", "Microsoft Office 365", "Outlook 2019", "Excel 2019", "Word 2019", "Access 2019", "Outlook 2016", "Excel 2016", "Word 2016", "Access 2016", "Outlook 2013", "Excel 2013", "Word 2013", "Access 2013", "Outlook 2010", "Excel 2010", "Word 2010", "Access 2010", "Outlook 2007", "Excel 2007", "Word 2007", "Access 2007", "PowerPoint 2007", "Visio 2007", "Publisher 2007", "Project 2007", "OneNote 2007", "InfoPath 2007", "Microsoft Office Groove 2007", "Outlook 2003", "Excel 2003", "Word 2003", "Access 2003", "PowerPoint 2003", "FrontPage 2003", "Visio 2003", "Publisher 2003", "Project 2003", "OneNote 2003", "InfoPath 2003", "Outlook 2002 (Outlook XP)", "Excel 2002 (Excel XP)", "Word 2002 (Word XP)", "Access 2002 (Access XP)", "PowerPoint 2002 (PowerPoint XP)", "FrontPage 2002 (FrontPage XP)", "Visio 2002 (Visio XP)", "Publisher 2002 (Publisher XP)", "Project 2002 (Project XP)", "Outlook 2000", "Excel 2000", "Word 2000", "Access 2000", "PowerPoint 2000", "FrontPage 2000", "Visio 2000", "Publisher 2000", "Project 2000", "Microsoft Office Live Meeting 2005", "Microsoft Works Suite 2003", "Microsoft Works Suite 2002", "Microsoft Works Suite 2001", "Microsoft Works 2000", "SharePoint Server 2019", "SharePoint Server 2016", "SharePoint Server 2013", "SharePoint Server 2010", "SharePoint Server 2007", "SharePoint Portal Server 2003", "SharePoint Portal Server 2001", "BizTalk Server 2006", "BizTalk Server 2004", "BizTalk Server 2002", "BizTalk Server 2000", "Internet Security and Acceleration (ISA) Server 2006", "Internet Security and Acceleration (ISA) Server 2004", "Internet Security and Acceleration (ISA) Server 2000", "System Center Essentials (SCE) 2010", "System Center Essentials (SCE) 2007", "System Center Operations Manager (SCOM) 2012", "System Center Virtual Machine Manager (SCVMM) 2012", "System Center Orchestrator (SCO) 2012", "System Center Service Manager (SCSM) 2012", "System Center Configuration Manager (SCCM) 2012", "System Center Configuration Manager (SCCM) 2007", "Systems Management Server (SMS) 2003", "Systems Management Server (SMS) 2.0", "Systems Management Server (SMS) 1.2", "Systems Management Server (SMS) 1.1", "Systems Management Server (SMS) 1.0", "SNA Server 4.0", "SNA Server 3.0", "System Center Operations Manager (SCOM) 2007", "Operations Manager (MOM) 2005", "Operations Manager (MOM) 2000", "Host Integration Server (HIS) 2004", "Host Integration Server (HIS) 2000", "Commerce Server 2007", "Commerce Server 2002", "Commerce Server 2000", "Dynamics CRM 3.0", "Zune", "Xbox 360", "Internet Explorer 11", "Internet Explorer 10", "Internet Explorer 9", "Internet Explorer 8", "Internet Explorer 7", "Internet Explorer 6", "Internet Explorer 5.5", "Internet Explorer 5.0", "SQL Server 2017", "SQL Server 2016", "SQL Server 2014", "SQL Server 2012", "SQL Server 2008 R2", "SQL Server 2008", "SQL Server 2005", "SQL Server 2000", "SQL Server 7.0", "Microsoft Data Access Components (MDAC) 2.8", "Microsoft Data Access Components (MDAC) 2.7", "Microsoft Data Access Components (MDAC) 2.6", "Microsoft Data Access Components (MDAC) 2.5", "Microsoft Data Access Components (MDAC) 2.1", "Visual FoxPro 9.0", "Visual FoxPro 8.0", "Visual FoxPro 7.0", "Visual FoxPro 6.0", ".NET Framework 4.7", ".NET Framework 4.6", ".NET Framework 4.5", ".NET Framework 4", ".NET Framework 3.5", ".NET Framework 3.0", ".NET Framework 2.0", ".NET Framework 1.1", ".NET Framework 1.0", "ASP.NET 2.0", "ASP.NET 1.1", "ASP.NET 1.0", "Visual Studio 2008", "Visual Studio 2005", "Visual C++ 2005", "Visual C# 2005", "Visual Basic 2005", "Visual Studio .NET 2003", "Visual C++ .NET 2003", "Visual C# .NET 2003", "Visual Basic .NET 2003", "Visual Studio .NET 2002", "Visual C++ .NET 2002", "Visual C# .NET 2002", "Visual Basic .NET 2002", "Visual Studio 6.0", "Visual C++ 6.0", "Visual Basic 6.0", "Windows Media Player 11", "Windows Media Player 10", "Windows Media Player 9", "Internet Information Services (IIS) 7.0", "Internet Information Services (IIS) 6.0", "Internet Information Services (IIS) 5.1", "Internet Information Services (IIS) 5.0", "Office Accounting 2007", "Small Business Accounting 2006", "Money 2007", "Money 2006", "Money 2005", "Money 2004", "Money 2003", "Money 2002", "Money 2001", "Visual SourceSafe 6.0", "Microsoft Encarta Encyclopedia 2000", "Age of Empires III (AoE3)", "Age of Empires II (AoE2)", "Age of Mythology", "Zoo Tycoon 2", "Zoo Tycoon", "Microsoft Mail for Appletalk Networks 3.1", "Microsoft Mail for Appletalk Networks 3.0" }
# Languge is a tough one
#Register-PSFTeppScriptblock -Name Language -ScriptBlock { [System.Globalization.CultureInfo]::GetCultures("AllCultures") | Where-Object Name -ne $null | Select-Object -ExpandProperty DisplayName }
Register-PSFTeppScriptblock -Name Language -ScriptBlock { "Afrikaans", "Afrikaans (South Africa)", "Arabic", "Arabic (U.A.E.)", "Arabic (Bahrain)", "Arabic (Algeria)", "Arabic (Egypt)", "Arabic (Iraq)", "Arabic (Jordan)", "Arabic (Kuwait)", "Arabic (Lebanon)", "Arabic (Libya)", "Arabic (Morocco)", "Arabic (Oman)", "Arabic (Qatar)", "Arabic (Saudi Arabia)", "Arabic (Syria)", "Arabic (Tunisia)", "Arabic (Yemen)", "Azeri (Latin)", "Azeri (Latin) (Azerbaijan)", "Azeri (Cyrillic) (Azerbaijan)", "Belarusian", "Belarusian (Belarus)", "Bulgarian", "Bulgarian (Bulgaria)", "Bosnian (Bosnia and Herzegovina)", "Catalan", "Catalan (Spain)", "Czech", "Czech (Czech Republic)", "Welsh", "Welsh (United Kingdom)", "Danish", "Danish (Denmark)", "German", "German (Austria)", "German (Switzerland)", "German (Germany)", "German (Liechtenstein)", "German (Luxembourg)", "Divehi", "Divehi (Maldives)", "Greek", "Greek (Greece)", "English", "English (Australia)", "English (Belize)", "English (Canada)", "English (Caribbean)", "English (United Kingdom)", "English (Ireland)", "English (Jamaica)", "English (New Zealand)", "English (Republic of the Philippines)", "English (Trinidad and Tobago)", "English (United States)", "English (South Africa)", "English (Zimbabwe)", "Esperanto", "Spanish", "Spanish (Argentina)", "Spanish (Bolivia)", "Spanish (Chile)", "Spanish (Colombia)", "Spanish (Costa Rica)", "Spanish (Dominican Republic)", "Spanish (Ecuador)", "Spanish (Castilian)", "Spanish (Spain)", "Spanish (Guatemala)", "Spanish (Honduras)", "Spanish (Mexico)", "Spanish (Nicaragua)", "Spanish (Panama)", "Spanish (Peru)", "Spanish (Puerto Rico)", "Spanish (Paraguay)", "Spanish (El Salvador)", "Spanish (Uruguay)", "Spanish (Venezuela)", "Estonian", "Estonian (Estonia)", "Basque", "Basque (Spain)", "Farsi", "Farsi (Iran)", "Finnish", "Finnish (Finland)", "Faroese", "Faroese (Faroe Islands)", "French", "French (Belgium)", "French (Canada)", "French (Switzerland)", "French (France)", "French (Luxembourg)", "French (Principality of Monaco)", "Galician", "Galician (Spain)", "Gujarati", "Gujarati (India)", "Hebrew", "Hebrew (Israel)", "Hindi", "Hindi (India)", "Croatian", "Croatian (Bosnia and Herzegovina)", "Croatian (Croatia)", "Hungarian", "Hungarian (Hungary)", "Armenian", "Armenian (Armenia)", "Indonesian", "Indonesian (Indonesia)", "Icelandic", "Icelandic (Iceland)", "Italian", "Italian (Switzerland)", "Italian (Italy)", "Japanese", "Japanese (Japan)", "Georgian", "Georgian (Georgia)", "Kazakh", "Kazakh (Kazakhstan)", "Kannada", "Kannada (India)", "Korean", "Korean (Korea)", "Konkani", "Konkani (India)", "Kyrgyz", "Kyrgyz (Kyrgyzstan)", "Lithuanian", "Lithuanian (Lithuania)", "Latvian", "Latvian (Latvia)", "Maori", "Maori (New Zealand)", "FYRO Macedonian", "FYRO Macedonian (Former Yugoslav Republic of Macedonia)", "Mongolian", "Mongolian (Mongolia)", "Marathi", "Marathi (India)", "Malay", "Malay (Brunei Darussalam)", "Malay (Malaysia)", "Maltese", "Maltese (Malta)", "Norwegian (Bokm?l)", "Norwegian (Bokm?l) (Norway)", "Dutch", "Dutch (Belgium)", "Dutch (Netherlands)", "Norwegian (Nynorsk) (Norway)", "Northern Sotho", "Northern Sotho (South Africa)", "Punjabi", "Punjabi (India)", "Polish", "Polish (Poland)", "Pashto", "Pashto (Afghanistan)", "Portuguese", "Portuguese (Brazil)", "Portuguese (Portugal)", "Quechua", "Quechua (Bolivia)", "Quechua (Ecuador)", "Quechua (Peru)", "Romanian", "Romanian (Romania)", "Russian", "Russian (Russia)", "Sanskrit", "Sanskrit (India)", "Sami (Northern)", "Sami (Northern) (Finland)", "Sami (Skolt) (Finland)", "Sami (Inari) (Finland)", "Sami (Northern) (Norway)", "Sami (Lule) (Norway)", "Sami (Southern) (Norway)", "Sami (Northern) (Sweden)", "Sami (Lule) (Sweden)", "Sami (Southern) (Sweden)", "Slovak", "Slovak (Slovakia)", "Slovenian", "Slovenian (Slovenia)", "Albanian", "Albanian (Albania)", "Serbian (Latin) (Bosnia and Herzegovina)", "Serbian (Cyrillic) (Bosnia and Herzegovina)", "Serbian (Latin) (Serbia and Montenegro)", "Serbian (Cyrillic) (Serbia and Montenegro)", "Swedish", "Swedish (Finland)", "Swedish (Sweden)", "Swahili", "Swahili (Kenya)", "Syriac", "Syriac (Syria)", "Tamil", "Tamil (India)", "Telugu", "Telugu (India)", "Thai", "Thai (Thailand)", "Tagalog", "Tagalog (Philippines)", "Tswana", "Tswana (South Africa)", "Turkish", "Turkish (Turkey)", "Tatar", "Tatar (Russia)", "Tsonga", "Ukrainian", "Ukrainian (Ukraine)", "Urdu", "Urdu (Islamic Republic of Pakistan)", "Uzbek (Latin)", "Uzbek (Latin) (Uzbekistan)", "Uzbek (Cyrillic) (Uzbekistan)", "Vietnamese", "Vietnamese (Viet Nam)", "Xhosa", "Xhosa (South Africa)", "Chinese", "Chinese (S)", "Chinese (Hong Kong)", "Chinese (Macau)", "Chinese (Singapore)", "Chinese (T)", "Zulu", "Zulu (South Africa)" }

# Register the actual auto completer
Register-PSFTeppArgumentCompleter -Command Get-KbUpdate, Save-KbUpdate -Parameter Architecture -Name Architecture
Register-PSFTeppArgumentCompleter -Command Get-KbUpdate, Save-KbUpdate -Parameter OperatingSystem -Name OperatingSystem
Register-PSFTeppArgumentCompleter -Command Get-KbUpdate, Save-KbUpdate -Parameter Product -Name Product
Register-PSFTeppArgumentCompleter -Command Get-KbUpdate, Save-KbUpdate -Parameter Language -Name Language


# set some defaults
if ((Get-Command -Name Get-NetConnectionProfile -ErrorAction SilentlyContinue)) {
    $script:internet = (Get-NetConnectionProfile).IPv4Connectivity -contains "Internet"
} else {
    try {
        $network = [Type]::GetTypeFromCLSID([Guid]"{DCB00C01-570F-4A9B-8D69-199FDBA5723B}")
        $script:internet = ([Activator]::CreateInstance($network)).GetNetworkConnections() | ForEach-Object {
            $_.GetNetwork().GetConnectivity()
        } | Where-Object { ($_ -band 64) -eq 64 }
    } catch {
        # don't care
    }
}

if (-not $internet) {
    Write-PSFMessage -Level Verbose -Message "Internet connection not detected. Setting source for Get-KbUpdate to Database."
    $PSDefaultParameterValues['Get-KbUpdate:Source'] = "Database"
    $PSDefaultParameterValues['Save-KbUpdate:Source'] = "Database"
    $null = Set-PSFConfig -FullName kbupdate.app.source -Value Database
}

# Source
Set-PSFConfig -FullName kbupdate.app.source -Value @('Web', 'Database') -Initialize -Validation stringarray -Handler { } -Description 'Data source for Get-KbUpdate and Save-KbUpdate'

# Disables session caching
Set-PSFConfig -FullName PSRemoting.Sessions.Enable -Value $true -Initialize -Validation bool -Handler { } -Description 'Globally enables session caching for PowerShell remoting'

# New-PSSessionOption
Set-PSFConfig -FullName PSRemoting.PsSessionOption.IncludePortInSPN -Value $false -Initialize -Validation bool -Description 'Changes the value of -IncludePortInSPN parameter used by New-PsSessionOption which is used for kbupdate internally when working with PSRemoting.'
Set-PSFConfig -FullName PSRemoting.PsSessionOption.SkipCACheck -Value $false -Initialize -Validation bool -Description 'Changes the value of -SkipCACheck parameter used by New-PsSessionOption which is used for kbupdate internally when working with PSRemoting.'
Set-PSFConfig -FullName PSRemoting.PsSessionOption.SkipCNCheck -Value $false -Initialize -Validation bool -Description 'Changes the value of -SkipCNCheck parameter used by New-PsSessionOption which is used for kbupdate internally when working with PSRemoting.'
Set-PSFConfig -FullName PSRemoting.PsSessionOption.SkipRevocationCheck -Value $false -Initialize -Validation bool -Description 'Changes the value of -SkipRevocationCheck parameter used by New-PsSessionOption which is used for kbupdate internally when working with PSRemoting.'

# New-PSSession
Set-PSFConfig -FullName PSRemoting.PsSession.UseSSL -Value $false -Initialize -Validation bool -Description 'Changes the value of -UseSSL parameter used by New-PsSession which is used for kbupdate internally when working with PSRemoting.'
Set-PSFConfig -FullName PSRemoting.PsSession.Port -Value $null -Initialize -Validation integerpositive -Description 'Changes the -Port parameter value used by New-PsSession which is used for kbupdate internally when working with PSRemoting. Use it when you don''t work with default port number. To reset, use Set-PSFConfig -FullName PSRemoting.PsSession.Port -Value $null'

Set-Alias -Name Get-KbInstalledUpdate -Value Get-KbInstalledSoftware

$null = $PSDefaultParameterValues["Start-Job:InitializationScript"] = {
    $null = Import-Module PSSQLite 4>$null
    $null = Import-Module PSFramework 4>$null
}

$script:importjob = Get-Job | Where-Object Name -eq kbupdate_cache_import

if (-not $global:kbupdate -and -not $script:importjob) {
    # Links, supersedes abd supersededby was taking too long to populate
    $kblib = Join-Path -Path (Split-Path -Path (Get-Module -Name kbupdate-library | Select-Object -Last 1).Path) -ChildPath library
    $linklib = Join-Path -Path $kblib -ChildPath links.dat
    $superhashlib = Join-Path -Path $kblib -ChildPath supersedes.dat
    $superbyhashlib = Join-Path -Path $kblib -ChildPath supersededby.dat

    if ((Test-Path -Path $linklib)) {
        $script:importjob = Start-Job -Name kbupdate_cache_import -ScriptBlock {
            $kbupdate = @{ }
            $kbupdate["linkhash"] = Import-PSFCliXml -Path $args[0]
            $kbupdate["superhash"] = Import-PSFCliXml -Path $args[1]
            $kbupdate["superbyhash"] = Import-PSFCliXml -Path $args[2]
            $kbupdate
        } -ArgumentList $linklib, $superhashlib, $superbyhashlib
    } else {
        Write-PSFMessage -Level Warning -Message "Cache not found, rebuilding. This should take about 45 seconds."
        $global:kbupdate = @{}
        $global:kbupdate["linkhash"] = [hashtable]::Synchronized(@{})
        $global:kbupdate["superbyhash"] = [hashtable]::Synchronized(@{})
        $global:kbupdate["superhash"] = [hashtable]::Synchronized(@{})

        foreach ($linkresult in (Invoke-SqliteQuery -DataSource $script:basedb -Query "select DISTINCT UpdateId, Link from Link")) {
            $global:kbupdate["linkhash"][$linkresult.UpdateId] += @($linkresult.Link)
        }

        foreach ($superresult in (Invoke-SqliteQuery -DataSource $script:basedb -Query "select UpdateId, KB, Description from Supersedes")) {
            $global:kbupdate["superhash"][$superresult.UpdateId] += @([pscustomobject]@{
                    KB          = $superresult.KB
                    Description = $superresult.Description
                }
            )
        }

        foreach ($superbyresult in (Invoke-SqliteQuery -DataSource $script:basedb -Query "select UpdateId, KB, Description, Description from SupersededBy")) {
            $global:kbupdate["superbyhash"][$superbyresult.UpdateId] += @([pscustomobject]@{
                    KB          = $superbyresult.KB
                    Description = $superbyresult.Description
                }
            )
        }

        $null = $global:kbupdate["superbyhash"] | Export-PSFCliXml -Path $superbyhashlib -Depth 2
        $null = $global:kbupdate["superhash"] | Export-PSFCliXml -Path $superhashlib -Depth 2
        $null = $global:kbupdate["linkhash"] | Export-PSFCliXml -Path $linklib -Depth 2
    }
}

# SIG # Begin signature block
# MIIjYAYJKoZIhvcNAQcCoIIjUTCCI00CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCOitUoeHOgMAHg
# 2ZVJCiAXl0f2VmCGUHqrxUZA2TdE0aCCHVkwggUaMIIEAqADAgECAhADBbuGIbCh
# Y1+/3q4SBOdtMA0GCSqGSIb3DQEBCwUAMHIxCzAJBgNVBAYTAlVTMRUwEwYDVQQK
# EwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xMTAvBgNV
# BAMTKERpZ2lDZXJ0IFNIQTIgQXNzdXJlZCBJRCBDb2RlIFNpZ25pbmcgQ0EwHhcN
# MjAwNTEyMDAwMDAwWhcNMjMwNjA4MTIwMDAwWjBXMQswCQYDVQQGEwJVUzERMA8G
# A1UECBMIVmlyZ2luaWExDzANBgNVBAcTBlZpZW5uYTERMA8GA1UEChMIZGJhdG9v
# bHMxETAPBgNVBAMTCGRiYXRvb2xzMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
# CgKCAQEAvL9je6vjv74IAbaY5rXqHxaNeNJO9yV0ObDg+kC844Io2vrHKGD8U5hU
# iJp6rY32RVprnAFrA4jFVa6P+sho7F5iSVAO6A+QZTHQCn7oquOefGATo43NAadz
# W2OWRro3QprMPZah0QFYpej9WaQL9w/08lVaugIw7CWPsa0S/YjHPGKQ+bYgI/kr
# EUrk+asD7lvNwckR6pGieWAyf0fNmSoevQBTV6Cd8QiUfj+/qWvLW3UoEX9ucOGX
# 2D8vSJxL7JyEVWTHg447hr6q9PzGq+91CO/c9DWFvNMjf+1c5a71fEZ54h1mNom/
# XoWZYoKeWhKnVdv1xVT1eEimibPEfQIDAQABo4IBxTCCAcEwHwYDVR0jBBgwFoAU
# WsS5eyoKo6XqcQPAYPkt9mV1DlgwHQYDVR0OBBYEFPDAoPu2A4BDTvsJ193ferHL
# 454iMA4GA1UdDwEB/wQEAwIHgDATBgNVHSUEDDAKBggrBgEFBQcDAzB3BgNVHR8E
# cDBuMDWgM6Axhi9odHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc2hhMi1hc3N1cmVk
# LWNzLWcxLmNybDA1oDOgMYYvaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NoYTIt
# YXNzdXJlZC1jcy1nMS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAwEwKjAoBggr
# BgEFBQcCARYcaHR0cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBBAEw
# gYQGCCsGAQUFBwEBBHgwdjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNl
# cnQuY29tME4GCCsGAQUFBzAChkJodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20v
# RGlnaUNlcnRTSEEyQXNzdXJlZElEQ29kZVNpZ25pbmdDQS5jcnQwDAYDVR0TAQH/
# BAIwADANBgkqhkiG9w0BAQsFAAOCAQEAj835cJUMH9Y2pBKspjznNJwcYmOxeBcH
# Ji+yK0y4bm+j44OGWH4gu/QJM+WjZajvkydJKoJZH5zrHI3ykM8w8HGbYS1WZfN4
# oMwi51jKPGZPw9neGS2PXrBcKjzb7rlQ6x74Iex+gyf8z1ZuRDitLJY09FEOh0BM
# LaLh+UvJ66ghmfIyjP/g3iZZvqwgBhn+01fObqrAJ+SagxJ/21xNQJchtUOWIlxR
# kuUn9KkuDYrMO70a2ekHODcAbcuHAGI8wzw4saK1iPPhVTlFijHS+7VfIt/d/18p
# MLHHArLQQqe1Z0mTfuL4M4xCUKpebkH8rI3Fva62/6osaXLD0ymERzCCBTAwggQY
# oAMCAQICEAQJGBtf1btmdVNDtW+VUAgwDQYJKoZIhvcNAQELBQAwZTELMAkGA1UE
# BhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2lj
# ZXJ0LmNvbTEkMCIGA1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4X
# DTEzMTAyMjEyMDAwMFoXDTI4MTAyMjEyMDAwMFowcjELMAkGA1UEBhMCVVMxFTAT
# BgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEx
# MC8GA1UEAxMoRGlnaUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBD
# QTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPjTsxx/DhGvZ3cH0wsx
# SRnP0PtFmbE620T1f+Wondsy13Hqdp0FLreP+pJDwKX5idQ3Gde2qvCchqXYJawO
# eSg6funRZ9PG+yknx9N7I5TkkSOWkHeC+aGEI2YSVDNQdLEoJrskacLCUvIUZ4qJ
# RdQtoaPpiCwgla4cSocI3wz14k1gGL6qxLKucDFmM3E+rHCiq85/6XzLkqHlOzEc
# z+ryCuRXu0q16XTmK/5sy350OTYNkO/ktU6kqepqCquE86xnTrXE94zRICUj6whk
# PlKWwfIPEvTFjg/BougsUfdzvL2FsWKDc0GCB+Q4i2pzINAPZHM8np+mM6n9Gd8l
# k9ECAwEAAaOCAc0wggHJMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYDVR0PAQH/BAQD
# AgGGMBMGA1UdJQQMMAoGCCsGAQUFBwMDMHkGCCsGAQUFBwEBBG0wazAkBggrBgEF
# BQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAChjdodHRw
# Oi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0Eu
# Y3J0MIGBBgNVHR8EejB4MDqgOKA2hjRodHRwOi8vY3JsNC5kaWdpY2VydC5jb20v
# RGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsMDqgOKA2hjRodHRwOi8vY3JsMy5k
# aWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsME8GA1UdIARI
# MEYwOAYKYIZIAYb9bAACBDAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdp
# Y2VydC5jb20vQ1BTMAoGCGCGSAGG/WwDMB0GA1UdDgQWBBRaxLl7KgqjpepxA8Bg
# +S32ZXUOWDAfBgNVHSMEGDAWgBRF66Kv9JLLgjEtUYunpyGd823IDzANBgkqhkiG
# 9w0BAQsFAAOCAQEAPuwNWiSz8yLRFcgsfCUpdqgdXRwtOhrE7zBh134LYP3DPQ/E
# r4v97yrfIFU3sOH20ZJ1D1G0bqWOWuJeJIFOEKTuP3GOYw4TS63XX0R58zYUBor3
# nEZOXP+QsRsHDpEV+7qvtVHCjSSuJMbHJyqhKSgaOnEoAjwukaPAJRHinBRHoXpo
# aK+bp1wgXNlxsQyPu6j4xRJon89Ay0BEpRPw5mQMJQhCMrI2iiQC/i9yfhzXSUWW
# 6Fkd6fp0ZGuy62ZD2rOwjNXpDd32ASDOmTFjPQgaGLOBm0/GkxAG/AeB+ova+YJJ
# 92JuoVP6EpQYhS6SkepobEQysmah5xikmmRR7zCCBY0wggR1oAMCAQICEA6bGI75
# 0C3n79tQ4ghAGFowDQYJKoZIhvcNAQEMBQAwZTELMAkGA1UEBhMCVVMxFTATBgNV
# BAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEkMCIG
# A1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4XDTIyMDgwMTAwMDAw
# MFoXDTMxMTEwOTIzNTk1OVowYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGln
# aUNlcnQgVHJ1c3RlZCBSb290IEc0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAv+aQc2jeu+RdSjwwIjBpM+zCpyUuySE98orYWcLhKac9WKt2ms2uexuE
# DcQwH/MbpDgW61bGl20dq7J58soR0uRf1gU8Ug9SH8aeFaV+vp+pVxZZVXKvaJNw
# wrK6dZlqczKU0RBEEC7fgvMHhOZ0O21x4i0MG+4g1ckgHWMpLc7sXk7Ik/ghYZs0
# 6wXGXuxbGrzryc/NrDRAX7F6Zu53yEioZldXn1RYjgwrt0+nMNlW7sp7XeOtyU9e
# 5TXnMcvak17cjo+A2raRmECQecN4x7axxLVqGDgDEI3Y1DekLgV9iPWCPhCRcKtV
# gkEy19sEcypukQF8IUzUvK4bA3VdeGbZOjFEmjNAvwjXWkmkwuapoGfdpCe8oU85
# tRFYF/ckXEaPZPfBaYh2mHY9WV1CdoeJl2l6SPDgohIbZpp0yt5LHucOY67m1O+S
# kjqePdwA5EUlibaaRBkrfsCUtNJhbesz2cXfSwQAzH0clcOP9yGyshG3u3/y1Yxw
# LEFgqrFjGESVGnZifvaAsPvoZKYz0YkH4b235kOkGLimdwHhD5QMIR2yVCkliWzl
# DlJRR3S+Jqy2QXXeeqxfjT/JvNNBERJb5RBQ6zHFynIWIgnffEx1P2PsIV/EIFFr
# b7GrhotPwtZFX50g/KEexcCPorF+CiaZ9eRpL5gdLfXZqbId5RsCAwEAAaOCATow
# ggE2MA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFOzX44LScV1kTN8uZz/nupiu
# HA9PMB8GA1UdIwQYMBaAFEXroq/0ksuCMS1Ri6enIZ3zbcgPMA4GA1UdDwEB/wQE
# AwIBhjB5BggrBgEFBQcBAQRtMGswJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRp
# Z2ljZXJ0LmNvbTBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQu
# Y29tL0RpZ2lDZXJ0QXNzdXJlZElEUm9vdENBLmNydDBFBgNVHR8EPjA8MDqgOKA2
# hjRodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290
# Q0EuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQwFAAOCAQEAcKC/
# Q1xV5zhfoKN0Gz22Ftf3v1cHvZqsoYcs7IVeqRq7IviHGmlUIu2kiHdtvRoU9BNK
# ei8ttzjv9P+Aufih9/Jy3iS8UgPITtAq3votVs/59PesMHqai7Je1M/RQ0SbQyHr
# lnKhSLSZy51PpwYDE3cnRNTnf+hZqPC/Lwum6fI0POz3A8eHqNJMQBk1RmppVLC4
# oVaO7KTVPeix3P0c2PR3WlxUjG/voVA9/HYJaISfb8rbII01YBwCA8sgsKxYoA5A
# Y8WYIsGyWfVVa88nq2x2zm8jLfR+cWojayL/ErhULSd+2DrZ8LaHlv1b0VysGMNN
# n3O3AamfV6peKOK5lDCCBq4wggSWoAMCAQICEAc2N7ckVHzYR6z9KGYqXlswDQYJ
# KoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IElu
# YzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGlnaUNlcnQg
# VHJ1c3RlZCBSb290IEc0MB4XDTIyMDMyMzAwMDAwMFoXDTM3MDMyMjIzNTk1OVow
# YzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQD
# EzJEaWdpQ2VydCBUcnVzdGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGlu
# ZyBDQTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMaGNQZJs8E9cklR
# VcclA8TykTepl1Gh1tKD0Z5Mom2gsMyD+Vr2EaFEFUJfpIjzaPp985yJC3+dH54P
# Mx9QEwsmc5Zt+FeoAn39Q7SE2hHxc7Gz7iuAhIoiGN/r2j3EF3+rGSs+QtxnjupR
# PfDWVtTnKC3r07G1decfBmWNlCnT2exp39mQh0YAe9tEQYncfGpXevA3eZ9drMvo
# hGS0UvJ2R/dhgxndX7RUCyFobjchu0CsX7LeSn3O9TkSZ+8OpWNs5KbFHc02DVzV
# 5huowWR0QKfAcsW6Th+xtVhNef7Xj3OTrCw54qVI1vCwMROpVymWJy71h6aPTnYV
# VSZwmCZ/oBpHIEPjQ2OAe3VuJyWQmDo4EbP29p7mO1vsgd4iFNmCKseSv6De4z6i
# c/rnH1pslPJSlRErWHRAKKtzQ87fSqEcazjFKfPKqpZzQmiftkaznTqj1QPgv/Ci
# PMpC3BhIfxQ0z9JMq++bPf4OuGQq+nUoJEHtQr8FnGZJUlD0UfM2SU2LINIsVzV5
# K6jzRWC8I41Y99xh3pP+OcD5sjClTNfpmEpYPtMDiP6zj9NeS3YSUZPJjAw7W4oi
# qMEmCPkUEBIDfV8ju2TjY+Cm4T72wnSyPx4JduyrXUZ14mCjWAkBKAAOhFTuzuld
# yF4wEr1GnrXTdrnSDmuZDNIztM2xAgMBAAGjggFdMIIBWTASBgNVHRMBAf8ECDAG
# AQH/AgEAMB0GA1UdDgQWBBS6FtltTYUvcyl2mi91jGogj57IbzAfBgNVHSMEGDAW
# gBTs1+OC0nFdZEzfLmc/57qYrhwPTzAOBgNVHQ8BAf8EBAMCAYYwEwYDVR0lBAww
# CgYIKwYBBQUHAwgwdwYIKwYBBQUHAQEEazBpMCQGCCsGAQUFBzABhhhodHRwOi8v
# b2NzcC5kaWdpY2VydC5jb20wQQYIKwYBBQUHMAKGNWh0dHA6Ly9jYWNlcnRzLmRp
# Z2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRSb290RzQuY3J0MEMGA1UdHwQ8MDow
# OKA2oDSGMmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRS
# b290RzQuY3JsMCAGA1UdIAQZMBcwCAYGZ4EMAQQCMAsGCWCGSAGG/WwHATANBgkq
# hkiG9w0BAQsFAAOCAgEAfVmOwJO2b5ipRCIBfmbW2CFC4bAYLhBNE88wU86/GPvH
# UF3iSyn7cIoNqilp/GnBzx0H6T5gyNgL5Vxb122H+oQgJTQxZ822EpZvxFBMYh0M
# CIKoFr2pVs8Vc40BIiXOlWk/R3f7cnQU1/+rT4osequFzUNf7WC2qk+RZp4snuCK
# rOX9jLxkJodskr2dfNBwCnzvqLx1T7pa96kQsl3p/yhUifDVinF2ZdrM8HKjI/rA
# J4JErpknG6skHibBt94q6/aesXmZgaNWhqsKRcnfxI2g55j7+6adcq/Ex8HBanHZ
# xhOACcS2n82HhyS7T6NJuXdmkfFynOlLAlKnN36TU6w7HQhJD5TNOXrd/yVjmScs
# PT9rp/Fmw0HNT7ZAmyEhQNC3EyTN3B14OuSereU0cZLXJmvkOHOrpgFPvT87eK1M
# rfvElXvtCl8zOYdBeHo46Zzh3SP9HSjTx/no8Zhf+yvYfvJGnXUsHicsJttvFXse
# GYs2uJPU5vIXmVnKcPA3v5gA3yAWTyf7YGcWoWa63VXAOimGsJigK+2VQbc61RWY
# MbRiCQ8KvYHZE/6/pNHzV9m8BPqC3jLfBInwAM1dwvnQI38AC+R2AibZ8GV2QqYp
# hwlHK+Z/GqSFD/yYlvZVVCsfgPrA8g4r5db7qS9EFUrnEw4d2zc4GqEr9u3WfPww
# ggbAMIIEqKADAgECAhAMTWlyS5T6PCpKPSkHgD1aMA0GCSqGSIb3DQEBCwUAMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwHhcNMjIwOTIxMDAwMDAwWhcNMzMxMTIxMjM1OTU5WjBGMQswCQYDVQQGEwJV
# UzERMA8GA1UEChMIRGlnaUNlcnQxJDAiBgNVBAMTG0RpZ2lDZXJ0IFRpbWVzdGFt
# cCAyMDIyIC0gMjCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAM/spSY6
# xqnya7uNwQ2a26HoFIV0MxomrNAcVR4eNm28klUMYfSdCXc9FZYIL2tkpP0GgxbX
# kZI4HDEClvtysZc6Va8z7GGK6aYo25BjXL2JU+A6LYyHQq4mpOS7eHi5ehbhVsbA
# umRTuyoW51BIu4hpDIjG8b7gL307scpTjUCDHufLckkoHkyAHoVW54Xt8mG8qjoH
# ffarbuVm3eJc9S/tjdRNlYRo44DLannR0hCRRinrPibytIzNTLlmyLuqUDgN5YyU
# XRlav/V7QG5vFqianJVHhoV5PgxeZowaCiS+nKrSnLb3T254xCg/oxwPUAY3ugjZ
# Naa1Htp4WB056PhMkRCWfk3h3cKtpX74LRsf7CtGGKMZ9jn39cFPcS6JAxGiS7uY
# v/pP5Hs27wZE5FX/NurlfDHn88JSxOYWe1p+pSVz28BqmSEtY+VZ9U0vkB8nt9Kr
# FOU4ZodRCGv7U0M50GT6Vs/g9ArmFG1keLuY/ZTDcyHzL8IuINeBrNPxB9Thvdld
# S24xlCmL5kGkZZTAWOXlLimQprdhZPrZIGwYUWC6poEPCSVT8b876asHDmoHOWIZ
# ydaFfxPZjXnPYsXs4Xu5zGcTB5rBeO3GiMiwbjJ5xwtZg43G7vUsfHuOy2SJ8bHE
# uOdTXl9V0n0ZKVkDTvpd6kVzHIR+187i1Dp3AgMBAAGjggGLMIIBhzAOBgNVHQ8B
# Af8EBAMCB4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAg
# BgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZ
# bU2FL3MpdpovdYxqII+eyG8wHQYDVR0OBBYEFGKK3tBh/I8xFO2XC809KpQU31Kc
# MFoGA1UdHwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdp
# Q2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAG
# CCsGAQUFBwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2Vy
# dC5jb20wWAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9E
# aWdpQ2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQw
# DQYJKoZIhvcNAQELBQADggIBAFWqKhrzRvN4Vzcw/HXjT9aFI/H8+ZU5myXm93KK
# mMN31GT8Ffs2wklRLHiIY1UJRjkA/GnUypsp+6M/wMkAmxMdsJiJ3HjyzXyFzVOd
# r2LiYWajFCpFh0qYQitQ/Bu1nggwCfrkLdcJiXn5CeaIzn0buGqim8FTYAnoo7id
# 160fHLjsmEHw9g6A++T/350Qp+sAul9Kjxo6UrTqvwlJFTU2WZoPVNKyG39+Xgmt
# dlSKdG3K0gVnK3br/5iyJpU4GYhEFOUKWaJr5yI+RCHSPxzAm+18SLLYkgyRTzxm
# lK9dAlPrnuKe5NMfhgFknADC6Vp0dQ094XmIvxwBl8kZI4DXNlpflhaxYwzGRkA7
# zl011Fk+Q5oYrsPJy8P7mxNfarXH4PMFw1nfJ2Ir3kHJU7n/NBBn9iYymHv+XEKU
# gZSCnawKi8ZLFUrTmJBFYDOA4CPe+AOk9kVH5c64A0JH6EE2cXet/aLol3ROLtoe
# HYxayB6a1cLwxiKoT5u92ByaUcQvmvZfpyeXupYuhVfAYOd4Vn9q78KVmksRAsiC
# nMkaBXy6cbVOepls9Oie1FqYyJ+/jbsYXEP10Cro4mLueATbvdH7WwqocH7wl4R4
# 4wgDXUcsY6glOJcB0j862uXl9uab3H4szP8XTE0AotjWAQ64i+7m4HJViSwnGWH2
# dwGMMYIFXTCCBVkCAQEwgYYwcjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTExMC8GA1UEAxMoRGln
# aUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBDQQIQAwW7hiGwoWNf
# v96uEgTnbTANBglghkgBZQMEAgEFAKCBhDAYBgorBgEEAYI3AgEMMQowCKACgACh
# AoAAMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAM
# BgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCA64Q771NatAfCwpWocdvPszsAs
# K7m02Et90bd5+G0trjANBgkqhkiG9w0BAQEFAASCAQChl2bCOG/2Z/L98i1fHQbL
# EHLGTlaJgkHr1ZRvY0bSL0nqIT3TrfXrPF+xEy5NWMtVbAENXZmh3pXQD5jpnmac
# EjrRwCy68IBlDw2mUdAkMHuuv+d1KjNvLRledo6zaT3bUEQWmsOsHrLcEq3T9Vl+
# mcVbkAnqqB/NnqWjCqKPNdKrzGYxrM0EfqwSXW+hbGetvPO4oL37LKzcrIeAFvQN
# 8KySVAjRZMuLyqB4EHPosniMT2/7cNPR/nC/MF1/J443+t/rhaEpA+C/zVvLg7Fb
# T2EKGPCPDwaKwbVW/Ecviu3xnPsD3hvAURComuKjvlEIxZ9obc4Otnuv2DaIHDqg
# oYIDIDCCAxwGCSqGSIb3DQEJBjGCAw0wggMJAgEBMHcwYzELMAkGA1UEBhMCVVMx
# FzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQDEzJEaWdpQ2VydCBUcnVz
# dGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGluZyBDQQIQDE1pckuU+jwq
# Sj0pB4A9WjANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0B
# BwEwHAYJKoZIhvcNAQkFMQ8XDTIzMDUxMjAwMjUzNVowLwYJKoZIhvcNAQkEMSIE
# IBMES7TcVSBCDWib0Z8yU7nN/+drdIRVwha8HRSQXOCRMA0GCSqGSIb3DQEBAQUA
# BIICAHTdtWOtbOxT6t1BfcIzp0SqCXnCPLy2lny1TglXhCXe/AATTxvPlYDOKKvK
# Pezjo6CgY+bBntdvtEEIjsQdPYf8MlTW7A8d3GxHEye+GqsRfqLqqP3EcHbUNpYl
# DRf7tmESB75rhp3jW5X5oarMn8lLACfZUSG6Xq2zQBQekI6AHUXJ0Au16gveXrVh
# ct2XqVzsOogyjJLMqapMxyNDPc2Z8r8EreBYrPiIJSwZFKyTl3H/VZut51599Par
# kNTYV+6k9sz6Q2pDRVLiWEtcDHzwDhiKytX826okjGhqeU+yhwL6fXzjxmuZdNwH
# VkEqKqhpR7N7oyOdCfFmPRUNXFLkkOQnLu4AnqZ9ESpUuXC9mz7vJGS/pmd8beIa
# TQ4BR5r1LPmTxpAkmhhTQJhqB2WsoP0pN8zMT35jf4BQ5sxtY1KejJhQGwddCvSZ
# DwIKJI3SnWdLGWDi+QeLgCZHqrAndTblXHkTZgjcoX21eYoy7xdI4CRrCMrsFMqu
# uHZyjzsYsZ0FDSKjpULHCvA4PmsNNknU8YYwj3M1HZfcleSdCXZ37JwvePKHh48a
# Cpk2pdXpwB36HGe+w+2Wiakrb2d6P8LoIKl8ia6/neqi2TlL2AiBcn20BXzgTig+
# KpSXVkJnn4Yg7RdkzxhQCeBdWIT60UpFYASyK8hrb+HkhD4O
# SIG # End signature block
