# Test script for Windows Update functionality
Write-Host "Testing Windows Update functionality..." -ForegroundColor Green

try {
    # Check if PSWindowsUpdate module is available
    if (-not (Get-Module -ListAvailable -Name PSWindowsUpdate)) {
        Write-Host "PSWindowsUpdate module not found. Installing..." -ForegroundColor Yellow
        Install-Module -Name PSWindowsUpdate -Force -Scope CurrentUser
    }
    
    Import-Module PSWindowsUpdate
    Write-Host "PSWindowsUpdate module loaded successfully." -ForegroundColor Green
    
    # Get available updates (using correct parameters)
    Write-Host "Fetching available Windows updates..." -ForegroundColor Yellow
    $updates = Get-WindowsUpdate -MicrosoftUpdate | Where-Object { $_.Result -eq "NotInstalled" } | Select-Object Title, KB, Size, Date
    
    if ($updates) {
        Write-Host "Found $($updates.Count) available updates:" -ForegroundColor Green
        foreach ($update in $updates | Select-Object -First 5) {
            $title = $update.Title -replace '\s+', ' '
            $kb = if ($update.KB) { "KB$($update.KB)" } else { "N/A" }
            $size = [math]::Round($update.Size / 1MB, 1)
            $date = $update.Date.ToString('yyyy-MM-dd HH:mm:ss')
            
            Write-Host "  • $title ($kb) - ${size}MB - $date" -ForegroundColor Cyan
        }
        
        if ($updates.Count -gt 5) {
            Write-Host "  ... and $($updates.Count - 5) more updates" -ForegroundColor Gray
        }
    } else {
        Write-Host "No updates found. Your system may be up to date." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "This might be because:" -ForegroundColor Yellow
    Write-Host "  • Windows Update service is not running" -ForegroundColor Yellow
    Write-Host "  • You need to run as Administrator" -ForegroundColor Yellow
    Write-Host "  • Internet connection issues" -ForegroundColor Yellow
}

Write-Host "`nTest completed." -ForegroundColor Green 