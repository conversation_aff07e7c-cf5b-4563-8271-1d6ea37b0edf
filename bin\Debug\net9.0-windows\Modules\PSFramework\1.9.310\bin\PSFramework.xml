<?xml version="1.0"?>
<doc>
    <assembly>
        <name>PSFramework</name>
    </assembly>
    <members>
        <member name="T:PSFramework.Commands.InvokePSFCallbackCommand">
            <summary>
            Command executing callbacks in functions
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFCallbackCommand.Data">
            <summary>
            Data to pass to the callback scriptblocks
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFCallbackCommand.EnableException">
            <summary>
            Whether to trigger a terminating exception in case the scriptblock errors out.
            Will be picked up automatically if not specified.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFCallbackCommand.PSCmdlet">
            <summary>
            The cmdlet object to use for triggering terminating errors.
            Will be picked up automatically if not specified.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFCallbackCommand._Caller">
            <summary>
            Information on the calling command, including name, module, file and line.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFCallbackCommand._ErrorScript">
            <summary>
            script used to write horrible errors on screen
            </summary>
        </member>
        <member name="M:PSFramework.Commands.InvokePSFCallbackCommand.BeginProcessing">
            <summary>
            Prepare runtime information, such as calling command or cmdlet object
            </summary>
        </member>
        <member name="M:PSFramework.Commands.InvokePSFCallbackCommand.ProcessRecord">
            <summary>
            Invoke callbacks
            </summary>
        </member>
        <member name="M:PSFramework.Commands.InvokePSFCallbackCommand.Terminate(PSFramework.FlowControl.CallbackException)">
            <summary>
            Kill command with maximum prejudice
            </summary>
            <param name="error">The error to terminate with</param>
        </member>
        <member name="T:PSFramework.Commands.SortPSFObjectCommand">
            <summary>
            Custom implementation of Sort-Object with PS7+ capabilities and custom property parsing
            </summary>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Stable">
            <summary>
            Gets or sets a value indicating whether a stable sort is required.
            </summary>
            <value></value>
            <remarks>
            Items that are duplicates according to the sort algorithm will appear
            in the same relative order in a stable sort.
            </remarks>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Descending">
            <summary>
            Gets or sets a value indicating whether the sort order is descending.
            </summary>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Unique">
            <summary>
            Gets or sets a value indicating whether the sort filters out any duplicate objects.
            </summary>
            <value></value>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Top">
            <summary>
            Gets or sets the number of items to return in a Top N sort.
            </summary>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Bottom">
            <summary>
            Gets or sets the number of items to return in a Bottom N sort.
            </summary>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.InputObject">
            <summary>
            </summary>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Property">
            <summary>
            Gets or Sets the Properties that would be used for Grouping, Sorting and Comparison.
            </summary>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.Culture">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="P:PSFramework.Commands.SortPSFObjectCommand.CaseSensitive">
            <summary>
            </summary>
            <value></value>
        </member>
        <member name="F:PSFramework.Commands.SortPSFObjectCommand._NonclonedProperties">
            <summary>
            List of properties to NOT clone into the hashtable used against Select-Object
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SortPSFObjectCommand._PS7Properties">
            <summary>
            List of properties that require at least PowerShell V7
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SortPSFObjectCommand._Pipeline">
            <summary>
            The pipeline that is wrapped around Select-Object
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SortPSFObjectCommand._VarName">
            <summary>
            Name of the temporary variable created in the caller scope
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SortPSFObjectCommand.BeginProcessing">
            <summary>
            Execute begin step
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SortPSFObjectCommand.ProcessRecord">
            <summary>
            Execute process step
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SortPSFObjectCommand.EndProcessing">
            <summary>
            Execute end step
            </summary>
        </member>
        <member name="T:PSFramework.Commands.ConvertToPSFHashtableCommand">
            <summary>
            Implements the ConvertTo-PSFHashtable command
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.Include">
            <summary>
            The properties to include explicitly
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.Exclude">
            <summary>
            Any properties to exclude explicitly
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.CaseSensitive">
            <summary>
            Enables case sensitivity.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.IncludeEmpty">
            <summary>
            Enables adding empty keys for explicitly included keys that were not found on the input
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.Inherit">
            <summary>
            Specifically designed for use in a command when passing through parameters with default values.
            Enabling this switch has the command inherit values from variables in the caller scope if their name has been specified in the Include parameter.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.Remap">
            <summary>
            Remap individual keys in the hashtable provided.
            Effectively renames entries in the hashtable.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.InputObject">
            <summary>
            The actual items to convert
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.ReferenceCommand">
            <summary>
            Command to use as reference. Reads parameters from the command and use them as "Include" parameter.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.ConvertToPSFHashtableCommand.ReferenceParameterSetName">
            <summary>
            The parameterset of the command to reference. Reads parameters from the specified parameterset of the command and use them as "Include" parameter.
            </summary>
        </member>
        <member name="M:PSFramework.Commands.ConvertToPSFHashtableCommand.BeginProcessing">
            <summary>
            Initialize command, resolving the ReferenceCommand if specified
            </summary>
        </member>
        <member name="M:PSFramework.Commands.ConvertToPSFHashtableCommand.ProcessRecord">
            <summary>
            Implements the basic processing logic to convert objects to hashtables
            </summary>
        </member>
        <member name="T:PSFramework.Commands.InvokePSFProtectedCommand">
            <summary>
            A wrapper that will execute the specified scriptblock in a safe manner
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.ScriptBlock">
            <summary>
            The scriptblock to execute.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.Action">
            <summary>
            Description of the action to perform
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.ActionString">
            <summary>
            A string to use localized message for the prompt
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.ActionStringValues">
            <summary>
            Values to format into the localized string
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.Target">
            <summary>
            The target to perform the action against
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.EnableException">
            <summary>
            Whether to trigger a terminating exception in case the scriptblock errors out.
            Will be picked up automatically if not specified.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.PSCmdlet">
            <summary>
            The cmdlet object to use for triggering terminating errors.
            Will be picked up automatically if not specified.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.Continue">
            <summary>
            Triggers the calling of 'continue' in case of error
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.ContinueLabel">
            <summary>
            The label in which to continue
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.Tag">
            <summary>
            Tags to add to the messages
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.RetryCount">
            <summary>
            How many times shall an attempt be made to try again when execution fails?
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.RetryWait">
            <summary>
            How long to wait inbetween retries?
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.RetryWaitEscalation">
            <summary>
            The multiplier applied to waits over the previous wait.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.RetryErrorType">
            <summary>
            Only retry on errors of the following types
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.RetryCondition">
            <summary>
            Only when this scriptblock returns $true will it try again.
            The scriptblock receives argument: The exception object.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.ErrorEvent">
            <summary>
            Code to execute if giving up in failure
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand.Level">
            <summary>
            The message level at which to generate non-error messages written by this cmdlet
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand._Caller">
            <summary>
            Information on the calling command, including name, module, file and line.
            </summary>
        </member>
        <member name="P:PSFramework.Commands.InvokePSFProtectedCommand._Message">
            <summary>
            The message to print when prompting
            </summary>
        </member>
        <member name="P:PSFramework.Commands.InvokePSFProtectedCommand._ErrorMessage">
            <summary>
            Error message to post when execution fails
            </summary>
        </member>
        <member name="F:PSFramework.Commands.InvokePSFProtectedCommand._ErrorScript">
            <summary>
            script used to write horrible errors on screen
            </summary>
        </member>
        <member name="M:PSFramework.Commands.InvokePSFProtectedCommand.BeginProcessing">
            <summary>
            Prepare runtime information, such as calling command or cmdlet object
            </summary>
        </member>
        <member name="M:PSFramework.Commands.InvokePSFProtectedCommand.ProcessRecord">
            <summary>
            Perform the actual logic
            </summary>
        </member>
        <member name="T:PSFramework.Commands.PSFCmdlet">
            <summary>
            Base class for all PSFramework Cmdlets, offering some shared tooling
            </summary>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.Invoke(System.String)">
            <summary>
            Invokes a string of text-based scriptcode
            </summary>
            <param name="ScriptCode">The script code to execute</param>
            <returns>Returns whatever it will return</returns>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.Invoke(System.Management.Automation.ScriptBlock,System.Boolean,System.Object,System.Object,System.Object,System.Object[])">
            <summary>
            Do a rich invocation of the specified scriptblock
            </summary>
            <param name="ScriptBlock">The scriptblock to execute</param>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="DollerUnder">The value to make available as $_</param>
            <param name="Input">The value to make available to $input</param>
            <param name="ScriptThis">The value to make available as $this</param>
            <param name="Args">The value to make available as $args</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.Invoke(System.String,System.Boolean,System.Object,System.Object,System.Object,System.Object[])">
            <summary>
            Do a rich invocation of the specified scriptblock
            </summary>
            <param name="ScriptCode">The scriptblock to execute</param>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="DollerUnder">The value to make available as $_</param>
            <param name="Input">The value to make available to $input</param>
            <param name="ScriptThis">The value to make available as $this</param>
            <param name="Args">The value to make available as $args</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.GetCaller(System.Int32)">
            <summary>
            Returns the caller CallStackFrame item
            </summary>
            <param name="Level">How many levels to peek into the callstack</param>
            <returns>Returns the caller CallStackFrame item</returns>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.GetCallerInfo(System.Int32)">
            <summary>
            Returns the caller information the specified levels upstack
            </summary>
            <param name="Level">How many levels to peek into the callstack</param>
            <returns>The caller information of the specified level</returns>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.TestFeature(System.String)">
            <summary>
            Checks whether a certain feature flag applies or not to the current execution.
            </summary>
            <param name="Name">The name of the feature to check.</param>
            <returns>Whether the flag is enabled</returns>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.WriteMessage(System.String,PSFramework.Message.MessageLevel,System.String,System.String,System.String,System.Int32,System.String[],System.Object,System.Collections.Hashtable)">
            <summary>
            Write a message using the PSFramework. Executed as scriptblock, so the current runspace must not be occupied elseways
            </summary>
            <param name="Message">The message to write</param>
            <param name="Level">The level to write it at</param>
            <param name="FunctionName">The name of the function / cmdlet to assume</param>
            <param name="ModuleName">The name of the module to assume</param>
            <param name="File">The file this message was written from</param>
            <param name="Line">The line in the file this message was written from</param>
            <param name="Tag">Tags to attach to this message</param>
            <param name="Target">A target object to specify</param>
            <param name="Data">Add additional metadata to the message written</param>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.WriteLocalizedMessage(System.String,System.Object[],PSFramework.Message.MessageLevel,System.String,System.String,System.String,System.Int32,System.String[],System.Object,System.Collections.Hashtable)">
            <summary>
            Write a message using the PSFramework. Executed as scriptblock, so the current runspace must not be occupied elseways
            </summary>
            <param name="String">The localized string to write</param>
            <param name="StringValues">The values to format into the localized string</param>
            <param name="Level">The level to write it at</param>
            <param name="FunctionName">The name of the function / cmdlet to assume</param>
            <param name="ModuleName">The name of the module to assume</param>
            <param name="File">The file this message was written from</param>
            <param name="Line">The line in the file this message was written from</param>
            <param name="Tag">Tags to attach to this message</param>
            <param name="Target">A target object to specify</param>
            <param name="Data">Add additional metadata to the message written</param>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.StopCommand(System.String,System.Exception,System.Object,System.String,System.String,System.String,System.Int32,System.String[],System.Boolean)">
            <summary>
            Stops the current command. Sets the stopping flag for non-terminal interruption.
            </summary>
            <param name="Message">The message to write as an error</param>
            <param name="Error">An exception object to include in the message system</param>
            <param name="Target">A target object to specify</param>
            <param name="FunctionName">The name of the function / cmdlet to assume</param>
            <param name="ModuleName">The name of the module to assume</param>
            <param name="File">The file this message was written from</param>
            <param name="Line">The line in the file this message was written from</param>
            <param name="Tag">Tags to attach to this message</param>
            <param name="EnableException">Whether the command should terminate in fire and death and terminating exceptions</param>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.StopLocalizedCommand(System.String,System.Object[],System.Exception,System.Object,System.String,System.String,System.String,System.Int32,System.String[],System.Boolean)">
            <summary>
            Stops the current command. Sets the stopping flag for non-terminal interruption.
            </summary>
            <param name="String">The localized string to write</param>
            <param name="StringValues">The values to format into the localized string</param>
            <param name="Error">An exception object to include in the message system</param>
            <param name="Target">A target object to specify</param>
            <param name="FunctionName">The name of the function / cmdlet to assume</param>
            <param name="ModuleName">The name of the module to assume</param>
            <param name="File">The file this message was written from</param>
            <param name="Line">The line in the file this message was written from</param>
            <param name="Tag">Tags to attach to this message</param>
            <param name="EnableException">Whether the command should terminate in fire and death and terminating exceptions</param>
        </member>
        <member name="F:PSFramework.Commands.PSFCmdlet.IsStopping">
            <summary>
            Whether the command has been set to terminate by StopCommand. Use when supporting EnableException
            </summary>
        </member>
        <member name="M:PSFramework.Commands.PSFCmdlet.InvokeCallback(System.Object,System.Boolean)">
            <summary>
            Invokes all applicable callback scripts.
            </summary>
            <param name="Data">Data to send to the callback scriptblocks</param>
            <param name="EnableException">Whether a failed callback scriptblock fails this command in fire and terminating exception.</param>
        </member>
        <member name="T:PSFramework.Commands.RemovePSFNullCommand">
            <summary>
            Implements the Remove-PSFNull command
            </summary>
        </member>
        <member name="F:PSFramework.Commands.RemovePSFNullCommand.InputObject">
            <summary>
            The objects to process
            </summary>
        </member>
        <member name="F:PSFramework.Commands.RemovePSFNullCommand.AllowEmptyCollections">
            <summary>
            Whether empty collections should be passed along
            </summary>
        </member>
        <member name="F:PSFramework.Commands.RemovePSFNullCommand.AllowEmptyStrings">
            <summary>
            Whether empty strings should be legal
            </summary>
        </member>
        <member name="F:PSFramework.Commands.RemovePSFNullCommand.Enumerate">
            <summary>
            Whether the output should be enumerated
            </summary>
        </member>
        <member name="M:PSFramework.Commands.RemovePSFNullCommand.ProcessRecord">
            <summary>
            Process items as they are passed to the cmdlet
            </summary>
        </member>
        <member name="T:PSFramework.Commands.SelectPSFObjectCommand">
            <summary>
            Implements the Select-PSFObject command
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.InputObject">
            <summary>
            The actual input object that is being processed
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Property">
            <summary>
            The properties to select. Supports fancy DSL
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.ExcludeProperty">
            <summary>
            Properties to skip
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.ExpandProperty">
            <summary>
            A property to expand.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Alias">
            <summary>
            Allow users to specify an alias property as part of the select
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.ScriptProperty">
            <summary>
            Specify script properties at runtime as part of the select
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.ScriptMethod">
            <summary>
            Specify script methods at runtime as part of the select
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Unique">
            <summary>
            Whether to exclude duplicates
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Last">
            <summary>
            The last number of items to pick
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.First">
            <summary>
            Pick the first n items.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Skip">
            <summary>
            Skip n items before picking items
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.SkipLast">
            <summary>
            Skip the last n items
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Wait">
            <summary>
            
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.Index">
            <summary>
            
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.ShowProperty">
            <summary>
            THe properties to display by default
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.ShowExcludeProperty">
            <summary>
            The properties to NOT display by default
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.TypeName">
            <summary>
            The typename to assign to the psobject
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand.KeepInputObject">
            <summary>
            Keep the original input object, just add to it.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand._NonclonedProperties">
            <summary>
            List of properties to NOT clone into the hashtable used against Select-Object
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand._NoAdjustment">
            <summary>
            Whether some adjustments to the object need to be done or whether the Select-Object output can be simply passed through.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand._DisplayPropertySet">
            <summary>
            The set controlling what properties will be shown by default
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SelectPSFObjectCommand._Pipeline">
            <summary>
            THe pipeline that is wrapped around Select-Object
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SelectPSFObjectCommand.BeginProcessing">
            <summary>
            Implements the begin action of the command
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SelectPSFObjectCommand.ProcessRecord">
            <summary>
            Implements the process action of the command
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SelectPSFObjectCommand.EndProcessing">
            <summary>
            Implements the end action of the command
            </summary>
        </member>
        <member name="T:PSFramework.Commands.SetPSFConfigCommand">
            <summary>
            Implements the Set-PSFConfig command
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.FullName">
            <summary>
            The full name of the setting
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Module">
            <summary>
            The name of the module the setting belongs to.
            Is optional due to just specifying a name is legal, in which case the first name segment becomes the module name.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Name">
            <summary>
            The name of the setting within a module.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Value">
            <summary>
            The value to apply.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.PersistedValue">
            <summary>
            The persisted value to apply.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.PersistedType">
            <summary>
            The persisted type to apply.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Description">
            <summary>
            Add documentation to the setting.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Validation">
            <summary>
            The validation script to use.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Handler">
            <summary>
            The handling script to apply when changing the value.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Hidden">
            <summary>
            Whether the setting should be hidden from casual discovery.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Default">
            <summary>
            Whether the setting should be applied only when nothing exists yet.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.Initialize">
            <summary>
            Whether this is the configuration initialization call.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.SimpleExport">
            <summary>
            Enabling this will cause the module to use friendly json notation on export to file.
            This may result in loss of data precision, but is more userfriendly.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.ModuleExport">
            <summary>
            Whether this setting applies to module scope file export.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.AllowDelete">
            <summary>
            Allow the setting to be deleted from memory. Has no effect after initialization.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.DisableValidation">
            <summary>
            Do not apply the validation script when changing values.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.DisableHandler">
            <summary>
            Do not run the handler script when changing values.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.PassThru">
            <summary>
            Return the changed configuration setting.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand.EnableException">
            <summary>
            Enable throwing exceptions.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._Config">
            <summary>
            The configuration item changed
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._Initialize">
            <summary>
            Whether this is an initialization execution.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._Persisted">
            <summary>
            Whether persisted values need to be restored.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._Exists">
            <summary>
            Whether the setting already exists.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._PolicyEnforced">
            <summary>
            The setting to be affected was enforced by policy and cannot be changed by the user.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._NameModule">
            <summary>
            Processed name of module.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._NameName">
            <summary>
            Processed name of setting within module.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._NameFull">
            <summary>
            Processed full name of setting.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.SetPSFConfigCommand._ValidationErrorMessage">
            <summary>
            The reason validation failed.
            Filled by ApplyValue.
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SetPSFConfigCommand.ProcessRecord">
            <summary>
            Implements the process action of Set-PSFConfig
            </summary>
        </member>
        <member name="M:PSFramework.Commands.SetPSFConfigCommand.ApplyValue(System.Object)">
            <summary>
            Applies a value to a configuration item, invoking validation and handler scriptblocks.
            </summary>
            <param name="Value">The value to apply</param>
        </member>
        <member name="M:PSFramework.Commands.SetPSFConfigCommand.ApplyCommonSettings">
            <summary>
            Abstracts out the regular settings that keep getting applied
            </summary>
        </member>
        <member name="T:PSFramework.Commands.TestPSFShouldProcessCommand">
            <summary>
            Implements the Test-PSFShouldProcess command
            </summary>
        </member>
        <member name="F:PSFramework.Commands.TestPSFShouldProcessCommand.Target">
            <summary>
            The target object to process
            </summary>
        </member>
        <member name="F:PSFramework.Commands.TestPSFShouldProcessCommand.Action">
            <summary>
            Description of the action to perform
            </summary>
        </member>
        <member name="F:PSFramework.Commands.TestPSFShouldProcessCommand.ActionString">
            <summary>
            A string to use localized message for the prompt
            </summary>
        </member>
        <member name="F:PSFramework.Commands.TestPSFShouldProcessCommand.ActionStringValues">
            <summary>
            Values to format into the localized string
            </summary>
        </member>
        <member name="F:PSFramework.Commands.TestPSFShouldProcessCommand.PSCmdlet">
            <summary>
            The PSCmdlet object of the calling command
            </summary>
        </member>
        <member name="F:PSFramework.Commands.TestPSFShouldProcessCommand._PSCmdlet">
            <summary>
            Private copy of the specified or found cmdlet object
            </summary>
        </member>
        <member name="P:PSFramework.Commands.TestPSFShouldProcessCommand._Action">
            <summary>
            THe resolved action string to display
            </summary>
        </member>
        <member name="M:PSFramework.Commands.TestPSFShouldProcessCommand.BeginProcessing">
            <summary>
            Clarifies Cmdlet object during begin
            </summary>
        </member>
        <member name="M:PSFramework.Commands.TestPSFShouldProcessCommand.ProcessRecord">
            <summary>
            Perform the query
            </summary>
        </member>
        <member name="T:PSFramework.Commands.WritePSFMessageCommand">
            <summary>
            Cmdlet performing message handling and logging
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Level">
            <summary>
            This parameter represents the verbosity of the message. The lower the number, the more important it is for a human user to read the message.
            By default, the levels are distributed like this:
            - 1-3 Direct verbose output to the user (using Write-Host)
            - 4-6 Output only visible when requesting extra verbosity (using Write-Verbose)
            - 1-9 Debugging information, written using Write-Debug
            
            In addition, it is possible to select the level "Warning" which moves the message out of the configurable range:
            The user will always be shown this message, unless he silences the entire verbosity.
            
            Possible levels:
            Critical (1), Important / Output / Host (2), Significant (3), VeryVerbose (4), Verbose (5), SomewhatVerbose (6), System (7), Debug (8), InternalComment (9), Warning (666)
            Either one of the strings or its respective number will do as input.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Message">
            <summary>
            The message to write/log. The function name and timestamp will automatically be prepended.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.String">
            <summary>
            A stored string to use to write the log.
            Used in combination with the localization component.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.StringValues">
            <summary>
            Values to format into the localized string referred to in String.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Tag">
            <summary>
            Tags to add to the message written.
            This allows filtering and grouping by category of message, targeting specific messages.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Data">
            <summary>
            Specify additional data points that are furnished to the logging providers.
            Data provided is arbitrary and not touched by the logging system itself.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.FunctionName">
            <summary>
            The name of the calling function.
            Will be automatically set, but can be overridden when necessary.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.ModuleName">
            <summary>
            The name of the module, the calling function is part of.
            Will be automatically set, but can be overridden when necessary.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.File">
            <summary>
            The file in which Write-PSFMessage was called.
            Will be automatically set, but can be overridden when necessary.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Line">
            <summary>
            The line on which Write-PSFMessage was called.
            Will be automatically set, but can be overridden when necessary.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.ErrorRecord">
            <summary>
            If an error record should be noted with the message, add the full record here.
            Especially designed for use with Warning-mode, it can legally be used in either mode.
            The error will be added to the $Error variable and enqued in the logging/debugging system.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Exception">
            <summary>
            Allows specifying an inner exception as input object. This will be passed on to the logging and used for messages.
            When specifying both ErrorRecord AND Exception, Exception wins, but ErrorRecord is still used for record metadata.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Once">
            <summary>
            Setting this parameter will cause this function to write the message only once per session.
            The string passed here and the calling function's name are used to create a unique ID, which is then used to register the action in the configuration system.
            Thus will the lockout only be written if called once and not burden the system unduly.
            This lockout will be written as a hidden value, to see it use Get-PSFConfig -Force.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.OverrideExceptionMessage">
            <summary>
            Disables automatic appending of exception messages.
            Use in cases where you already have a speaking message interpretation and do not need the original message.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Target">
            <summary>
            Add the object the message is all about, in order to simplify debugging / troubleshooting.
            For example, when calling this from a function targeting a remote computer, the computername could be specified here, allowing all messages to easily be correlated to the object processed.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.NoNewLine">
            <summary>
            Skip adding a new line after writing the message to screen.
            It is still logged as a single message though.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.EnableException">
            <summary>
            This parameters disables user-friendly warnings and enables the throwing of exceptions.
            This is less user friendly, but allows catching exceptions in calling scripts.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.Breakpoint">
            <summary>
            Enables breakpoints on the current message. By default, setting '-Debug' will NOT cause an interrupt on the current position.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand.PSCmdlet">
            <summary>
            The cmdlet object to use for writing errors.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._timestamp">
            <summary>
            The start time of the cmdlet
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._silent">
            <summary>
            Whether this cmdlet is run in silent mode
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._fromStopFunction">
            <summary>
            Whether this cmdlet was called by Stop-PSFFunction
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._callStack">
            <summary>
            The current callstack
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._stackDepth">
            <summary>
            How many items exist on the callstack
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._message">
            <summary>
            The message to write
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._messageSimple">
            <summary>
            The message simplified without timestamps. Used for logging.
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._messageColor">
            <summary>
            The message to write in color
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._messageDeveloper">
            <summary>
            Non-colored version of developermode
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._messageDeveloperColor">
            <summary>
            Colored version of developermode
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._Tags">
            <summary>
            List of tags to process
            </summary>
        </member>
        <member name="F:PSFramework.Commands.WritePSFMessageCommand._isDebug">
            <summary>
            Whether debug mode is enabled
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._errorQualifiedMessage">
            <summary>
            The input message with the error content included if desired
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._ResolvedMessage">
            <summary>
            Unified representation of the various ways messages can be specified
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._MessageSystem">
            <summary>
            The final message to use for internal logging
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._MessageStreams">
            <summary>
            The final message to use for writing to streams, such as verbose or warning
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._MessageHost">
            <summary>
            The final message to use for host messages (write using Write-PSFHostColor)
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._BreadCrumbsString">
            <summary>
            Provide breadcrumb queue of the callstack
            </summary>
        </member>
        <member name="P:PSFramework.Commands.WritePSFMessageCommand._BreadCrumbsStringColored">
            <summary>
            Provide a breadcrumb queue of the callstack in color tags
            </summary>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.BeginProcessing">
            <summary>
            Processes the begin phase of the cmdlet
            </summary>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.ProcessRecord">
            <summary>
            Processes the process phase of the cmdlet
            </summary>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.ResolveTarget(System.Object)">
            <summary>
            Processes the target transform rules on an input object
            </summary>
            <param name="Item">The item to transform</param>
            <returns>The transformed object</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.ResolveException(System.Exception)">
            <summary>
            Processes the specified exception specified
            </summary>
            <param name="Item">The exception to process</param>
            <returns>The transformed exception</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.ResolveLevel(PSFramework.Message.MessageLevel)">
            <summary>
            Processs the input level and apply policy and rules
            </summary>
            <param name="Level">The original level of the message</param>
            <returns>The processed level</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.GetMessage">
            <summary>
            Builds the message item for display of Verbose, Warning and Debug streams
            </summary>
            <returns>The message to return</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.GetMessageSimple">
            <summary>
            Builds the base message for internal system use.
            </summary>
            <returns>The message to return</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.GetMessageColor">
            <summary>
            Builds the message item if needed and returns it
            </summary>
            <returns>The message to return</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.GetMessageDeveloper">
            <summary>
            Non-host output in developermode
            </summary>
            <returns>The string to write on messages that don't go straight to Write-PSFHostColor</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.GetMessageDeveloperColor">
            <summary>
            Host output in developermode
            </summary>
            <returns>The string to write on messages that go straight to Write-PSFHostColor</returns>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.WriteHostColor(System.String)">
            <summary>
            Writes a message to screen
            </summary>
            <param name="Message">The message to write</param>
        </member>
        <member name="M:PSFramework.Commands.WritePSFMessageCommand.ResolveDefaultColor">
            <summary>
            Calculates the default color a message should have
            </summary>
            <returns>The default color of a message</returns>
        </member>
        <member name="T:PSFramework.Configuration.PersistedConfig">
            <summary>
            A setting that has been persisted on the computer.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.PersistedConfig.FullName">
            <summary>
            The full name of the setting
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.PersistedConfig.Scope">
            <summary>
            The scope it has been persisted to
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.PersistedConfig.Value">
            <summary>
            The value of the setting
            </summary>
        </member>
        <member name="T:PSFramework.Configuration.Config">
            <summary>
            Configuration Manager as well as individual configuration object.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.Name">
            <summary>
            The Name of the setting
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.FullName">
            <summary>
            The full name of the configuration entry, comprised of both Module and Name.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.Module">
            <summary>
            The module of the setting. Helps being able to group configurations.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.Description">
            <summary>
            A description of the specific setting
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.Type">
            <summary>
            The data type of the value stored in the configuration element.
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.Value">
            <summary>
            The value stored in the configuration element
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.SafeValue">
            <summary>
            The value stored in the configuration element, but without deserializing objects.
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.Unchanged">
            <summary>
            Whether the value of the configuration setting has been changed since its initialization.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.Handler">
            <summary>
            The handler script that is run whenever the configuration value is set.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.Validation">
            <summary>
            Validates the user input
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.Hidden">
            <summary>
            Setting this to true will cause the element to not be discovered unless using the '-Force' parameter on "Get-DbaConfig"
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.AllowDelete">
            <summary>
            Whether the setting can be legally deleted.
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.Initialized">
            <summary>
            Whether the setting has been initialized. This handles module imports and avoids modules overwriting settings when imported in multiple runspaces.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.PolicySet">
            <summary>
            Whether this setting was set by policy
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.PolicyEnforced">
            <summary>
            Whether this setting was set by policy and forbids changes to the configuration.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.SimpleExport">
            <summary>
            Enabling this causes export to json to use simple json serialization for data transmission.
            This is suitable for simple data that is not sensitive to conversion losses.
            Simple export leads to exports more easily readable to the human eye.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.ModuleExport">
            <summary>
            Whether this setting should be exported to a module specific file when exporting to json by modulename.
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.Config.RegistryData">
            <summary>
            The finalized value to put into the registry value when using policy to set this setting.
            Deprecated property.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.Config.DefaultValue">
            <summary>
            The default value the configuration item was set to when initializing
            </summary>
        </member>
        <member name="M:PSFramework.Configuration.Config.SetPersistedValue(PSFramework.Configuration.ConfigurationValueType,System.String)">
            <summary>
            Applies the persisted value to the configuration item.
            This method should only be called by PSFramework internals
            </summary>
            <param name="Type">The type of data being specified</param>
            <param name="ValueString">The value string to register</param>
        </member>
        <member name="M:PSFramework.Configuration.Config.ResetValue">
            <summary>
            Resets the configuration value to its configured default value
            </summary>
        </member>
        <member name="T:PSFramework.Configuration.ConfigScope">
            <summary>
            The location where a setting was applied
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.UserDefault">
            <summary>
            The configuration is set as default value for the user
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.UserMandatory">
            <summary>
            The configuration is enforced for the user
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.SystemDefault">
            <summary>
            The configuration is set as default value for all users on the system
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.SystemMandatory">
            <summary>
            The configuration is enforced for all users on the system.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.FileUserLocal">
            <summary>
            The configuration is stored as Json in the per user local machine config directory.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.FileUserShared">
            <summary>
            The configuration is stored as Json in the per user config directory shared across machines.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.FileSystem">
            <summary>
            The configuration is stored as Json in the local computer config directory.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.Environment">
            <summary>
            The configuration is stored in environment variables with full data syntax.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigScope.EnvironmentSimple">
            <summary>
            The configuration is stored in environment variables with simplified data syntax.
            </summary>
        </member>
        <member name="T:PSFramework.Configuration.ConfigurationHost">
            <summary>
            Host class providing static configuration settings that are constant across all runspaces within the process.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationHost.Configurations">
            <summary>
            Hashtable containing all the configuration entries
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationHost.Validation">
            <summary>
            Hashtable containing all the registered validations
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationHost.Schemata">
            <summary>
            The list of configuration definitions, controlling how configuration data is ingested.
            </summary>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.DeleteConfiguration(System.String)">
            <summary>
            Deletes a configuration setting in compliance with policy.
            </summary>
            <param name="FullName">The full name of the setting the remove</param>
            <returns>Whether the deletion was successful</returns>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationHost.ImportFromRegistryDone">
            <summary>
            Whether the import from registry has been completed. Prevents multiple imports and overwrites when importing the module multiple times.
            </summary>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.ConvertToPersistedValue(System.Object)">
            <summary>
            Converts any object into its persisted state.
            </summary>
            <param name="Item">The item to convert.</param>
            <returns>Its persisted state representation.</returns>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.ConvertFromPersistedValue(System.String,PSFramework.Configuration.ConfigurationValueType)">
            <summary>
            Converts a persisted value back to its original data type
            </summary>
            <param name="PersistedValue">The value in its persisted state</param>
            <param name="Type">The type of the persisted value</param>
            <returns>The natural state of the value originally persisted</returns>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.ConvertFromPersistedValue(System.String)">
            <summary>
            Converts a persisted value back to its original data type
            </summary>
            <param name="TypeQualifiedPersistedValue">The value in its persisted state, with a prefixed type identifier.</param>
            <returns>The natural state of the value originally persisted</returns>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.GetConfigValue(System.String)">
            <summary>
            Retrieve the configuration value only.
            Returns null if the configuration entry does not exist.
            </summary>
            <param name="FullName">The full name of the configuration object of which to return the value</param>
            <returns>The value of the configuration entry.</returns>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.Utf8ToBase64(System.String)">
            <summary>
            Converts a plain text into a base64 string
            </summary>
            <param name="Value">The string to convert</param>
            <returns>base64 encoded version of string.</returns>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationHost.Base64ToUtf8(System.String)">
            <summary>
            Converts a base64 encoded string into plain text
            </summary>
            <param name="Value">The string to convert</param>
            <returns>Plain Text string</returns>
        </member>
        <member name="T:PSFramework.Configuration.ConfigurationValue">
            <summary>
            Contains all information about a configuration item's value
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.ConfigurationValue.Value">
            <summary>
            The runtime value of the setting.
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.ConfigurationValue.PersistedValue">
            <summary>
            The value in its persisted state
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValue.PersistedType">
            <summary>
            The kind of 
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.ConfigurationValue.TypeQualifiedPersistedValue">
            <summary>
            The type qualified representation of the persisted value
            </summary>
        </member>
        <member name="P:PSFramework.Configuration.ConfigurationValue.SafeValue">
            <summary>
            Ensures wanton Get-PSFConfig will not deserialize persisted objects that might have registered deserialization in their module.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValue._Value">
            <summary>
            Internal storage for the Value property
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValue._PersistedValue">
            <summary>
            Internal storage for the PersistedValue property
            </summary>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationValue.ToString">
            <summary>
            The string representation of its actual value
            </summary>
            <returns>Returns the type-qualified string representation of its value</returns>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationValue.#ctor(System.String,PSFramework.Configuration.ConfigurationValueType)">
            <summary>
            Creates a value object from persisted data
            </summary>
            <param name="PersistedValue">The value that will be persisted</param>
            <param name="PersistedType">The type of the value to be persisted</param>
        </member>
        <member name="M:PSFramework.Configuration.ConfigurationValue.#ctor(System.Object)">
            <summary>
            Creates a value object from runtime data
            </summary>
            <param name="Value">The value that will be stored</param>
        </member>
        <member name="T:PSFramework.Configuration.ConfigurationValueType">
            <summary>
            The data types supported by the configuration system.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Unknown">
            <summary>
            An unknown type, should be prevented
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Null">
            <summary>
            The value is as empty as the void.
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Bool">
            <summary>
            The value is of a true/false kind
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Int">
            <summary>
            The value is a regular integer
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Double">
            <summary>
            The value is a double numeric value
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Long">
            <summary>
            The value is a long type
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.String">
            <summary>
            The value is a common string
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Timespan">
            <summary>
            The value is a regular timespan
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Datetime">
            <summary>
            The value is a plain datetime
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.ConsoleColor">
            <summary>
            The value is a fancy console color
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Array">
            <summary>
            The value is an array full of booty
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Hashtable">
            <summary>
            The value is a hashtable
            </summary>
        </member>
        <member name="F:PSFramework.Configuration.ConfigurationValueType.Object">
            <summary>
            The value is something indeterminate, but possibly complex
            </summary>
        </member>
        <member name="T:PSFramework.Filter.Condition">
            <summary>
            An individual filter condition.
            Building block of a PSFramework filter expression.
            </summary>
        </member>
        <member name="P:PSFramework.Filter.Condition.Name">
            <summary>
            Name of the condition. The String used within the expression.
            </summary>
        </member>
        <member name="F:PSFramework.Filter.Condition.Module">
            <summary>
            Name of the module owning the condition.
            </summary>
        </member>
        <member name="F:PSFramework.Filter.Condition.Version">
            <summary>
            Version of the condition. Multiple versions can coexist.
            </summary>
        </member>
        <member name="F:PSFramework.Filter.Condition.ScriptBlock">
            <summary>
            The actual script-code that is run when evaluating this condition
            </summary>
        </member>
        <member name="F:PSFramework.Filter.Condition.Type">
            <summary>
            What kind of condition is this?
            Static conditions are evaluated once only and are generally used for environment-conditions that are unlikely to change at runtime (e.g.: Operating System, PowerShell Version, runtime environment).
            Dynamic conditions are evaluated each time they are invoked.
            </summary>
        </member>
        <member name="M:PSFramework.Filter.Condition.#ctor(System.String,System.String,PSFramework.Utility.PsfScriptBlock,System.Version,PSFramework.Filter.ConditionType)">
            <summary>
            Create a new condition.
            </summary>
            <param name="Name">Name of the condition</param>
            <param name="Module">Name of the module owning the condition</param>
            <param name="ScriptBlock">The scriptblock that evaluates the condition</param>
            <param name="Version">The version of the condition</param>
            <param name="Type">The type of the condition</param>
        </member>
        <member name="M:PSFramework.Filter.Condition.Invoke(System.Object)">
            <summary>
            Evaluate the condition.
            This will run the stored scriptblock unless it is a static condition and has previously been invoked.
            </summary>
            <param name="Argument">An argument to pass to the scriptblock, available inside as $_</param>
            <returns>Whether the condition is met or not.</returns>
        </member>
        <member name="M:PSFramework.Filter.Condition.ToString">
            <summary>
            Simple string representation of the condition
            </summary>
            <returns>The name of the condition</returns>
        </member>
        <member name="T:PSFramework.Filter.ConditionSet">
            <summary>
            A set of conditions that can be applied to expressions.
            A filter expression can only contain conditions defined in the ConditionSet assigned.
            </summary>
        </member>
        <member name="P:PSFramework.Filter.ConditionSet.Name">
            <summary>
            Name of the condition set
            </summary>
        </member>
        <member name="F:PSFramework.Filter.ConditionSet.Module">
            <summary>
            The module defining the condition set
            </summary>
        </member>
        <member name="F:PSFramework.Filter.ConditionSet.Version">
            <summary>
            Version of the condition set.
            </summary>
        </member>
        <member name="P:PSFramework.Filter.ConditionSet.Conditions">
            <summary>
            Returns the list of conditions included in the set.
            Mostly for display purposes.
            </summary>
        </member>
        <member name="F:PSFramework.Filter.ConditionSet.ConditionTable">
            <summary>
            The conditions contained within the condition set
            </summary>
        </member>
        <member name="M:PSFramework.Filter.ConditionSet.#ctor(System.String,System.String,System.Version,System.Collections.Generic.List{PSFramework.Filter.Condition})">
            <summary>
            Create a new condition set
            </summary>
            <param name="Name">Name of the set</param>
            <param name="Module">Module under which the condition set is defined</param>
            <param name="Version">Version of the set</param>
            <param name="Conditions">Conditions to include in the set.</param>
        </member>
        <member name="M:PSFramework.Filter.ConditionSet.ToString">
            <summary>
            Default string representation.
            </summary>
            <returns>The name of the condition set</returns>
        </member>
        <member name="M:PSFramework.Filter.ConditionSet.Add(PSFramework.Filter.Condition)">
            <summary>
            Add a condition object to the condition set.
            </summary>
            <param name="Condition">The condition object to add</param>
        </member>
        <member name="T:PSFramework.Filter.ConditionType">
            <summary>
            What kind of condition is it?
            </summary>
        </member>
        <member name="F:PSFramework.Filter.ConditionType.Static">
            <summary>
            The condition is static - its value needs only be processed once.
            </summary>
        </member>
        <member name="F:PSFramework.Filter.ConditionType.Dynamic">
            <summary>
            The condition is dynamic - its value may change and should be evaluated each time.
            May accept arguments.
            </summary>
        </member>
        <member name="T:PSFramework.Filter.Expression">
            <summary>
            Stores a filter expression and executes it as needed.
            </summary>
        </member>
        <member name="P:PSFramework.Filter.Expression.ExpressionString">
            <summary>
            The filter expression string
            </summary>
        </member>
        <member name="F:PSFramework.Filter.Expression.ConditionSet">
            <summary>
            The ConditionSet under which this expression will be executed.
            </summary>
        </member>
        <member name="P:PSFramework.Filter.Expression.Conditions">
            <summary>
            Name of the conditions that make up this expression
            </summary>
        </member>
        <member name="M:PSFramework.Filter.Expression.#ctor(System.String,PSFramework.Filter.ConditionSet)">
            <summary>
            Create a new expression object based off a filter expression.
            </summary>
            <param name="Expression">The filter expression that will be evaluated</param>
            <param name="ConditionSet">The condition set under which the expression will be evaluated. Every condition used in the expression must be part of this set.</param>
        </member>
        <member name="M:PSFramework.Filter.Expression.Evaluate(System.Object,PSFramework.Filter.ConditionSet)">
            <summary>
            Evaluates the filter expression.
            </summary>
            <param name="Argument">Optional argument, in case the conditions are designed to evaluate a specific object</param>
            <param name="ConditionSet">Optional argument, in case the ConditionSet was not specified at creation time.</param>
            <returns>Whether the expression is true or not</returns>
        </member>
        <member name="M:PSFramework.Filter.Expression.Clone">
            <summary>
            Clones the existing expression
            </summary>
            <returns>A clone of the current expression object</returns>
        </member>
        <member name="T:PSFramework.Filter.FilterContainer">
            <summary>
            A container object designed to hold all the stored conditions and condition sets included in a runspace.
            </summary>
        </member>
        <member name="P:PSFramework.Filter.FilterContainer.Filters">
            <summary>
            Returns the filter data for the current runspace.
            </summary>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.FindCondition(System.String,System.String,System.Version,System.Boolean)">
            <summary>
            Retrieve registered conditions based on the information provided
            </summary>
            <param name="Module">The modulename to filter by.</param>
            <param name="Name">The condition name to filter by.</param>
            <param name="Version">The version number to select.</param>
            <param name="AllVersions">Return all versions of a given condition</param>
            <returns>All conditions matching the search criteria.</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.GetCondition(System.String,System.String,System.Version,System.Boolean)">
            <summary>
            Retrieve registered conditions based on the information provided
            </summary>
            <param name="Module">The modulename to filter by.</param>
            <param name="Name">The condition name to filter by.</param>
            <param name="Version">The version number to select.</param>
            <param name="AllVersions">Return all versions of a given condition</param>
            <returns>All conditions matching the search criteria.</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.FindConditionSet(System.String,System.String,System.Version,System.Boolean)">
            <summary>
            Retrieve registered condition sets based on the information provided
            </summary>
            <param name="Module">The modulename to filter by.</param>
            <param name="Name">The conditionset name to filter by.</param>
            <param name="Version">The version number to select.</param>
            <param name="AllVersions">Return all versions of a given conditionset</param>
            <returns>All condition sets matching the search criteria.</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.GetConditionSet(System.String,System.String,System.Version,System.Boolean)">
            <summary>
            Retrieve registered condition sets based on the information provided
            </summary>
            <param name="Module">The modulename to select.</param>
            <param name="Name">The condition name to select.</param>
            <param name="Version">The version number to select.</param>
            <param name="AllVersions">Return all versions of a given condition</param>
            <returns>All conditions matching the search criteria.</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.AddCondition(System.String,System.String,PSFramework.Utility.PsfScriptBlock,System.Version,PSFramework.Filter.ConditionType)">
            <summary>
            Create a new condition.
            </summary>
            <param name="Name">Name of the condition</param>
            <param name="Module">Name of the module owning the condition</param>
            <param name="ScriptBlock">The scriptblock that evaluates the condition</param>
            <param name="Version">The version of the condition</param>
            <param name="Type">The type of the condition</param>
            <returns>The newly created condition</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.AddConditionSet(System.String,System.String,System.Version,System.Collections.Generic.List{PSFramework.Filter.Condition})">
            <summary>
            Create a new condition set
            </summary>
            <param name="Name">Name of the set</param>
            <param name="Module">Module under which the condition set is defined</param>
            <param name="Version">Version of the set</param>
            <param name="Conditions">Conditions to include in the set.</param>
            <returns>The newly created condition set</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.AddConditionSet(System.String,System.String,System.Version,PSFramework.Filter.Condition[])">
            <summary>
            Create a new condition set
            </summary>
            <param name="Name">Name of the set</param>
            <param name="Module">Module under which the condition set is defined</param>
            <param name="Version">Version of the set</param>
            <param name="Conditions">Conditions to include in the set.</param>
            <returns>The newly created condition set</returns>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.Add(PSFramework.Filter.Condition)">
            <summary>
            Add a predefined condition object to the container
            </summary>
            <param name="Condition">The condition object to add</param>
        </member>
        <member name="M:PSFramework.Filter.FilterContainer.Add(PSFramework.Filter.ConditionSet)">
            <summary>
            Add a predefined condition set object to the container
            </summary>
            <param name="ConditionSet">The condition set object to add</param>
        </member>
        <member name="T:PSFramework.FlowControl.CallbackException">
            <summary>
            A custom exception, including the callback that failed
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.CallbackException.Callback">
            <summary>
            The callback that failed
            </summary>
        </member>
        <member name="M:PSFramework.FlowControl.CallbackException.#ctor(PSFramework.FlowControl.Callback,System.Exception)">
            <summary>
            Creates a new callback exception, including the Callback that failed
            </summary>
            <param name="Callback">The callback that failed</param>
            <param name="InnerException">The exception it failed with</param>
        </member>
        <member name="T:PSFramework.FlowControl.Callback">
            <summary>
            Data structure representing a registered callback scriptblock
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.Callback.Name">
            <summary>
            The name of the callback script. Used to build the unique ID to prevent accidental duplication
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.Callback.ModuleName">
            <summary>
            Name of the module the callback applies to. Only when invoked from within this module will it trigger
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.Callback.CommandName">
            <summary>
            Name of the command the callback applies to. Only when invoked from a command with a similar name will it trigger
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.Callback.Runspace">
            <summary>
            The runspace the callback belongs to. If this is set, only when invoked from that runspace will it trigger.
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.Callback.BreakAffinity">
            <summary>
            Whether the scriptblock's runspace affinity should be broken. Setting this to true will execute the scriptblock in the runspace that triggered it, rather than the one that defined it.
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.Callback.ScriptBlock">
            <summary>
            The actual callback scriptcode
            </summary>
        </member>
        <member name="P:PSFramework.FlowControl.Callback.Identity">
            <summary>
            The full identity of the callback. Used for internal callback management.
            </summary>
        </member>
        <member name="M:PSFramework.FlowControl.Callback.Applies(System.String,System.String)">
            <summary>
            Whether the callback applies to the current command.
            </summary>
            <param name="ModuleName">Module the current command is part of</param>
            <param name="CommandName">Command that is currently executing</param>
            <returns>True if it applies, otherwise False</returns>
        </member>
        <member name="M:PSFramework.FlowControl.Callback.Invoke(PSFramework.Meta.CallerInfo,System.Management.Automation.PSCmdlet,System.Object)">
            <summary>
            Invokes the callback scriptblock as configured.
            </summary>
            <param name="Caller">The object containing the information pertaining to the calling command.</param>
            <param name="Invoker">The meta object representing the invoking command.</param>
            <param name="Data">Extra data that was passed to the event</param>
        </member>
        <member name="M:PSFramework.FlowControl.Callback.ToString">
            <summary>
            Returns the string representation of the callback
            </summary>
            <returns>The string representation of the callback</returns>
        </member>
        <member name="T:PSFramework.FlowControl.CallbackHost">
            <summary>
            Host class providing statics needed to operate the callback feature.
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.CallbackHost.Callbacks">
            <summary>
            Grand dictionary of callbacks
            </summary>
        </member>
        <member name="M:PSFramework.FlowControl.CallbackHost.Add(PSFramework.FlowControl.Callback)">
            <summary>
            Adds a callback item to the list of registered callbacks
            </summary>
            <param name="Callback">The callback to add</param>
        </member>
        <member name="M:PSFramework.FlowControl.CallbackHost.Get(System.String,System.Boolean)">
            <summary>
            Returns a list of callbacks
            </summary>
            <param name="Name">The name to filter by</param>
            <param name="All">Whether also callbacks from other runspaces should be included</param>
            <returns>The list of matching callbacks</returns>
        </member>
        <member name="M:PSFramework.FlowControl.CallbackHost.Remove(PSFramework.FlowControl.Callback)">
            <summary>
            Removes a given callback from the list of registered callbacks
            </summary>
            <param name="Callback">The callback object to remove</param>
        </member>
        <member name="M:PSFramework.FlowControl.CallbackHost.RemoveRunspaceOwned">
            <summary>
            Removes all callbacks specific to the current runspace. Use this during the shutdown sequence of a runspace.
            </summary>
        </member>
        <member name="M:PSFramework.FlowControl.CallbackHost.Invoke(PSFramework.Meta.CallerInfo,System.Management.Automation.PSCmdlet,System.Object)">
            <summary>
            Invokes the callback scriptblocks that apply to the current scenario
            </summary>
            <param name="Caller">The object containing the information pertaining to the command calling the command invoking the callback.</param>
            <param name="Invoker">The meta object representing the invoking command.</param>
            <param name="Data">Extra data that was passed to the event</param>
        </member>
        <member name="T:PSFramework.FlowControl.OperatingSystem">
            <summary>
            The various operating systems PowerShell can run on
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.OperatingSystem.Windows">
            <summary>
            Common windows OS
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.OperatingSystem.Linux">
            <summary>
            Any Linux distribution
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.OperatingSystem.MacOS">
            <summary>
            Any Mac Operating System
            </summary>
        </member>
        <member name="T:PSFramework.FlowControl.PSEdition">
            <summary>
            The powershell edition
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.PSEdition.Desktop">
            <summary>
            The desktop edition of PowerShell - all editions 5.1 or below
            </summary>
        </member>
        <member name="F:PSFramework.FlowControl.PSEdition.Core">
            <summary>
            .NET core based editions of PowerShell
            </summary>
        </member>
        <member name="T:PSFramework.ComputerManagement.ComputerManagementHost">
            <summary>
            Host class containing static iresources for the computer management component
            </summary>
        </member>
        <member name="F:PSFramework.ComputerManagement.ComputerManagementHost.PSSessionIdleTimeout">
            <summary>
            The timespan a PSSession may be idle before it is cleared for cleanup
            </summary>
        </member>
        <member name="F:PSFramework.ComputerManagement.ComputerManagementHost.KnownSessionTypes">
            <summary>
            List of known session types that can be used in a SessionContainer.
            </summary>
        </member>
        <member name="T:PSFramework.ComputerManagement.PSSessionContainer">
            <summary>
            A class designed to contain PSSessionInfo objects
            </summary>
        </member>
        <member name="M:PSFramework.ComputerManagement.PSSessionContainer.#ctor">
            <summary>
            Creeates a session container dictionary, that is not opinionated about casing.
            </summary>
        </member>
        <member name="M:PSFramework.ComputerManagement.PSSessionContainer.GetExpired">
            <summary>
            Returns a list of all expired sessions
            </summary>
            <returns>The list of expired sessions</returns>
        </member>
        <member name="M:PSFramework.ComputerManagement.PSSessionContainer.GetBroken">
            <summary>
            Returns a list of all sessions that have broken (generally by having hit the hard wsman limits or the remote computer being down)
            </summary>
            <returns>The list of broken sessions.</returns>
        </member>
        <member name="T:PSFramework.ComputerManagement.PSSessionInfo">
            <summary>
            Runtime information object on a PSSession connection. Used to implement session caching and disconnection.
            </summary>
        </member>
        <member name="F:PSFramework.ComputerManagement.PSSessionInfo.Session">
            <summary>
            The session object to inform upon
            </summary>
        </member>
        <member name="F:PSFramework.ComputerManagement.PSSessionInfo.LastUsed">
            <summary>
            The time this session object was last used
            </summary>
        </member>
        <member name="P:PSFramework.ComputerManagement.PSSessionInfo.IsExpired">
            <summary>
            Shows whether the session has expired
            </summary>
        </member>
        <member name="P:PSFramework.ComputerManagement.PSSessionInfo.ComputerName">
            <summary>
            Name of the remote session
            </summary>
        </member>
        <member name="P:PSFramework.ComputerManagement.PSSessionInfo.Availability">
            <summary>
            The current state of the session
            </summary>
        </member>
        <member name="M:PSFramework.ComputerManagement.PSSessionInfo.ResetTimestamp">
            <summary>
            Resets the LastUsed timestamp, ensuring this session is not discarded prematurely.
            </summary>
        </member>
        <member name="M:PSFramework.ComputerManagement.PSSessionInfo.#ctor(System.Management.Automation.Runspaces.PSSession)">
            <summary>
            Creates a session info object from a session object.
            </summary>
            <param name="Session">The session to wrap inside an info object</param>
        </member>
        <member name="T:PSFramework.ComputerManagement.SessionContainer">
            <summary>
            Container for multiple session objects, to pass through to internal commands
            </summary>
        </member>
        <member name="F:PSFramework.ComputerManagement.SessionContainer.ComputerName">
            <summary>
            Name of the computer
            </summary>
        </member>
        <member name="F:PSFramework.ComputerManagement.SessionContainer.Connections">
            <summary>
            The connections payload. One connection per connection type.
            </summary>
        </member>
        <member name="M:PSFramework.ComputerManagement.SessionContainer.op_Implicit(PSFramework.ComputerManagement.SessionContainer)~System.Management.Automation.Runspaces.PSSession">
            <summary>
            Converts the session container to PSSession, if one is included
            </summary>
            <param name="Container">The container from which to gather the PSSession</param>
        </member>
        <member name="M:PSFramework.ComputerManagement.SessionContainer.op_Implicit(PSFramework.ComputerManagement.SessionContainer)~Microsoft.Management.Infrastructure.CimSession">
            <summary>
            Converts the session container to CImSession, if one is included
            </summary>
            <param name="Container">The container from which to gather the CimSession</param>
        </member>
        <member name="M:PSFramework.ComputerManagement.SessionContainer.ToString">
            <summary>
            The default string representation
            </summary>
            <returns></returns>
        </member>
        <member name="T:PSFramework.Feature.FeatureHost">
            <summary>
            Manages the feature flag component
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureHost._ModuleFeatureFlags">
            <summary>
            Repository of features modules may opt into or out of
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureHost._ExperimentalFeatureFlags">
            <summary>
            Repository of features and their global enablement state
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureHost.Features">
            <summary>
            List of all registered features.
            </summary>
        </member>
        <member name="M:PSFramework.Feature.FeatureHost.WriteModuleFlag(System.String,System.String,System.Boolean)">
            <summary>
            Sets a module-scope feature flag
            </summary>
            <param name="ModuleName">The module for which to set the feature flag</param>
            <param name="FeatureFlag">The flag to set it to</param>
            <param name="Value">The value to set it to</param>
        </member>
        <member name="M:PSFramework.Feature.FeatureHost.WriteGlobalFlag(System.String,System.Boolean)">
            <summary>
            Sets a global feature flag
            </summary>
            <param name="FeatureFlag">The flag to set</param>
            <param name="Value">The value to apply</param>
        </member>
        <member name="M:PSFramework.Feature.FeatureHost.ReadFlag(System.String,System.String)">
            <summary>
            Returns whether a given feature is enabled either globally or module locally
            </summary>
            <param name="FeatureFlag">The flag to test</param>
            <param name="ModuleName">The module to look up (specifying this is optional, omitting this parameter will cause it to only test global flags)</param>
            <returns>Whether the feature is enabled</returns>
        </member>
        <member name="M:PSFramework.Feature.FeatureHost.ReadModuleFlag(System.String,System.String)">
            <summary>
            Returns whether a module specific feature is enabled or not
            </summary>
            <param name="FeatureFlag">The feature flag to check for</param>
            <param name="ModuleName">The module to check in</param>
            <returns>Whether the features asked for is enabled for the module specified</returns>
        </member>
        <member name="T:PSFramework.Feature.FeatureItem">
            <summary>
            Represents a feature a module offers
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureItem.Name">
            <summary>
            The name of the feature
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureItem.Owner">
            <summary>
            The owning module
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureItem.Global">
            <summary>
            Whether the feature can be set globally
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureItem.ModuleSpecific">
            <summary>
            Whether the feature can be overridden on a per module bases.
            </summary>
        </member>
        <member name="F:PSFramework.Feature.FeatureItem.Description">
            <summary>
            The general description of the feature for human consumption
            </summary>
        </member>
        <member name="M:PSFramework.Feature.FeatureItem.#ctor">
            <summary>
            Creates an empty feature
            </summary>
        </member>
        <member name="M:PSFramework.Feature.FeatureItem.#ctor(System.String,System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            Creates a feature all fileld out.
            </summary>
            <param name="Name"></param>
            <param name="Owner"></param>
            <param name="Global"></param>
            <param name="ModuleSpecific"></param>
            <param name="Description"></param>
        </member>
        <member name="T:PSFramework.License.License">
            <summary>
            Contains license information for a single product
            </summary>
        </member>
        <member name="F:PSFramework.License.License.Product">
            <summary>
            The name of the product the license is for.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.Manufacturer">
            <summary>
            Who made the license product?
            </summary>
        </member>
        <member name="F:PSFramework.License.License.ProductVersion">
            <summary>
            The version of the product covered by the license.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.ProductType">
            <summary>
            The type of product the licensed product is.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.LicenseName">
            <summary>
            The name of the license. Useful for the usual public licenses.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.LicenseVersion">
            <summary>
            The version of the license. Useful for the usual public licenses.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.LicenseDate">
            <summary>
            When was the product licensed with the specified license.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.LicenseType">
            <summary>
            The type of the license. This allows filtering licenses by their limitations.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.LicenseText">
            <summary>
            The full license text for the pleasure of the reader.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.Description">
            <summary>
            Add some desription to how it is used.
            </summary>
        </member>
        <member name="F:PSFramework.License.License.Parent">
            <summary>
            A parent license to indicate a product used within a product.
            </summary>
        </member>
        <member name="M:PSFramework.License.License.ToString">
            <summary>
            The default string representation of the license object
            </summary>
            <returns>The default string representation of the license object</returns>
        </member>
        <member name="T:PSFramework.License.LicenseHost">
            <summary>
            Host class covering all static needs of the licensing system.
            </summary>
        </member>
        <member name="F:PSFramework.License.LicenseHost.Licenses">
            <summary>
            The list containing all registered licenses.
            </summary>
        </member>
        <member name="M:PSFramework.License.LicenseHost.Add(PSFramework.License.License)">
            <summary>
            Adds a new licenses to the list of registerd licenses.
            </summary>
            <param name="License">The license to add</param>
        </member>
        <member name="M:PSFramework.License.LicenseHost.Clear">
            <summary>
            Removes all registered Licenses
            </summary>
        </member>
        <member name="M:PSFramework.License.LicenseHost.Get">
            <summary>
            Returns all registered licenses
            </summary>
            <returns>All registerd licenses</returns>
        </member>
        <member name="M:PSFramework.License.LicenseHost.Get(PSFramework.License.License)">
            <summary>
            Returns a license that matches the specified license in content
            </summary>
            <param name="ReferenceLicense">The license based on which to search</param>
            <returns>The matching license object</returns>
        </member>
        <member name="M:PSFramework.License.LicenseHost.Remove(PSFramework.License.License)">
            <summary>
            Removes a spceific licenses from the list of registerd licenses
            </summary>
            <param name="License">License to remove</param>
        </member>
        <member name="T:PSFramework.License.LicenseType">
            <summary>
            What kind of License is this? By default, a license is a free license allowing modification but requiring attribution.
            </summary>
        </member>
        <member name="F:PSFramework.License.LicenseType.Free">
            <summary>
            No special type is present
            </summary>
        </member>
        <member name="F:PSFramework.License.LicenseType.Commercial">
            <summary>
            The license is for a commercial product. This means you have to acquire use permission, such as software licenses, user CALs et al.
            </summary>
        </member>
        <member name="F:PSFramework.License.LicenseType.NoAttribution">
            <summary>
            Reusing this product requires no attribution. Just use it and call it your own.
            </summary>
        </member>
        <member name="F:PSFramework.License.LicenseType.NoModify">
            <summary>
            This product may be used, but must not be modified in any way.
            </summary>
        </member>
        <member name="T:PSFramework.License.ProductType">
            <summary>
            What kind of product is being licensed
            </summary>
        </member>
        <member name="F:PSFramework.License.ProductType.Module">
            <summary>
            The product is a PowerShell module
            </summary>
        </member>
        <member name="F:PSFramework.License.ProductType.Script">
            <summary>
            The Product is a Script of any kind
            </summary>
        </member>
        <member name="F:PSFramework.License.ProductType.Library">
            <summary>
            The Product is a library, bringing functionality to the Shell
            </summary>
        </member>
        <member name="F:PSFramework.License.ProductType.Application">
            <summary>
            The Product is a standalone application, that happens to utilize PowerShell
            </summary>
        </member>
        <member name="F:PSFramework.License.ProductType.Other">
            <summary>
            The Product is anything other than the default types
            </summary>
        </member>
        <member name="T:PSFramework.Localization.LocalizationHost">
            <summary>
            Helper class that offers static resources for the localization component
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalizationHost.Language">
            <summary>
            The language to display text with by default. Will use thread information if not set.
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalizationHost.LoggingLanguage">
            <summary>
            The default language to log in.
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalizationHost.Strings">
            <summary>
            List of strings registered
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalizationHost.ModuleLoggingLanguage">
            <summary>
            Mapping module name to the language to use for logging.
            </summary>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.SetLoggingLanguage(System.String,System.String)">
            <summary>
            Configure the module specific logging language.
            </summary>
            <param name="Module">The module to configure</param>
            <param name="Language">The language to set. Leave empty to remove entry.</param>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.Write(System.String,System.String,System.String)">
            <summary>
            Writes a localized string. If needed creates it, then sets the text of the specified language.
            </summary>
            <param name="FullName">The name of the value to set</param>
            <param name="Language">The language to set it for</param>
            <param name="Text">The text to register</param>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.Write(System.String,System.String,System.String,System.String)">
            <summary>
            Writes a localized string. If needed creates it, then sets the text of the specified language.
            </summary>
            <param name="Module">The name of the module for which to set a value</param>
            <param name="Name">The name of the text for which to set the value</param>
            <param name="Language">The language to set it for</param>
            <param name="Text">The text to register</param>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.Read(System.String)">
            <summary>
            Reads a localized string from the list of available strings
            </summary>
            <param name="FullName">The name of the string to request. Include the modulename</param>
            <returns>The localized string requested. Empty string if nothing.</returns>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.Read(System.String,System.Object[])">
            <summary>
            Reads a localized string from the list of available strings
            </summary>
            <param name="FullName">The name of the string to request. Include the modulename</param>
            <param name="StringValues">The values to format into the string</param>
            <returns>The localized string requested. Empty string if nothing.</returns>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.Read(System.String,System.String,System.Object[])">
            <summary>
            Reads a localized string from the list of available strings
            </summary>
            <param name="ModuleName">The name of the module the string belongs to.</param>
            <param name="Name">The individual name of the setting.</param>
            <param name="StringValues">The values to format into the string</param>
            <returns>The localized string requested. Empty string if nothing.</returns>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.ReadLog(System.String)">
            <summary>
            Reads a localized string from the list of available strings for the purpose of logging
            </summary>
            <param name="FullName">The name of the string to request. Include the modulename</param>
            <returns>The localized string requested. Empty string if nothing.</returns>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.ReadLog(System.String,System.Object[])">
            <summary>
            Reads a localized string from the list of available strings for the purpose of logging
            </summary>
            <param name="FullName">The name of the string to request. Include the modulename</param>
            <param name="StringValues">The values to format into the string</param>
            <returns>The localized string requested. Empty string if nothing.</returns>
        </member>
        <member name="M:PSFramework.Localization.LocalizationHost.ReadLog(System.String,System.String,System.Object[])">
            <summary>
            Reads a localized string from the list of available strings for the purpose of logging
            </summary>
            <param name="ModuleName">The name of the module the string belongs to.</param>
            <param name="Name">The individual name of the setting.</param>
            <param name="StringValues">The values to format into the string</param>
            <returns>The localized string requested. Empty string if nothing.</returns>
        </member>
        <member name="T:PSFramework.Localization.LocalString">
            <summary>
            A string used for localized text
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalString.Module">
            <summary>
            The name of the module the string is a part of
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalString.Name">
            <summary>
            The name of the string
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalString.FullName">
            <summary>
            The full, public name of the string
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalString.Value">
            <summary>
            The actual string value of the text
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalString.LogValue">
            <summary>
            The string value to use for logging purposes
            </summary>
        </member>
        <member name="M:PSFramework.Localization.LocalString.Set(System.String,System.String)">
            <summary>
            Sets the text for a specific language
            </summary>
            <param name="Language">The language to set it for (eg: en-US; not case sensitive)</param>
            <param name="Text">The text to apply</param>
        </member>
        <member name="M:PSFramework.Localization.LocalString.GetAll">
            <summary>
            Returns a list of all strings
            </summary>
            <returns>The strings in all languages available</returns>
        </member>
        <member name="M:PSFramework.Localization.LocalString.#ctor(System.String,System.String)">
            <summary>
            Creates a new localized string
            </summary>
            <param name="Module"></param>
            <param name="Name"></param>
        </member>
        <member name="M:PSFramework.Localization.LocalString.#ctor(System.String)">
            <summary>
            Creates a new localized string
            </summary>
            <param name="FullName"></param>
        </member>
        <member name="T:PSFramework.Localization.LocalStrings">
            <summary>
            An accessor, designed to make strings more accessible from within powershell script code
            </summary>
        </member>
        <member name="F:PSFramework.Localization.LocalStrings._ModuleName">
            <summary>
            The name of the module to map with this accessor
            </summary>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Qualify(System.String)">
            <summary>
            Helper method that qualifies names with the module
            </summary>
            <param name="Name"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.ContainsKey(System.String)">
            <summary>
            
            </summary>
            <param name="Key"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Add(System.String,System.String)">
            <summary>
            
            </summary>
            <param name="Key"></param>
            <param name="Value"></param>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Remove(System.String)">
            <summary>
            
            </summary>
            <param name="Key"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.TryGetValue(System.String,System.String@)">
            <summary>
            
            </summary>
            <param name="Key"></param>
            <param name="Value"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Add(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <summary>
            
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Clear">
            <summary>
            
            </summary>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Contains(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <summary>
            
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.String}[],System.Int32)">
            <summary>
            
            </summary>
            <param name="array"></param>
            <param name="arrayIndex"></param>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Remove(System.Collections.Generic.KeyValuePair{System.String,System.String})">
            <summary>
            
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.GetEnumerator">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Contains(System.Object)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Add(System.Object,System.Object)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <param name="value"></param>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.System#Collections#IDictionary#GetEnumerator">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.Remove(System.Object)">
            <summary>
            
            </summary>
            <param name="key"></param>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.CopyTo(System.Array,System.Int32)">
            <summary>
            
            </summary>
            <param name="array"></param>
            <param name="index"></param>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.Keys">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.Values">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.Count">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.IsReadOnly">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.System#Collections#IDictionary#Keys">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.System#Collections#IDictionary#Values">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.IsFixedSize">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.SyncRoot">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.IsSynchronized">
            <summary>
            
            </summary>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.Item(System.Object)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="P:PSFramework.Localization.LocalStrings.Item(System.String)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Localization.LocalStrings.#ctor(System.String)">
            <summary>
            Creates a wrapper around the string collection
            </summary>
            <param name="ModuleName">The name of the module to wrap</param>
        </member>
        <member name="T:PSFramework.Logging.Error">
            <summary>
            Container for errors that happened during logging
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Error.Provider">
            <summary>
            The logging provider that had the issue
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Error.Instance">
            <summary>
            The specific instance of the logging provider that encounter the issue
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Error.Timestamp">
            <summary>
            When it all happened
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Error.ErrorRecord">
            <summary>
            The error that happened
            </summary>
        </member>
        <member name="M:PSFramework.Logging.Error.#ctor(System.String,System.String,System.Management.Automation.ErrorRecord)">
            <summary>
            Create a new error object
            </summary>
            <param name="Provider">Logging Provider with the issue</param>
            <param name="Instance">Instance of the Logging Provider with the issue</param>
            <param name="ErrorRecord">The Issue</param>
        </member>
        <member name="T:PSFramework.Logging.LogFileFileType">
            <summary>
            The data format to log to in the logfile logging provider
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LogFileFileType.Csv">
            <summary>
            Write as comma separated value
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LogFileFileType.Html">
            <summary>
            Write as Html fragment
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LogFileFileType.Json">
            <summary>
            Write as Json fragment
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LogFileFileType.Xml">
            <summary>
            Write as Xml fragment
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LogFileFileType.CMTrace">
            <summary>
            Write as CMTrace compatible entry
            </summary>
        </member>
        <member name="T:PSFramework.Logging.LoggingState">
            <summary>
            Used to signal the current processing state of the logging runspace
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LoggingState.Unstarted">
            <summary>
            The initial state before the logging runspace is spun up
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LoggingState.Initializing">
            <summary>
            Spinning up logging providers
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LoggingState.Ready">
            <summary>
            Ready to process messages
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LoggingState.Writing">
            <summary>
            Currently busy writing messages
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LoggingState.Broken">
            <summary>
            A critical error in the logging runspaces has occured, logging has terminated
            </summary>
        </member>
        <member name="F:PSFramework.Logging.LoggingState.Stopped">
            <summary>
            The logging has been fully stopped
            </summary>
        </member>
        <member name="T:PSFramework.Logging.Provider">
            <summary>
            Class containing all information/content needed for a logging provider
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.Name">
            <summary>
            Each logging provider has a name. Make sure it's unique
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.Enabled">
            <summary>
            Whether the logging provider is enabled. Only when this is set to true will the logging script execute its events.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.Initialized">
            <summary>
            The provider has had its initial runtime configuration (what is stored in the BeginEvent) applied.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.InstallationOptional">
            <summary>
            Whether the provider must be installed, in order for it to be used.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.ProviderVersion">
            <summary>
            The generation of the logging provider.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.IncludeModules">
            <summary>
            List of modules to include in the logging. Only messages generated from these modules will be considered by the provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.ExcludeModules">
            <summary>
            List of modules to exclude in the logging. Messages generated from these modules will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.IncludeFunctions">
            <summary>
            List of functions to include in the logging. Only messages generated from these functions will be considered by the provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.ExcludeFunctions">
            <summary>
            List of functions to exclude in the logging. Messages generated from these functions will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.IncludeTags">
            <summary>
            List of tags to include. Only messages with these tags will be considered by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.ExcludeTags">
            <summary>
            List of tags to exclude. Messages with these tags will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.IncludeRunspaces">
            <summary>
            List of runspaces to include. Only messages from one of these runspaces will be considered by this provider.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.ExcludeRunspaces">
            <summary>
            List of runspaces to exclude. Messages from these runspaces will be ignored by this provider.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.MinLevel">
            <summary>
            The minimum level of message to log.
            Note, the lower the level, the higher the priority.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.Provider.MaxLevel">
            <summary>
            The maximum level of message to log.
            Note, the lower the level, the higher the priority.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.IncludeWarning">
            <summary>
            Whether to include warning messages in the log
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.IncludeError">
            <summary>
            Whether to include error messages in the log
            </summary>
        </member>
        <member name="M:PSFramework.Logging.Provider.MessageApplies(PSFramework.Message.LogEntry)">
            <summary>
            Tests whether a log entry applies to the provider instance
            </summary>
            <param name="Entry">The Entry to validate</param>
            <returns>Whether it applies</returns>
        </member>
        <member name="M:PSFramework.Logging.Provider.MessageApplies(PSFramework.Message.PsfExceptionRecord)">
            <summary>
            Tests whether an error record applies to the provider instance
            </summary>
            <param name="Record">The error record to test</param>
            <returns>Whether it applies to the provider instance</returns>
        </member>
        <member name="F:PSFramework.Logging.Provider.BeginEvent">
            <summary>
            Event that is executed once per logging script execution, before logging occurs
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.StartEvent">
            <summary>
            Event that is executed each logging cycle, right before processing items.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.MessageEvent">
            <summary>
            Event that is executed for each message written
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.ErrorEvent">
            <summary>
            Event that is executed for each error written
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.EndEvent">
            <summary>
            Event that is executed once per logging cycle, after processing all message and error items.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.FinalEvent">
            <summary>
            Final Event that is executed when stopping the logging script.
            </summary>
        </member>
        <member name="M:PSFramework.Logging.Provider.LocalizeEvents">
            <summary>
            This will import the events into the current execution context, breaking runspace affinity
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.IsInstalledScript">
            <summary>
            Script that recognizes, whether the provider has been isntalled correctly. Some providers require installation, in order to function properly.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.InstallationScript">
            <summary>
            Script that installs the provider
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.InstallationParameters">
            <summary>
            Script that generates dynamic parameters for installing the provider.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.ConfigurationParameters">
            <summary>
            Scriptblock that adds additional parameters as needed to the Set-PSFLoggingProvider function
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.ConfigurationScript">
            <summary>
            The configuration steps taken by the Set-PSFLoggingProvider function
            </summary>
        </member>
        <member name="F:PSFramework.Logging.Provider.Errors">
            <summary>
            List of errors that happened on the logging runspace.
            </summary>
        </member>
        <member name="M:PSFramework.Logging.Provider.ToString">
            <summary>
            Returns the name of the provider.
            </summary>
            <returns>Returns the name of the provider.</returns>
        </member>
        <member name="T:PSFramework.Logging.ProviderHost">
            <summary>
            Provides infrastructure services for the logging provider system
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderHost.Providers">
            <summary>
            Dictionary with all registered logging providers
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderHost.LoggingState">
            <summary>
            The current state of the logging system
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.GetEnabled">
            <summary>
            Returns all enabled logging providers
            </summary>
            <returns>All enabled logging providers</returns>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.GetInitialized(System.Boolean)">
            <summary>
            Returns all enabled &amp; initialized logging provider
            </summary>
            /// <param name="IncludeDisabled">Whether disabled providers should also be returned</param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.GetEnabledInstances">
            <summary>
            Returns all enabled provider instances
            </summary>
            <returns>All enabled provider instances</returns>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.GetInitializedInstances(System.Boolean)">
            <summary>
            Returns all enabled and initialized provider instances
            </summary>
            <param name="IncludeDisabled">Whether disabled instances should also be returned</param>
            <returns>All enabled and initialized provider instances</returns>
        </member>
        <member name="F:PSFramework.Logging.ProviderHost.ProviderV2ModuleScript">
            <summary>
            The scriptblock used to create the dynamic module implementing the logging provider generation 2 logging instance.
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.InitializeProviderInstance(PSFramework.Logging.ProviderInstance)">
            <summary>
            Initializes a logging provider instance, creating the dynamic module needed and updating its metadata to reflect this change.
            </summary>
            <param name="Instance">The instance to initialize</param>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.UpdateAllInstances">
            <summary>
            Updates all V2 provider instances, creating new ones depending on configuration.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderHost._CycleIndicator">
            <summary>
            Used to indicate a new logging cycle occured. Switches between 1 and 0
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.WaitNextCycle(System.DateTime)">
            <summary>
            Wait for the next logging cycle to begin
            </summary>
            <param name="Timeout">Up to how long to wait</param>
            <exception cref="T:System.TimeoutException">Took too long</exception>
        </member>
        <member name="M:PSFramework.Logging.ProviderHost.NextCycle">
            <summary>
            Indicate the next cycle started
            </summary>
        </member>
        <member name="T:PSFramework.Logging.ProviderInstance">
            <summary>
            Active instance of a logging provider
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.Name">
            <summary>
            name of the instance
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.Provider">
            <summary>
            The logging provider it is an instance of
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.Enabled">
            <summary>
            Whether the logging provider instance is enabled. Only when this is set to true will the logging script execute its events.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.Initialized">
            <summary>
            The provider instance has had its initial runtime configuration (what is stored in the BeginEvent) applied.
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.#ctor(PSFramework.Logging.ProviderV2,System.String)">
            <summary>
            Creates a new instance of a logging provider
            </summary>
            <param name="Provider">The provider from which to create an instance.</param>
            <param name="Name">The name of the instance to create.</param>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.ImportConfig">
            <summary>
            Refreshes the filter and enablement settings from configuration
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.RequiresInclude">
            <summary>
            Whether any include rule must apply before the instance accepts a message.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.IncludeModules">
            <summary>
            List of modules to include in the logging. Only messages generated from these modules will be considered by the provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.ExcludeModules">
            <summary>
            List of modules to exclude in the logging. Messages generated from these modules will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.IncludeFunctions">
            <summary>
            List of functions to include in the logging. Only messages generated from these functions will be considered by the provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.ExcludeFunctions">
            <summary>
            List of functions to exclude in the logging. Messages generated from these functions will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.IncludeTags">
            <summary>
            List of tags to include. Only messages with these tags will be considered by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.ExcludeTags">
            <summary>
            List of tags to exclude. Messages with these tags will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.IncludeRunspaces">
            <summary>
            List of runspaces to include. Only messages from one of these runspaces will be considered by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.ExcludeRunspaces">
            <summary>
            List of runspaces to exclude. Messages from these runspaces will be ignored by this provider instance.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.MinLevel">
            <summary>
            The minimum level of message to log.
            Note, the lower the level, the higher the priority.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.MaxLevel">
            <summary>
            The maximum level of message to log.
            Note, the lower the level, the higher the priority.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.IncludeWarning">
            <summary>
            Whether to include warning messages in the log
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.IncludeError">
            <summary>
            Whether to include error messages in the log
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.NotAfter">
            <summary>
            Deadline after which no more messages can be accepted. Used during the logging provider disablement workflow and should not be used outside that.
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.MessageApplies(PSFramework.Message.LogEntry)">
            <summary>
            Tests whether a log entry applies to the provider instance
            </summary>
            <param name="Entry">The Entry to validate</param>
            <returns>Whether it applies</returns>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.MessageApplies(PSFramework.Message.PsfExceptionRecord)">
            <summary>
            Tests whether an error record applies to the provider instance
            </summary>
            <param name="Record">The error record to test</param>
            <returns>Whether it applies to the provider instance</returns>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.Module">
            <summary>
            The module containing the instance content
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.BeginCommand">
            <summary>
            The function to use to execute in the begin phase
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.StartCommand">
            <summary>
            The function to use to execute the start phase
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.MessageCommand">
            <summary>
            The function to use to execute the message phase
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.ErrorCommand">
            <summary>
            The function to use to execute the error phase
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.EndCommand">
            <summary>
            the function to use to execute the end phase
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderInstance.FinalCommand">
            <summary>
            The function to use to execute the final phase
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.AddRunspace(System.Guid)">
            <summary>
            Add a runspace ID to the list of included runspaces. This is threadsafe.
            </summary>
            <param name="Runspace">ID of the runspace to add.</param>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.RemoveRunspace(System.Guid)">
            <summary>
            Remove a runspace ID from the list of included runspaces. This is threadsafe.
            </summary>
            <param name="Runspace">The runspace to remove.</param>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.Drain(System.Boolean)">
            <summary>
            Wait until all applicable messages have been processed, then disable this instance.
            </summary>
            <param name="WaitForFinalize">Wait until the the final block of the instance has been executed.</param>
            <exception cref="T:System.TimeoutException">Will not wait longer than five minutes to drain messages.</exception>
        </member>
        <member name="P:PSFramework.Logging.ProviderInstance.Errors">
            <summary>
            The last 128 errors that happenend to the provider instance
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.AddError(System.Management.Automation.ErrorRecord)">
            <summary>
            Add an error record to the list of errors that happened
            </summary>
            <param name="ErrorRecord">The error that happened</param>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.GetError">
            <summary>
            Get a list of all errors that happened
            </summary>
            <returns>The list of previous errors</returns>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.ClearErrors">
            <summary>
            Resets the list of errors that happened
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderInstance.ToString">
            <summary>
            Returns the name of the provider instance.
            </summary>
            <returns>Returns the name of the provider instance.</returns>
        </member>
        <member name="T:PSFramework.Logging.ProviderV2">
            <summary>
            A generation 2 logging provider, supporting resource isolation and multi-instance operation
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderV2.ProviderVersion">
            <summary>
            The generation of the logging provider.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderV2.Instances">
            <summary>
            The list of instances currently enabled
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderV2.ConfigurationRoot">
            <summary>
            The root configuration name.
            Used by instances to facilitate instance-specific configuration retrieval.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderV2.InstanceProperties">
            <summary>
            List of configuration properties one can assign to a logging provider instance.
            Maps directly to instance-specific configuration keys.
            Mostly for documentation purposes.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderV2.Functions">
            <summary>
            Functions used by the logging provider instances.
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderV2.EnabledInstances">
            <summary>
            List of enabled instances
            </summary>
        </member>
        <member name="P:PSFramework.Logging.ProviderV2.DisabledInstances">
            <summary>
            List of disabled instances
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderV2.ConfigurationDefaultValues">
            <summary>
            The default values to include in configuration for new instances
            </summary>
        </member>
        <member name="M:PSFramework.Logging.ProviderV2.UpdateInstances">
            <summary>
            Creates provider instances based on configuration.
            </summary>
        </member>
        <member name="T:PSFramework.Logging.ProviderVersion">
            <summary>
            The generation of the Logging Provider Type
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderVersion.Version_1">
            <summary>
            The initial, now legacy version of logging providers, where all providers share the same variable scope.
            </summary>
        </member>
        <member name="F:PSFramework.Logging.ProviderVersion.Version_2">
            <summary>
            Generation 2 logging provider, where each provider is handled as a dynamically created module, properly isolating resource from each other.
            </summary>
        </member>
        <member name="T:PSFramework.Message.CallStack">
            <summary>
            Container for a callstack, to create a non-volatile copy of the relevant information
            </summary>
        </member>
        <member name="F:PSFramework.Message.CallStack.Entries">
            <summary>
            The entries that make up the callstack
            </summary>
        </member>
        <member name="F:PSFramework.Message.CallStack.DefaultEntryJoinSequence">
            <summary>
            The string sequence used to join callstack entries by default.
            </summary>
        </member>
        <member name="M:PSFramework.Message.CallStack.ToString">
            <summary>
            String representation of the callstack copy
            </summary>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Message.CallStack.ToString(System.String)">
            <summary>
            String representation of the callstack copy
            </summary>
            <param name="JoinBy">By what the individual lines should be joined</param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Message.CallStack.GetSubStack(System.Int32)">
            <summary>
            Picks a subset of the total callstack
            </summary>
            <param name="Start">The starting index to work from. Anything larger than 0 means skipping the innermost entries of the stack.</param>
            <returns>A pared-down callstack</returns>
            <exception cref="T:System.IndexOutOfRangeException">When the starting index is larger than the number of entries available.</exception>
        </member>
        <member name="M:PSFramework.Message.CallStack.GetSubStack(System.Int32,System.Int32)">
            <summary>
            Picks a subset of the total callstack
            </summary>
            <param name="Start">The starting index to work from. Anything larger than 0 means skipping the innermost entries of the stack.</param>
            <param name="Count">How many entries should be included after the start index.</param>
            <returns>A pared-down callstack</returns>
            <exception cref="T:System.IndexOutOfRangeException">When the starting index is larger than the number of entries available.</exception>
        </member>
        <member name="M:PSFramework.Message.CallStack.#ctor">
            <summary>
            Create an empty callstack
            </summary>
        </member>
        <member name="M:PSFramework.Message.CallStack.#ctor(System.Collections.Generic.IEnumerable{System.Management.Automation.CallStackFrame})">
            <summary>
            Initialize a callstack from a live callstack frame
            </summary>
            <param name="CallStack">The live powershell callstack</param>
        </member>
        <member name="T:PSFramework.Message.CallStackEntry">
            <summary>
            A single entry within the callstack
            </summary>
        </member>
        <member name="F:PSFramework.Message.CallStackEntry.FunctionName">
            <summary>
            The name of the command that executed
            </summary>
        </member>
        <member name="P:PSFramework.Message.CallStackEntry.File">
            <summary>
            The file the command was defined in
            </summary>
        </member>
        <member name="F:PSFramework.Message.CallStackEntry.Line">
            <summary>
            THe line in the scriptblock that has been executed
            </summary>
        </member>
        <member name="F:PSFramework.Message.CallStackEntry.InvocationInfo">
            <summary>
            The full invocation info object
            </summary>
        </member>
        <member name="M:PSFramework.Message.CallStackEntry.#ctor">
            <summary>
            Create an empty callstack entry.
            </summary>
        </member>
        <member name="M:PSFramework.Message.CallStackEntry.#ctor(System.String,System.String,System.Int32,System.Management.Automation.InvocationInfo)">
            <summary>
            Creates a prefilled callstack entry.
            </summary>
            <param name="FunctionName"></param>
            <param name="File"></param>
            <param name="Line"></param>
            <param name="InvocationInfo"></param>
        </member>
        <member name="M:PSFramework.Message.CallStackEntry.ToString">
            <summary>
            The string notation of the callstack entry
            </summary>
            <returns></returns>
        </member>
        <member name="T:PSFramework.Message.LogEntry">
            <summary>
            An individual entry for the message log
            </summary>
        </member>
        <member name="P:PSFramework.Message.LogEntry.Message">
            <summary>
            The message logged
            </summary>
        </member>
        <member name="P:PSFramework.Message.LogEntry.LogMessage">
            <summary>
            The message to use for logging purposes.
            Using the localized string feature, this allows maintaining uniform logging languages while still supporting localized 
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Type">
            <summary>
            What kind of entry was this?
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Timestamp">
            <summary>
            When was the message logged?
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.FunctionName">
            <summary>
            What function wrote the message
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.ModuleName">
            <summary>
            The name of the module of the function that wrote the message
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Tags">
            <summary>
            The tags applied to the message
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Data">
            <summary>
            Additional metadata provided by the message writer
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Level">
            <summary>
            What level was the message?
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Runspace">
            <summary>
            What runspace was the message written from?
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.ComputerName">
            <summary>
            The computer the message was generated on
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.TargetObject">
            <summary>
            The object that was the focus of this message.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.File">
            <summary>
            The file from which the message was written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Line">
            <summary>
            The line on which the message was written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.CallStack">
            <summary>
            The callstack when the message was written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.Username">
            <summary>
            The user that did the writing.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.ErrorRecord">
            <summary>
            An error record associated with the message.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.String">
            <summary>
            The string key to use when retrieving localized strings.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntry.StringValue">
            <summary>
            Values to format into the localized string
            </summary>
        </member>
        <member name="M:PSFramework.Message.LogEntry.#ctor">
            <summary>
            Creates an empty log entry
            </summary>
        </member>
        <member name="M:PSFramework.Message.LogEntry.#ctor(System.String,PSFramework.Message.LogEntryType,System.DateTime,System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Hashtable,PSFramework.Message.MessageLevel,System.Guid,System.String,System.Object,System.String,System.Int32,PSFramework.Message.CallStack,System.String,PSFramework.Message.PsfExceptionRecord,System.String,System.Object[])">
            <summary>
            Creates a filled out log entry
            </summary>
            <param name="Message">The message that was logged</param>
            <param name="Type">The type(s) of message written</param>
            <param name="Timestamp">When was the message logged</param>
            <param name="FunctionName">What function wrote the message</param>
            <param name="ModuleName">Name of the module the function writing this message came from</param>
            <param name="Tags">Tags that were applied to the message</param>
            <param name="Data">Additional data provided by the message writer</param>
            <param name="Level">What level was the message written at.</param>
            <param name="Runspace">The ID of the runspace that wrote the message.</param>
            <param name="ComputerName">The computer the message was generated on.</param>
            <param name="TargetObject">The object this message was all about.</param>
            <param name="File">The file of the code that wrote the message.</param>
            <param name="Line">The line on which the message was written.</param>
            <param name="CallStack">The callstack that triggered the write.</param>
            <param name="Username">The user responsible for running the code that is writing the message.</param>
            <param name="ErrorRecord">An associated error item.</param>
            <param name="String">The string key to use for retrieving localized strings</param>
            <param name="StringValue">The values to format into the localized string</param>
        </member>
        <member name="T:PSFramework.Message.LogEntryType">
            <summary>
            The kind of information the logged entry was.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntryType.None">
            <summary>
            This entry wasn't written to any stream
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntryType.Information">
            <summary>
            A message that was written to the current host equivalent, if available also to the information stream
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntryType.Verbose">
            <summary>
            A message that was written to the verbose stream
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntryType.Debug">
            <summary>
            A message that was written to the Debug stream
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogEntryType.Warning">
            <summary>
            A message written to the warning stream
            </summary>
        </member>
        <member name="T:PSFramework.Message.LogHost">
            <summary>
            Provides static information storage for logging related settings, as well as housing the logging queues.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxErrorCount">
            <summary>
            The maximum numbers of error records maintained in-memory.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxMessageCount">
            <summary>
            The maximum number of messages that can be maintained in the in-memory message queue
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxMessagefileBytes">
            <summary>
            The maximum size of a given logfile. When reaching this limit, the file will be abandoned and a new log created. Set to 0 to not limit the size.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxMessagefileCount">
            <summary>
            The maximum number of logfiles maintained at a time. Exceeding this number will cause the oldest to be culled. Set to 0 to disable the limit.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxErrorFileBytes">
            <summary>
            The maximum size all error files combined may have. When this number is exceeded, the oldest entry is culled.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxTotalFolderSize">
            <summary>
            This is the upper limit of length all items in the log folder may have combined across all processes.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.LoggingPath">
            <summary>
            Path to where the logfiles live.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MaxLogFileAge">
            <summary>
            Any logfile older than this will automatically be cleansed
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MessageLogFileEnabled">
            <summary>
            Governs, whether a log file for the system messages is written
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.MessageLogEnabled">
            <summary>
            Governs, whether a log of recent messages is kept in memory
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.ErrorLogFileEnabled">
            <summary>
            Governs, whether log files for errors are written
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.ErrorLogEnabled">
            <summary>
            Governs, whether a log of recent errors is kept in memory
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.FileSystemModernLog">
            <summary>
            Whether the filesystem logging provider uses the modern logging style with CSV headers and extra columns
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.FileSystemSerializationDepth">
            <summary>
            The depth to which the target object should serialized.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.TimeFormat">
            <summary>
            The time pattern used when writing logfiles using the filesystem provider
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.Interval">
            <summary>
            The interval at which the loging runspace runs. Increase to improve performance, reduce the minimize writing latency.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.IntervalIdle">
            <summary>
            The interval at which the loging runspace runs, when there is nothing to do.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.IntervalIdleDuration">
            <summary>
            The time with no message written that needs to occur for the logging runspace to switch to idle mode.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.LastLogged">
            <summary>
            The time the last message or error were written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.LoggingEnabled">
            <summary>
            Whether the logging is enabled at all.
            Setting this to false will stop and prevent the start of the logging runspace.
            </summary>
        </member>
        <member name="P:PSFramework.Message.LogHost.NextInterval">
            <summary>
            The next interval to use.
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.OutQueueError">
            <summary>
            The outbound queue for errors. These will be processed and written to xml
            </summary>
        </member>
        <member name="F:PSFramework.Message.LogHost.OutQueueLog">
            <summary>
            The outbound queue for logs. These will be processed and written to logfile
            </summary>
        </member>
        <member name="M:PSFramework.Message.LogHost.GetErrors">
            <summary>
            Retrieves a copy of the Error stack
            </summary>
            <returns>All errors thrown by functions using the message or flowcontrol system</returns>
        </member>
        <member name="M:PSFramework.Message.LogHost.GetLog">
            <summary>
            Retrieves a copy of the message log
            </summary>
            <returns>All messages logged this session.</returns>
        </member>
        <member name="M:PSFramework.Message.LogHost.ClearLog">
            <summary>
            Clears the current in-memory log
            </summary>
        </member>
        <member name="M:PSFramework.Message.LogHost.WriteErrorEntry(System.Management.Automation.ErrorRecord[],System.String,System.String,System.Collections.Generic.List{System.String},System.DateTime,System.String,System.Guid,System.String)">
            <summary>
            Write an error record to the log
            </summary>
            <param name="Record">The actual error record as powershell wrote it</param>
            <param name="FunctionName">The name of the function writing the error</param>
            <param name="ModuleName">The name of the module the function writing the error came from</param>
            <param name="Tags">The tags that were assigned to the error event</param>
            <param name="Timestamp">When was the error written</param>
            <param name="Message">What message was passed to the user</param>
            <param name="Runspace">The runspace the message was written from</param>
            <param name="ComputerName">The computer the error was written on</param>
            <returns>The error entry, so it may be included in the message generated</returns>
        </member>
        <member name="M:PSFramework.Message.LogHost.WriteLogEntry(System.String,PSFramework.Message.LogEntryType,System.DateTime,System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Hashtable,PSFramework.Message.MessageLevel,System.Guid,System.String,System.String,System.Int32,System.Collections.Generic.IEnumerable{System.Management.Automation.CallStackFrame},System.String,PSFramework.Message.PsfExceptionRecord,System.Object)">
            <summary>
            Write a new entry to the log
            </summary>
            <param name="Message">The message to log</param>
            <param name="Type">The type of the message logged</param>
            <param name="Timestamp">When was the message generated</param>
            <param name="FunctionName">What function wrote the message</param>
            <param name="ModuleName">What module did the function writing this message come from?</param>
            <param name="Tags">The tags that were applied to the message</param>
            <param name="Data">Additional data provided by the message</param>
            <param name="Level">At what level was the function written</param>
            <param name="Runspace">The runspace the message is coming from</param>
            <param name="ComputerName">The computer the message was generated on</param>
            <param name="File">The file from which the message was written</param>
            <param name="Line">The line on which the message was written</param>
            <param name="TargetObject">The object associated with a given message.</param>
            <param name="CallStack">The callstack at the moment the message was written.</param>
            <param name="Username">The name of the user under which the code being executed</param>
            <param name="ErrorRecord">An associated error record</param>
            <returns>The entry that is being written</returns>
        </member>
        <member name="M:PSFramework.Message.LogHost.WriteLogEntry(System.String,PSFramework.Message.LogEntryType,System.DateTime,System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Hashtable,PSFramework.Message.MessageLevel,System.Guid,System.String,System.String,System.Int32,System.Collections.Generic.IEnumerable{System.Management.Automation.CallStackFrame},System.String,PSFramework.Message.PsfExceptionRecord,System.String,System.Object[],System.Object)">
            <summary>
            Write a new entry to the log
            </summary>
            <param name="Message">The message to log</param>
            <param name="Type">The type of the message logged</param>
            <param name="Timestamp">When was the message generated</param>
            <param name="FunctionName">What function wrote the message</param>
            <param name="ModuleName">What module did the function writing this message come from?</param>
            <param name="Tags">The tags that were applied to the message</param>
            <param name="Data">Additional data provided by the message</param>
            <param name="Level">At what level was the function written</param>
            <param name="Runspace">The runspace the message is coming from</param>
            <param name="ComputerName">The computer the message was generated on</param>
            <param name="File">The file from which the message was written</param>
            <param name="Line">The line on which the message was written</param>
            <param name="TargetObject">The object associated with a given message.</param>
            <param name="CallStack">The callstack at the moment the message was written.</param>
            <param name="Username">The name of the user under which the code being executed</param>
            <param name="String">The string key to use for retrieving localized strings</param>
            <param name="StringValue">The values to format into the localized string</param>
            <param name="ErrorRecord">An associated error record</param>
            <returns>The entry that is being written</returns>
        </member>
        <member name="T:PSFramework.Message.MessageColorCondition">
            <summary>
            A rule object to change the default color of a message written to screen.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.Name">
            <summary>
            Name of the condition, making it easier to manage rules and figure out where a rule comes form
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.Color">
            <summary>
            The color it applies to
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.Priority">
            <summary>
            The precedence weight. The lower the number the higher the priority. The first match wins.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.MinLevel">
            <summary>
            Minimum message level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.MaxLevel">
            <summary>
            Maximum message level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.IncludeTags">
            <summary>
            At least one specified tag must be included
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.ExcludeTags">
            <summary>
            Any one of these tags disqualifies
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.IncludeModules">
            <summary>
            Any one of these modules must be included
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.ExcludeModules">
            <summary>
            Any one of these modules disqualify
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.IncludeFunctions">
            <summary>
            Any one of these functions must be included
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageColorCondition.ExcludeFunctions">
            <summary>
            Any one of these functions disqualify
            </summary>
        </member>
        <member name="M:PSFramework.Message.MessageColorCondition.#ctor(System.String,System.ConsoleColor)">
            <summary>
            Create a new Message Color Condition
            </summary>
            <param name="Name">The name of the condition. Not case sensitive.</param>
            <param name="Color">The color this rule applies</param>
            <exception cref="T:System.ArgumentNullException">Providing an empty string for a name will not end well.</exception>
        </member>
        <member name="M:PSFramework.Message.MessageColorCondition.Applies(PSFramework.Commands.WritePSFMessageCommand)">
            <summary>
            Whether the specified message color condition applies to the current message
            </summary>
            <param name="Command">The Write-PSFMessage command currently being processed</param>
            <returns>Whether the message applies</returns>
        </member>
        <member name="M:PSFramework.Message.MessageColorCondition.HasOverlap(System.String[],System.String[])">
            <summary>
            Helper utility, comparing two string arrays and checking whether at least one value is in both.
            Is not case sensitive.
            </summary>
            <param name="One">The first array to compare</param>
            <param name="Two">The second array to compare</param>
            <returns>Whether at least one value is in both arrays</returns>
        </member>
        <member name="T:PSFramework.Message.MessageEventSubscription">
            <summary>
            Condition and logic to be executed on message events
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription.Name">
            <summary>
            Name of the event subscription, must be unique.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription.ScriptBlock">
            <summary>
            Scriptblock to execute if the condition is met
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._MessageFilter">
            <summary>
            The internally stored filter value for Message
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.MessageFilter">
            <summary>
            The value the Message is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.MessageFilterSet">
            <summary>
            Whether filtering by Message was enabled
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._ModuleNameFilter">
            <summary>
            The internally stored filter value for ModuleName
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.ModuleNameFilter">
            <summary>
            The value the ModuleName is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.ModuleNameFilterSet">
            <summary>
            Whether filtering by ModuleName was enabled
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._FunctionNameFilter">
            <summary>
            The internally stored filter value for FunctionName
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.FunctionNameFilter">
            <summary>
            The value the FunctionName is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.FunctionNameFilterSet">
            <summary>
            Whether filtering by FunctionName was enabled
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._TargetFilter">
            <summary>
            The internally stored filter value for Target
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.TargetFilter">
            <summary>
            The value the Target is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.TargetFilterSet">
            <summary>
            Whether filtering by Target was enabled
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._LevelFilter">
            <summary>
            The internally stored filter value for Level
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.LevelFilter">
            <summary>
            The value the Level is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.LevelFilterSet">
            <summary>
            Whether filtering by Level was enabled
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._TagFilter">
            <summary>
            The internally stored filter value for Tag
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.TagFilter">
            <summary>
            The value the Tag is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.TagFilterSet">
            <summary>
            Whether filtering by Tag was enabled
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageEventSubscription._RunspaceFilter">
            <summary>
            The internally stored filter value for Runspace
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.RunspaceFilter">
            <summary>
            The value the Runspace is filtered by
            </summary>
        </member>
        <member name="P:PSFramework.Message.MessageEventSubscription.RunspaceFilterSet">
            <summary>
            Whether filtering by Runspace was enabled
            </summary>
        </member>
        <member name="M:PSFramework.Message.MessageEventSubscription.Applies(PSFramework.Message.LogEntry)">
            <summary>
            Checks, whether a given entry matches the filter defined in this subscription
            </summary>
            <param name="Entry">The entry to validate</param>
            <returns>Whether the subscription should react to this entry</returns>
        </member>
        <member name="T:PSFramework.Message.MessageHost">
            <summary>
            Provides static resources to the messaging subsystem
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MaximumInformation">
            <summary>
            The maximum message level to still display to the user directly.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MaximumVerbose">
            <summary>
            The maxium message level where verbose information is still written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MaximumDebug">
            <summary>
            The maximum message level where debug information is still written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MinimumInformation">
            <summary>
            The minimum required message level for messages that will be shown to the user.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MinimumVerbose">
            <summary>
            The minimum required message level where verbose information is written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MinimumDebug">
            <summary>
            The minimum required message level where debug information is written.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.InfoColor">
            <summary>
            The color stuff gets written to the console in
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.InfoColorEmphasis">
            <summary>
            The color important stuff gets written to the console in
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.InfoColorSubtle">
            <summary>
            The color background stuff gets written to the console in
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.DeveloperColor">
            <summary>
            The color stuff gets written to the console in, when developer mode is enabled and the message would not have been written after all
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.DeveloperMode">
            <summary>
            Enables the developer mode. In this all messages are written to the console, in order to make it easier to troubleshoot issues.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.NestedLevelDecrement">
            <summary>
            Message levels can decrease by nested level. This causes messages to have an increasingly reduced level as the size of the callstack increases. 
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.DisableVerbosity">
            <summary>
            Globally override all verbosity
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.EnableMessageTimestamp">
            <summary>
            Include message timestamps in verbose message output
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.EnableMessageLevel">
            <summary>
            Include the level of a message in verbose message output
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.EnableMessagePrefix">
            <summary>
            Include message prefix in verbose message output
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.EnableMessageDisplayCommand">
            <summary>
            Include the message display command in the verbose message output
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.EnableMessageBreadcrumbs">
            <summary>
            Include the entire callstack in the verbose message output
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.NoColor">
            <summary>
            Whether the console messages should be written with only one color, rather than respecting color tags
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.PrefixValueError">
            <summary>
            Define the message prefix value for the critical level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.PrefixValueWarning">
            <summary>
            Define the message prefix value for the warning level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.PrefixValueVerbose">
            <summary>
            Define the message prefix value for the verbose level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.PrefixValueHost">
            <summary>
            Define the message prefix value for the host level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.PrefixValueSignificant">
            <summary>
            Define the message prefix value for the important level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.TransformErrorQueueSize">
            <summary>
            The size of the transform error queue. When adding more than this, the oldest entry will be discarded
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.ExceptionTransforms">
            <summary>
            Provides the option to transform exceptions based on the original exception type
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.TargetTransforms">
            <summary>
            Provides the option to transform target objects based on type. This is sometimes important when working with live state objects that should not be serialized.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.ColorTransforms">
            <summary>
            Provids the option to change the default color used when writing a message to screen
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.TransformErrors">
            <summary>
            The list of transformation errors that occured.
            </summary>
        </member>
        <member name="M:PSFramework.Message.MessageHost.GetTransformErrors">
            <summary>
            Returns the current queue of failed transformations
            </summary>
            <returns>The list of transformations that failed</returns>
        </member>
        <member name="M:PSFramework.Message.MessageHost.WriteTransformError(System.Management.Automation.ErrorRecord,System.String,System.String,System.Object,PSFramework.Message.TransformType,System.Guid)">
            <summary>
            Writes a new transform error
            </summary>
            <param name="Record">The record of what went wrong</param>
            <param name="FunctionName">The name of the function writing the transformed message</param>
            <param name="ModuleName">The module the function writing the transformed message is part of</param>
            <param name="Object">The object that should have been transformed</param>
            <param name="Type">The type of transform that was attempted</param>
            <param name="Runspace">The runspace it all happened on</param>
        </member>
        <member name="F:PSFramework.Message.MessageHost.ExceptionTransformList">
            <summary>
            List of custom transforms for exceptions
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.TargetTransformlist">
            <summary>
            List of custom transforms for targets
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.MessageLevelModifiers">
            <summary>
            List of all modifiers that apply to message levels
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageHost.Events">
            <summary>
            List of events that subscribe to messages being written
            </summary>
        </member>
        <member name="T:PSFramework.Message.MessageLevel">
            <summary>
            The various levels of verbosity available.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Critical">
            <summary>
            Very important message, should be shown to the user as a high priority
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Important">
            <summary>
            Important message, the user should read this
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Output">
            <summary>
            Important message, the user should read this
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Host">
            <summary>
            Important message, the user should read this
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Significant">
            <summary>
            Message relevant to the user.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.VeryVerbose">
            <summary>
            Not important to the regular user, still of some interest to the curious
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Verbose">
            <summary>
            Background process information, in case the user wants some detailed information on what is currently happening.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.SomewhatVerbose">
            <summary>
            A footnote in current processing, rarely of interest to the user
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.System">
            <summary>
            A message of some interest from an internal system persepctive, but largely irrelevant to the user.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Debug">
            <summary>
            Something only of interest to a debugger
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.InternalComment">
            <summary>
            This message barely made the cut from being culled. Of purely development internal interest, and even there is 'interest' a strong word for it.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Warning">
            <summary>
            This message is a warning, sure sign something went badly wrong
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevel.Error">
            <summary>
            This message is even more of a warning, sure sign something went really, really wrong
            </summary>
        </member>
        <member name="T:PSFramework.Message.MessageLevelModifier">
            <summary>
            A modification to a given message's level
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.Name">
            <summary>
            Name of the modifier. Prevents duplication in a multi-runspace scenario.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.Modifier">
            <summary>
            The amount to modify the level by
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.IncludeFunctionName">
            <summary>
            Apply modifier only to messages from this function.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.ExcludeFunctionName">
            <summary>
            Apply modifier not when the message is written by this function.
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.IncludeModuleName">
            <summary>
            Apply modifier only to messages from this module
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.ExcludeModuleName">
            <summary>
            Do not apply modifier to messages from this module
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.IncludeTags">
            <summary>
            Only apply this modifier to a message that includes at least one of these tags
            </summary>
        </member>
        <member name="F:PSFramework.Message.MessageLevelModifier.ExcludeTags">
            <summary>
            Do not apply this modifier to a message that includes any of the following tags
            </summary>
        </member>
        <member name="M:PSFramework.Message.MessageLevelModifier.AppliesTo(System.String,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Tests, whether a message a message should be modified by this modiier
            </summary>
            <param name="FunctionName">The name of the function writing the message</param>
            <param name="ModuleName">The name of the module, the function writing this message comes from</param>
            <param name="Tags">The tags of the message written</param>
            <returns>Whether the message applies</returns>
        </member>
        <member name="T:PSFramework.Message.PsfException">
            <summary>
            Wrapper class that can emulate any exception for purpose of serialization without blowing up the storage space consumed
            </summary>
        </member>
        <member name="M:PSFramework.Message.PsfException.GetException">
            <summary>
            Returns the original exception object that we interpreted. This is on purpose not a property, as we want to avoid messing with serialization size.
            </summary>
            <returns>The original exception that got thrown</returns>
        </member>
        <member name="F:PSFramework.Message.PsfException.Message">
            <summary>
            The actual Exception Message
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.Source">
            <summary>
            The original source of the Exception
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.StackTrace">
            <summary>
            Where on the callstack did the exception occur?
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.TargetSite">
            <summary>
            What was the target site on the code that caused it. This property has been altered to avoid export issues, if a string representation is not sufficient, access the original exception using GetException()
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.HResult">
            <summary>
            The HResult of the exception. Useful in debugging native code errors.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.HelpLink">
            <summary>
            Link to a proper help article.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.Data">
            <summary>
            Additional data that has been appended
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.InnerException">
            <summary>
            The inner exception in a chain of exceptions.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.ExceptionTypeName">
            <summary>
            The full namespace name of the exception that has been wrapped.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.ExceptionData">
            <summary>
            Contains additional properties other exceptions might contain.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.CategoryInfo">
            <summary>
            The category of the error
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.ErrorDetails">
            <summary>
            The details on the error
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.FullyQualifiedErrorId">
            <summary>
            The specific error identity, used to identify the target
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.InvocationInfo">
            <summary>
            The details of how this was called.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.ScriptStackTrace">
            <summary>
            The script's stacktrace
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.TargetObject">
            <summary>
            The object being processed
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.FunctionName">
            <summary>
            The name of the function throwing the error
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.Timestamp">
            <summary>
            When was the error thrown
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.Runspace">
            <summary>
            The runspace the error occured on.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfException.ComputerName">
            <summary>
            The computer the error occured on.
            </summary>
        </member>
        <member name="M:PSFramework.Message.PsfException.#ctor">
            <summary>
            Creates an empty exception object. Mostly for serialization support
            </summary>
        </member>
        <member name="M:PSFramework.Message.PsfException.#ctor(System.Exception)">
            <summary>
            Creates an exception based on an original exception object
            </summary>
            <param name="Except">The exception to wrap around</param>
        </member>
        <member name="M:PSFramework.Message.PsfException.#ctor(System.Management.Automation.ErrorRecord)">
            <summary>
            Creates a rich information exception object based on a full error record as recorded by PowerShell
            </summary>
            <param name="Record">The error record to copy from</param>
        </member>
        <member name="M:PSFramework.Message.PsfException.#ctor(System.Exception,System.String,System.DateTime,System.String,System.Guid,System.String)">
            <summary>
            Creates a new exception object with rich meta information from the PowerShell runtime.
            </summary>
            <param name="Except">The exception thrown</param>
            <param name="FunctionName">The name of the function in which the error occured</param>
            <param name="Timestamp">When did the error occur</param>
            <param name="Message">The message to add to the exception</param>
            <param name="Runspace">The ID of the runspace from which the exception was thrown. Useful in multi-runspace scenarios.</param>
            <param name="ComputerName">The computer the error occured on.</param>
        </member>
        <member name="M:PSFramework.Message.PsfException.#ctor(System.Management.Automation.ErrorRecord,System.String,System.DateTime,System.String,System.Guid,System.String)">
            <summary>
            Creates a new exception object with rich meta information from the PowerShell runtime.
            </summary>
            <param name="Record">The error record written</param>
            <param name="FunctionName">The name of the function in which the error occured</param>
            <param name="Timestamp">When did the error occur</param>
            <param name="Message">The message to add to the exception</param>
            <param name="Runspace">The ID of the runspace from which the exception was thrown. Useful in multi-runspace scenarios.</param>
            <param name="ComputerName">The computer the error occured on.</param>
        </member>
        <member name="M:PSFramework.Message.PsfException.ToString">
            <summary>
            Returns a string representation of the exception.
            </summary>
            <returns></returns>
        </member>
        <member name="T:PSFramework.Message.PsfExceptionRecord">
            <summary>
            Carrier class, designed to hold an arbitrary number of exceptions. Used for exporting to XML in nice per-incident packages.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.Runspace">
            <summary>
            Runspace where shit happened.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.ComputerName">
            <summary>
            The computer name the exception was written on
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.Timestamp">
            <summary>
            When did things go bad?
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.FunctionName">
            <summary>
            Name of the function, where fail happened.
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.ModuleName">
            <summary>
            The module of the function where fail happened
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.Tags">
            <summary>
            The tags that were applied to the failure
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.Message">
            <summary>
            The message the poor user was shown.
            </summary>
        </member>
        <member name="P:PSFramework.Message.PsfExceptionRecord.ExceptionType">
            <summary>
            Displays the name of the exception, the make scanning exceptions easier.
            </summary>
        </member>
        <member name="P:PSFramework.Message.PsfExceptionRecord.TargetObject">
            <summary>
            The target object of the first exception in the list, if any
            </summary>
        </member>
        <member name="F:PSFramework.Message.PsfExceptionRecord.Exceptions">
            <summary>
            List of Exceptions that are part of the incident (usually - but not always - only one).
            </summary>
        </member>
        <member name="M:PSFramework.Message.PsfExceptionRecord.ToString">
            <summary>
            The default string display of this message
            </summary>
            <returns>The message text of the exception</returns>
        </member>
        <member name="M:PSFramework.Message.PsfExceptionRecord.#ctor">
            <summary>
            Creates an empty container. Ideal for the homeworker who loves doing it all himself.
            </summary>
        </member>
        <member name="M:PSFramework.Message.PsfExceptionRecord.#ctor(PSFramework.Message.PsfException)">
            <summary>
            Creates a container filled with the first exception.
            </summary>
            <param name="Exception"></param>
        </member>
        <member name="M:PSFramework.Message.PsfExceptionRecord.#ctor(System.Guid,System.String,System.DateTime,System.String,System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Creates a container filled with the meta information but untouched by exceptions
            </summary>
            <param name="Runspace">The runspace where it all happened</param>
            <param name="ComputerName">The computer the error was recorded</param>
            <param name="Timestamp">When did it happen?</param>
            <param name="FunctionName">Where did it happen?</param>
            <param name="ModuleName">The name of the module where fail happened</param>
            <param name="Tags">The tags that were assigned to the failure</param>
            <param name="Message">What did the witness have to say?</param>
        </member>
        <member name="T:PSFramework.Message.TransformCondition">
            <summary>
            A condition, under which the object shall be transaformed
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformCondition.TypeName">
            <summary>
            Name of the type. All similar types (as determined by the '-like' operator) will be transformed.
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformCondition.ModuleName">
            <summary>
            The name of the module to consider, using the -Like operator
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformCondition.FunctionName">
            <summary>
            The name of the function name to consider, using the -Like operator
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformCondition.ScriptBlock">
            <summary>
            The scriptblock that performs the transformation
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformCondition.Type">
            <summary>
            What kind of transformation is being performed?
            </summary>
        </member>
        <member name="M:PSFramework.Message.TransformCondition.#ctor(System.String,System.String,System.String,System.Management.Automation.ScriptBlock,PSFramework.Message.TransformType)">
            <summary>
            Initializes a transform condition
            </summary>
            <param name="TypeName">Only objects of similar name will be transformed</param>
            <param name="ModuleName">Only objects coming from similar modules will be considered</param>
            <param name="FunctionName">Only objects coming from similar functions will be considered</param>
            <param name="ScriptBlock">The scriptblock used for the transformation</param>
            <param name="Type">What kind of transformation this is</param>
        </member>
        <member name="T:PSFramework.Message.TransformError">
            <summary>
            An error occured during a message transformation
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.Record">
            <summary>
            The error record of what went wrong
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.FunctionName">
            <summary>
            The name of the function writing the message that failed to transform
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.ModuleName">
            <summary>
            The name of the module the command writing the message came from
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.Timestamp">
            <summary>
            When did it all happen?
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.Object">
            <summary>
            The object that was supposed to be transformed
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.Type">
            <summary>
            The kind of transform that failed
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformError.Runspace">
            <summary>
            The runspace it all happened on
            </summary>
        </member>
        <member name="M:PSFramework.Message.TransformError.#ctor(System.Management.Automation.ErrorRecord,System.String,System.String,System.Object,PSFramework.Message.TransformType,System.Guid)">
            <summary>
            Creates a new transform error
            </summary>
            <param name="Record">The record of what went wrong</param>
            <param name="FunctionName">The name of the function writing the transformed message</param>
            <param name="ModuleName">The module the function writing the transformed message is part of</param>
            <param name="Object">The object that should have been transformed</param>
            <param name="Type">The type of transform that was attempted</param>
            <param name="Runspace">The runspace it all happened on</param>
        </member>
        <member name="T:PSFramework.Message.TransformList">
            <summary>
            List engine, managing the lists for a message transformation type
            </summary>
        </member>
        <member name="M:PSFramework.Message.TransformList.GetAll">
            <summary>
            Returns all entries in the list.
            </summary>
            <returns>The list of transforms contained within</returns>
        </member>
        <member name="M:PSFramework.Message.TransformList.IsListed(PSFramework.Message.TransformCondition)">
            <summary>
            Returns whether the actual object is part of the list
            </summary>
            <param name="Condition">The object to test for list membership</param>
            <returns>Whether the object is listed</returns>
        </member>
        <member name="M:PSFramework.Message.TransformList.IsContained(PSFramework.Message.TransformCondition)">
            <summary>
            Returns whether a condition with equal conditions already exists
            </summary>
            <param name="Condition">The condition to test</param>
            <returns>Whether the referenced condition is already listed</returns>
        </member>
        <member name="M:PSFramework.Message.TransformList.Add(PSFramework.Message.TransformCondition)">
            <summary>
            Adds a condition to the list, if there is no equivalent condition present.
            </summary>
            <param name="Condition">The condition to add</param>
        </member>
        <member name="M:PSFramework.Message.TransformList.Remove(PSFramework.Message.TransformCondition)">
            <summary>
            Removes a condition from the lsit of conditional transforms
            </summary>
            <param name="Condition">The condition to remove</param>
        </member>
        <member name="M:PSFramework.Message.TransformList.Get(System.String,System.String,System.String)">
            <summary>
            Returns the first transform whose filter is similar enough to work out.
            </summary>
            <param name="TypeName">The name of the type to check for a transform</param>
            <param name="ModuleName">The module of the command that wrote the message with the transformable object</param>
            <param name="Functionname">The command that wrote the message with the transformable object</param>
            <returns>Either a transform or null, if no fitting transform was found</returns>
        </member>
        <member name="T:PSFramework.Message.TransformType">
            <summary>
            The messaging system provides these kinds of transformations for input.
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformType.Target">
            <summary>
            A target transform can transform the target object specified. Used for live-state objects that should not be serialized on a second thread.
            </summary>
        </member>
        <member name="F:PSFramework.Message.TransformType.Exception">
            <summary>
            An exception transform allows automatic transformation of exceptions. Primarily used to unwrap exceptions from an API that wraps all exceptions.
            </summary>
        </member>
        <member name="T:PSFramework.Meta.CallerInfo">
            <summary>
            Helper Class parsing CallStackInfo into something usable
            </summary>
        </member>
        <member name="F:PSFramework.Meta.CallerInfo.CallerFunction">
            <summary>
            The name of the function in the callstackframe
            </summary>
        </member>
        <member name="F:PSFramework.Meta.CallerInfo.CallerModule">
            <summary>
            The name of the module the function in the callstackframe is part of
            </summary>
        </member>
        <member name="F:PSFramework.Meta.CallerInfo.CallerFile">
            <summary>
            The file this was called from
            </summary>
        </member>
        <member name="F:PSFramework.Meta.CallerInfo.CallerLine">
            <summary>
            The line in the file this was called from
            </summary>
        </member>
        <member name="M:PSFramework.Meta.CallerInfo.#ctor(System.Management.Automation.CallStackFrame)">
            <summary>
            Constructs the detailed information needed from a CallStackFrame
            </summary>
            <param name="Frame">The frame to parse</param>
        </member>
        <member name="M:PSFramework.Meta.CallerInfo.GetCaller(System.Int32)">
            <summary>
            Returns the caller CallStackFrame item
            </summary>
            <param name="Level">How many levels to peek into the callstack</param>
            <returns>Returns the caller CallStackFrame item</returns>
        </member>
        <member name="T:PSFramework.Meta.PipelineCommand">
            <summary>
            Information object for a command on the pipeline
            </summary>
        </member>
        <member name="F:PSFramework.Meta.PipelineCommand.PipelineId">
            <summary>
            ID of the pipeline the command was read from
            </summary>
        </member>
        <member name="F:PSFramework.Meta.PipelineCommand.Index">
            <summary>
            Index of the command within the pipeline
            </summary>
        </member>
        <member name="F:PSFramework.Meta.PipelineCommand.Command">
            <summary>
            Information on the command itself
            </summary>
        </member>
        <member name="P:PSFramework.Meta.PipelineCommand.InvocationName">
            <summary>
            Under what name was the command called?
            </summary>
        </member>
        <member name="F:PSFramework.Meta.PipelineCommand.InvocationInfo">
            <summary>
            The full invocation information
            </summary>
        </member>
        <member name="P:PSFramework.Meta.PipelineCommand.BoundParameters">
            <summary>
            The parameters that were bound to the command
            </summary>
        </member>
        <member name="F:PSFramework.Meta.PipelineCommand.CommandItem">
            <summary>
            The actual PowerShell internal object representing the command on the pipeline
            </summary>
        </member>
        <member name="M:PSFramework.Meta.PipelineCommand.IsCommand(System.Management.Automation.PSCmdlet)">
            <summary>
            Tests whether the specified cmdlet variable is the same instance of its class as this one.
            </summary>
            <param name="CmdletItem">The cmdlet to compare</param>
            <returns>Whether the specified cmdlet is the same instance as this one</returns>
        </member>
        <member name="M:PSFramework.Meta.PipelineCommand.#ctor(System.Int32,System.Int32,System.Management.Automation.CommandInfo,System.Management.Automation.InvocationInfo,System.Object)">
            <summary>
            Create a new pipelinecommand object
            </summary>
            <param name="PipelineId">ID of the pipeline the command was read from</param>
            <param name="Index">Index of the command within the pipeline</param>
            <param name="Command">Information on the command itself</param>
            <param name="InvocationInfo">The full invocation information</param>
            <param name="CommandItem">The actual PowerShell internal object representing the command on the pipeline</param>
        </member>
        <member name="T:PSFramework.Meta.Pipeline">
            <summary>
            Object representing a pipeline at runtime
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.InstanceId">
            <summary>
            The unique ID of the pipeline
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.StartTime">
            <summary>
            When the pipeline was created
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.Commands">
            <summary>
            The commands that make up the pipeline
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.Text">
            <summary>
            The full text of the pipeline
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.Ast">
            <summary>
            The Ast of the pipeline
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.OutputAssigned">
            <summary>
            Whether the output is getting assigned to something
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.OutputAssignedTo">
            <summary>
            What the output gets assigned to
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.InputFromVariable">
            <summary>
            Does the pipeline receive input from a variable?
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.InputVariable">
            <summary>
            What variable does the pipeline receive input from
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.InputDirect">
            <summary>
            Does the pipeline receive a constant as input value directly?
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.InputValue">
            <summary>
            What is the value it receives?
            </summary>
        </member>
        <member name="F:PSFramework.Meta.Pipeline.PipelineItem">
            <summary>
            The actual PowerShell internal pipeline object
            </summary>
        </member>
        <member name="T:PSFramework.Meta.PsfErrorRecord">
            <summary>
            A customized error record class, enabling write access to a few properties usually only readable
            </summary>
        </member>
        <member name="M:PSFramework.Meta.PsfErrorRecord.#ctor(System.Management.Automation.ErrorRecord,System.Exception)">
            <summary>
            Create an error record based off another record
            </summary>
            <param name="Record">The record to wrap</param>
            <param name="ReplaceParent">Replace the original exception with your own exception</param>
        </member>
        <member name="M:PSFramework.Meta.PsfErrorRecord.#ctor(System.Exception,System.String,System.Management.Automation.ErrorCategory,System.Object)">
            <summary>
            Create an error record based off an exception
            </summary>
            <param name="Error">The exception to wrap</param>
            <param name="ErrorId">An ID to attach to the error</param>
            <param name="Category">What kind of error happened</param>
            <param name="TargetObject">The target of the operation that failed</param>
        </member>
        <member name="M:PSFramework.Meta.PsfErrorRecord.#ctor(System.String,System.Management.Automation.ErrorCategory,System.String,System.Object)">
            <summary>
            Create a new error record, based off a message.
            </summary>
            <param name="Message">The message to build the error record around</param>
            <param name="Category">What kind of error happened</param>
            <param name="ErrorId">An ID to attach to the error</param>
            <param name="Target">The target of the operation that failed</param>
        </member>
        <member name="P:PSFramework.Meta.PsfErrorRecord.InvocationInfo">
            <summary>
            Information about the context where the error happened
            </summary>
        </member>
        <member name="P:PSFramework.Meta.PsfErrorRecord.ScriptStackTrace">
            <summary>
            The Stack Trace when things went bad
            </summary>
        </member>
        <member name="P:PSFramework.Meta.PsfErrorRecord.FullyQualifiedErrorId">
            <summary>
            Identify the error by its own, grand ID
            </summary>
        </member>
        <member name="M:PSFramework.Meta.PsfErrorRecord.SetStackTrace(PSFramework.Message.CallStack)">
            <summary>
            Applies a specified CallStack to the error record
            </summary>
            <param name="Callstack">The Callstack object to write</param>
        </member>
        <member name="M:PSFramework.Meta.PsfErrorRecord.GetException(System.String,System.Management.Automation.ErrorCategory)">
            <summary>
            Generate a new exception object from a message and an error category
            </summary>
            <param name="Message">The message to wrap into an exception</param>
            <param name="Category">The category of the error, determining the exception type</param>
            <returns>An exception</returns>
        </member>
        <member name="T:PSFramework.Parameter.ComputerParameter">
            <summary>
            Parameter class that converts various input into a unified input representation
            </summary>
        </member>
        <member name="P:PSFramework.Parameter.ComputerParameter.ComputerName">
            <summary>
            The resolved computername
            </summary>
        </member>
        <member name="P:PSFramework.Parameter.ComputerParameter.IsLocalhost">
            <summary>
            Whether the computername is actually localhost or one of its equivalents
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ComputerParameter.Type">
            <summary>
            What kind of object was passed. This makes it easier to detect and reuse specific kinds of live object in circumstances where that is desirable.
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.op_Implicit(PSFramework.Parameter.ComputerParameter)~System.String">
            <summary>
            Implicitly converts the parameter class to string
            </summary>
            <param name="Parameter">The parameter to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.ToString">
            <summary>
            Overrides the default ToString() method
            </summary>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.String)">
            <summary>
            Generates a Computer Parameter object from string
            </summary>
            <param name="ComputerName">The name to use as input</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.Net.IPAddress)">
            <summary>
            Creates a Computer Parameter from an IPAddress
            </summary>
            <param name="Address"></param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.Management.Automation.Runspaces.PSSession)">
            <summary>
            Creates a Computer Parameter from a PSSession
            </summary>
            <param name="Session">The session to use</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(PSFramework.ComputerManagement.SessionContainer)">
            <summary>
            Creates a Computer Parameter from a Session Container object.
            </summary>
            <param name="Container">A container that can be used to store multiple types of session objects in parallel.</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(PSFramework.ComputerManagement.PSSessionInfo)">
            <summary>
            Creates a Computer Parameter from a PSSessionInfo object.
            Shouldn't happen in the wild, but still a legal inmput.
            </summary>
            <param name="SessionInfo">The session information object to interpret</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(Microsoft.Management.Infrastructure.CimSession)">
            <summary>
            Creates a Computer Parameter from a CimSession
            </summary>
            <param name="Session">The session to create the parameter from</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.Net.NetworkInformation.PingReply)">
            <summary>
            Creates a Computer Parameter from the reply to a ping
            </summary>
            <param name="Ping">The result of a ping</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.Net.IPHostEntry)">
            <summary>
            Creates a Computer Parameter from the result of a dns resolution
            </summary>
            <param name="Entry">The result of a dns resolution</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.Data.SqlClient.SqlConnection)">
            <summary>
            Creates a Computer Parameter from an established SQL Connection
            </summary>
            <param name="Connection">The connection to use</param>
        </member>
        <member name="M:PSFramework.Parameter.ComputerParameter.#ctor(System.Object)">
            <summary>
            Generates a Computer Parameter object from anything
            </summary>
            <param name="InputObject"></param>
        </member>
        <member name="T:PSFramework.Parameter.ComputerParameterInputType">
            <summary>
            The kind of object that was bound to the parameter class
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ComputerParameterInputType.Default">
            <summary>
            The input object is just any non-special kind of input.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ComputerParameterInputType.PSSession">
            <summary>
            The input object is a PowerShell Session object
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ComputerParameterInputType.SMOServer">
            <summary>
            The input object is a live SMO Server object
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ComputerParameterInputType.CimSession">
            <summary>
            The input object is a live cim session object
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ComputerParameterInputType.Container">
            <summary>
            The input object is a session container object, potentially containing live session objects of various types at the same time.
            </summary>
        </member>
        <member name="T:PSFramework.Parameter.DateTimeParameter">
            <summary>
            Parameter class for interpreting timespans
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.DateTimeParameter.Value">
            <summary>
            The resolved datetime value
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.ToString">
            <summary>
            The string value of the datetime object contained within.
            </summary>
            <returns>The string value of the datetime object contained within.</returns>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.op_Implicit(PSFramework.Parameter.DateTimeParameter)~System.DateTime">
            <summary>
            Implicitly converts the parameter to datetime, allowing it to be used on parameters requiring the type
            </summary>
            <param name="Parameter">The parameterclass object to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.op_Implicit(System.DateTime)~PSFramework.Parameter.DateTimeParameter">
            <summary>
            Implicitly converts a datetime to this parameterclass object.
            </summary>
            <param name="Value">The timespan object to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.#ctor(System.DateTime)">
            <summary>
            Creates a DateTimeParameter from a DateTime object (not the hardest challenge)
            </summary>
            <param name="Value">The timespan object to accept</param>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.#ctor(System.Int32)">
            <summary>
            Creates a DateTimeParameter with a DateTime value in seconds relative to the specifiewd number.
            </summary>
            <param name="Seconds">The seconds to differ from the current time.</param>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.#ctor(System.String)">
            <summary>
            Creates a TimeSpanParameter from a string object
            </summary>
            <param name="Value">The string to interpret</param>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.#ctor(System.Object)">
            <summary>
            Creates a TimeSpanParameter from any kind of object it has been taught to understand
            </summary>
            <param name="InputObject">The object to interpret</param>
        </member>
        <member name="M:PSFramework.Parameter.DateTimeParameter.ParseDateTime(System.String)">
            <summary>
            Parses an input string as timespan
            </summary>
            <param name="Value">The string to interpret</param>
            <returns>The interpreted timespan value</returns>
        </member>
        <member name="T:PSFramework.Parameter.EncodingParameter">
            <summary>
            Class that interprets input as an encoding
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.EncodingParameter.Encoding">
            <summary>
            The parsed encoding to use.
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.op_Implicit(PSFramework.Parameter.EncodingParameter)~System.Text.Encoding">
            <summary>
            Implicitly convert the value to an encoding object
            </summary>
            <param name="Parameter">The parameterclass object to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.op_Implicit(System.Text.Encoding)~PSFramework.Parameter.EncodingParameter">
            <summary>
            Implicitly convert encoding to the parameterclass by calling the correct constructor
            </summary>
            <param name="Encoding">The encoding to encapsule</param>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.#ctor(System.Text.Encoding)">
            <summary>
            Encapsule an encoding object in the parameter class
            </summary>
            <param name="Encoding">The encoding to wrap</param>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.#ctor(System.String)">
            <summary>
            Converts a string value to encoding
            </summary>
            <param name="EncodingString">A string value to interpret as encoding.</param>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.#ctor(System.Object)">
            <summary>
            Convert just about anything to encoding if registered.
            </summary>
            <param name="InputObject">The object to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.GetEncoding(System.String)">
            <summary>
            Returns the correct encoding for a given string
            </summary>
            <param name="Text">The string to convert to encoding</param>
            <returns>The encoding to retrieve.</returns>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.GetEncodingString(System.Text.Encoding)">
            <summary>
            Returns the user-expected string from the specified encoding.
            "Expected" in the context of PowerShell usage.
            </summary>
            <param name="EncodingItem">The item to convert to string</param>
            <returns>The powershell string representation</returns>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.ToString">
            <summary>
            String representation of the parameter class
            </summary>
            <returns>The returned encoding string</returns>
        </member>
        <member name="M:PSFramework.Parameter.EncodingParameter.IsUTF8BOM(System.Text.Encoding)">
            <summary>
            Accepts a UTF8 encoding and returns, whether it includes writing a ByteOrderMark
            </summary>
            <param name="Encoding">The encoding object to interpret</param>
            <returns>Whether it is with BOM or without</returns>
        </member>
        <member name="T:PSFramework.Parameter.PathDirectoryLaxParameter">
            <summary>
            Interpret input values as directory paths, silently ignoring bad input
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Convert a single DirectoryInfo object
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.IO.DirectoryInfo[])">
            <summary>
            Convert any number of DirectoryInfo objects
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryLaxParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathFileSystemLaxParameter">
            <summary>
            Resolves any file or folder evaluating wildcards, will not throw on bad input
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Convert a single DirectoryInfo object
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.IO.DirectoryInfo[])">
            <summary>
            Convert any number of DirectoryInfo objects
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemLaxParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathFileSystemParameter">
            <summary>
            Resolves any file or folder evaluating wildcards
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Convert a single DirectoryInfo object
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.IO.DirectoryInfo[])">
            <summary>
            Convert any number of DirectoryInfo objects
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathFileSystemParameterBase">
            <summary>
            Base class for parameter classes related to filesystem path validation
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.PathFileSystemParameterBase.InputObject">
            <summary>
            The original item presented as input
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.PathFileSystemParameterBase.FailedInput">
            <summary>
            The input that failed
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.PathFileSystemParameterBase._PropertyMapping">
            <summary>
            Contains the list of property mappings.
            Types can be registered to it, allowing the parameter class to blindly interpret unknown types
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameterBase.SetTypePropertyMapping(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Assigns a property mapping for a given type, allowing the parameter class to handle unknown types
            </summary>
            <param name="Name">The name of the type. Use the FullName of the type</param>
            <param name="PropertyName">The property names to register. When parsing input, it will move down this list until a valid property was found</param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameterBase.AddEx(System.String)">
            <summary>
            Add a value if it is not yet included.
            Silently ignores values already in the ArrayList
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameterBase.op_Addition(PSFramework.Parameter.PathFileSystemParameterBase,PSFramework.Parameter.PathFileSystemParameterBase)">
            <summary>
            Add two parameter classes representing resolved paths.
            Mostly intended to converge literal and non-literal parameter options.
            Note: PowerShell will usually enumerate the result of this, leading to a simple object array
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Parameter.PathFileSystemParameterBase.ToString">
            <summary>
            The default string display style
            </summary>
            <returns></returns>
        </member>
        <member name="T:PSFramework.Parameter.ParameterClass">
            <summary>
            Base class of all parameter classes
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClass.InputObject">
            <summary>
            The original item presented as input
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClass._PropertyMapping">
            <summary>
            Contains the list of property mappings.
            Types can be registered to it, allowing the parameter class to blindly interpret unknown types
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.ParameterClass.SetTypePropertyMapping(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Assigns a property mapping for a given type, allowing the parameter class to handle unknown types
            </summary>
            <param name="Name">The name of the type. Use the FullName of the type</param>
            <param name="PropertyName">The property names to register. When parsing input, it will move down this list until a valid property was found</param>
        </member>
        <member name="M:PSFramework.Parameter.ParameterClass.ToString">
            <summary>
            Returns the string representation of the parameter. Should be overridden by inheriting classes.
            </summary>
            <returns>The string representation of the object</returns>
        </member>
        <member name="T:PSFramework.Parameter.ParameterClassAttribute">
            <summary>
            Attribute marking a class
            </summary>
        </member>
        <member name="T:PSFramework.Parameter.ParameterClasses">
            <summary>
            List of available parameter classes
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClasses.Computer">
            <summary>
            The computer parameter class allows easily targeting a computer
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClasses.DateTime">
            <summary>
            The datetime parameter class allows for an easy way to specify a datetime
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClasses.TimeSpan">
            <summary>
            The timespan parameter class allows for an easy way to specify a timespan
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClasses.Encoding">
            <summary>
            The encoding parameter class allows to consistently accept encodings as input.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterClasses.Path">
            <summary>
            The set of path parameter classes allow to resolve file system path designations into absolute paths
            </summary>
        </member>
        <member name="T:PSFramework.Parameter.ParameterContractAttribute">
            <summary>
            The attribute used to define the elements of a ParameterClass contract
            </summary>
        </member>
        <member name="P:PSFramework.Parameter.ParameterContractAttribute.Type">
            <summary>
            Returns the type of the element this attribute is supposed to be attached to.
            </summary>
        </member>
        <member name="P:PSFramework.Parameter.ParameterContractAttribute.Behavior">
            <summary>
            Returns the behavior to expect from the contracted element. This sets the expectations on how this element is likely to act.
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.ParameterContractAttribute.#ctor(PSFramework.Parameter.ParameterContractType,PSFramework.Parameter.ParameterContractBehavior)">
            <summary>
            Ceates a perfectly common parameter contract attribute. For use with all parameter classes' public elements.
            </summary>
            <param name="Type"></param>
            <param name="Behavior"></param>
        </member>
        <member name="T:PSFramework.Parameter.ParameterContractBehavior">
            <summary>
            Defines how this element will behave
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.NotContracted">
            <summary>
            This elements is not actually part of the contract. Generally you wouldn't want to add the attribute at all in that case. However, in some places it helps avoiding confusion.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.Mandatory">
            <summary>
            This element may never be null and must be considered in all assignments. Even if the element is de facto not nullable, all constructors must assign it.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.Optional">
            <summary>
            This element may contain data, but is not required to. In case of a method, it may simply do nothing
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.Failable">
            <summary>
            This method may throw an error when executing and should always be handled with try/catch. Use this on methods that use external calls.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.Arbiter">
            <summary>
            The content of the thus marked field determines the dependent's state. Generally, only if the arbiter is true, will the dependent elements be mandatory. This behavior may only be assigned to boolean fields.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.Conditional">
            <summary>
            This behavior can be assigned together with the 'Mandatory' behavior. It means the field is only mandatory if an arbiter field is present and set to true.
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractBehavior.Conversion">
            <summary>
            Converts content. Generally applied only to operators, but some methods may also convert information.
            </summary>
        </member>
        <member name="T:PSFramework.Parameter.ParameterContractType">
            <summary>
            Defines what kind of element is granted the contract
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractType.Field">
            <summary>
            The contracted element is a field containing a value
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractType.Method">
            <summary>
            The contracted element is a method, performing an action
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.ParameterContractType.Operator">
            <summary>
            The contracted element is an operator, facilitating type conversion. Generally into a dedicated object type this parameterclass abstracts.
            </summary>
        </member>
        <member name="T:PSFramework.Parameter.PathDirectoryParameter">
            <summary>
            Interpret input values as directory paths
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Convert a single DirectoryInfo object
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.IO.DirectoryInfo[])">
            <summary>
            Convert any number of DirectoryInfo objects
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathDirectoryParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathFileLaxParameter">
            <summary>
            Parameter class that will not throw on bad or empty input and will instead just return empty paths
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileLaxParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathFileParameter">
            <summary>
            Input Converter for file input
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathFileParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathLiteralLaxParameter">
            <summary>
            Input converter performing path evaluation without wildcard interpretation, not throwing errors on bad input
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Convert a single DirectoryInfo object
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.IO.DirectoryInfo[])">
            <summary>
            Convert any number of DirectoryInfo objects
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralLaxParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathLiteralParameter">
            <summary>
            Input converter performing path evaluation without wildcard interpretation
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Convert a single DirectoryInfo object
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.IO.DirectoryInfo[])">
            <summary>
            Convert any number of DirectoryInfo objects
            </summary>
            <param name="Directory"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathLiteralParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.PathNewFileParameter">
            <summary>
            Parameter class, converting input into the path to a file, where either the file or at least its parent folder already exist.
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.String)">
            <summary>
            Convert a single path
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.String[])">
            <summary>
            Convert any number of paths
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.IO.FileInfo)">
            <summary>
            Convert a single FileInfo object
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.IO.FileInfo[])">
            <summary>
            Convert any number of FileInfo objects
            </summary>
            <param name="File"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.Uri)">
            <summary>
            Convert a single Uri
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.Uri[])">
            <summary>
            Convert multiple Uris
            </summary>
            <param name="Uri"></param>
        </member>
        <member name="M:PSFramework.Parameter.PathNewFileParameter.#ctor(System.Object)">
            <summary>
            Convert anything else
            </summary>
            <param name="Input"></param>
        </member>
        <member name="T:PSFramework.Parameter.SelectAliasParameter">
            <summary>
            A parameter class for binding specific alias property mappings
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.SelectAliasParameter.Value">
            <summary>
            The aliases to map
            </summary>
        </member>
        <member name="P:PSFramework.Parameter.SelectAliasParameter.Aliases">
            <summary>
            The alias objects to use. Will be cached on first retrieval
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.SelectAliasParameter.#ctor(System.Collections.Hashtable)">
            <summary>
            Convert hashtables to alias properties
            </summary>
            <param name="Hashtable">A hashtable mapping alias name to reference property</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectAliasParameter.#ctor(System.String)">
            <summary>
            Parses a string input into one or multiple alias expressions.
            </summary>
            <param name="StringValue">the string to parse</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectAliasParameter.ToString">
            <summary>
            Builds the string display of the parameter class
            </summary>
            <returns>The string representation of the aliases to create</returns>
        </member>
        <member name="T:PSFramework.Parameter.SelectParameter">
            <summary>
            Class that automatically parses input chosen for the -Property parameter of Select-PSUObject
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.SelectParameter.Value">
            <summary>
            The value as Select-Object wants it
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.SelectParameter.#ctor(System.String)">
            <summary>
            Builds a property parameter from string
            </summary>
            <param name="Value">The string to interpret</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectParameter.#ctor(System.Collections.Hashtable)">
            <summary>
            Builds a select parameter from a hashtable (pretty straightforward)
            </summary>
            <param name="Hash">The hashtable to accept</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectParameter.ToString">
            <summary>
            The default string representation of the wrapped object
            </summary>
            <returns>The string representation of the wrapped object</returns>
        </member>
        <member name="T:PSFramework.Parameter.SelectScriptMethodParameter">
            <summary>
            Parameter class parsing various inputs for scriptmethod properties
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.SelectScriptMethodParameter.Value">
            <summary>
            The name to script mappings that make up the script properties or methods
            </summary>
        </member>
        <member name="P:PSFramework.Parameter.SelectScriptMethodParameter.Methods">
            <summary>
            Retrieve a list of script methods. Results will be automatically cached on first retrieval
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptMethodParameter.#ctor(System.Collections.Hashtable)">
            <summary>
            Convert hashtables to scriptmethod properties
            </summary>
            <param name="Hashtable">A hashtable mapping name to scriptblock</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptMethodParameter.#ctor(System.String)">
            <summary>
            Parses a string input into one or multiple alias expressions.
            </summary>
            <param name="StringValue">the string to parse</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptMethodParameter.#ctor(System.Management.Automation.ScriptBlock)">
            <summary>
            Create a script method from a scriptblock. Scriptblock is processed as string!
            </summary>
            <param name="ScriptBlock">The scriptblock to evaluate</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptMethodParameter.#ctor(System.Management.Automation.PSScriptMethod)">
            <summary>
            Create a scriptmethod from ... a scriptmethod!
            </summary>
            <param name="Method">The scriptmethod to integrate</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptMethodParameter.ToString">
            <summary>
            Returnd the string representation of the scriptmethod
            </summary>
            <returns></returns>
        </member>
        <member name="T:PSFramework.Parameter.SelectScriptPropertyParameter">
            <summary>
            Parameter class that handles script property input
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.SelectScriptPropertyParameter.Value">
            <summary>
            The actual properties
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptPropertyParameter.#ctor(System.Management.Automation.PSScriptProperty)">
            <summary>
            Create a script property from a script property
            </summary>
            <param name="Property">The property to add</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptPropertyParameter.#ctor(System.String)">
            <summary>
            Create a script property from a string
            </summary>
            <param name="StringValue">The string to interpret into a scriptproperty</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptPropertyParameter.#ctor(System.Management.Automation.ScriptBlock)">
            <summary>
            Create a script property from a scriptblock. Scriptblock is processed as string!
            </summary>
            <param name="ScriptBlock">The scriptblock to evaluate</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptPropertyParameter.#ctor(System.Collections.Hashtable)">
            <summary>
            Create one or multiple script properties from hashtable
            </summary>
            <param name="Hashtable">The hashtable to build from</param>
        </member>
        <member name="M:PSFramework.Parameter.SelectScriptPropertyParameter.ToString">
            <summary>
            Print things to string
            </summary>
            <returns>The string representation of these properties</returns>
        </member>
        <member name="T:PSFramework.Parameter.SortParameter">
            <summary>
            Input parser for wrapping around Sort-Object
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.SortParameter.Value">
            <summary>
            The value passed to Sort-Object
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.SortParameter.#ctor(System.String)">
            <summary>
            Parse Input strings
            </summary>
            <param name="Property">The string to parse as property to sort by</param>
        </member>
        <member name="M:PSFramework.Parameter.SortParameter.#ctor(System.Object)">
            <summary>
            Any other input is passed straight through
            </summary>
            <param name="InputObject">The input object to pass through</param>
        </member>
        <member name="M:PSFramework.Parameter.SortParameter.ToString">
            <summary>
            Returns the string representation of the resultant sort property
            </summary>
            <returns>Some text</returns>
        </member>
        <member name="T:PSFramework.Parameter.TimeSpanParameter">
            <summary>
            Parameter class for interpreting timespans
            </summary>
        </member>
        <member name="F:PSFramework.Parameter.TimeSpanParameter.Value">
            <summary>
            The resolved timespan value
            </summary>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.ToString">
            <summary>
            The string value of the timespan object contained within.
            </summary>
            <returns>The string value of the timespan object contained within.</returns>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.op_Implicit(PSFramework.Parameter.TimeSpanParameter)~System.TimeSpan">
            <summary>
            Implicitly converts the parameter to timespan, allowing it to be used on parameters requiring the type
            </summary>
            <param name="Parameter">The parameterclass object to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.op_Implicit(System.TimeSpan)~PSFramework.Parameter.TimeSpanParameter">
            <summary>
            Implicitly converts a timespan to this parameterclass object.
            </summary>
            <param name="Value">The timespan object to convert</param>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.#ctor(System.TimeSpan)">
            <summary>
            Creates a TimeSpanParameter from a TimeSpan object (not the hardest challenge)
            </summary>
            <param name="Value">The timespan object to accept</param>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.#ctor(System.Int32)">
            <summary>
            Creates a TimeSpanParameter from integer, assuming it to mean seconds
            </summary>
            <param name="Seconds">The seconds to run</param>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.#ctor(System.String)">
            <summary>
            Creates a TimeSpanParameter from a string object
            </summary>
            <param name="Value">The string to interpret</param>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.#ctor(System.Object)">
            <summary>
            Creates a TimeSpanParameter from any kind of object it has been taught to understand
            </summary>
            <param name="InputObject">The object to interpret</param>
        </member>
        <member name="M:PSFramework.Parameter.TimeSpanParameter.ParseTimeSpan(System.String)">
            <summary>
            Parses an input string as timespan
            </summary>
            <param name="Value">The string to interpret</param>
            <returns>The interpreted timespan value</returns>
        </member>
        <member name="T:PSFramework.PSFCore.DebugData">
            <summary>
            Debug data container
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.DebugData.Timestamp">
            <summary>
            TImestamp the data was created at
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.DebugData.Label">
            <summary>
            Label for the data written
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.DebugData.Data">
            <summary>
            Data submitted by the writer
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.DebugData.ScriptCallstack">
            <summary>
            Callstack at the moment of the call
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.DebugData.Runspace">
            <summary>
            Runspace the message was written from
            </summary>
        </member>
        <member name="M:PSFramework.PSFCore.DebugData.#ctor(System.String,System.Object)">
            <summary>
            Create debug information for troubleshooting purposes
            </summary>
            <param name="Data">The data to write</param>
            <param name="Label">A label to store with the data to better track its origin</param>
        </member>
        <member name="T:PSFramework.PSFCore.NoJeaCommandAttribute">
            <summary>
            Decorator attribute declaring a command in its entirety unsafe for making publicly available in JEA
            </summary>
        </member>
        <member name="T:PSFramework.PSFCore.NoJeaParameterAttribute">
            <summary>
            The thus tagged parameter is not safe to publish with JEA
            </summary>
        </member>
        <member name="T:PSFramework.PSFCore.PSFCoreHost">
            <summary>
            Class containing static settings and values used to globally handle things
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.PSFCoreHost.Unattended">
            <summary>
            Whether the current console is in unattended mode
            </summary>
        </member>
        <member name="P:PSFramework.PSFCore.PSFCoreHost.ModuleRoot">
            <summary>
            The path to where the module was imported from
            </summary>
        </member>
        <member name="M:PSFramework.PSFCore.PSFCoreHost.Initialize">
            <summary>
            Initializes the PSFramework library.
            Required for some components to work correctly.
            </summary>
        </member>
        <member name="M:PSFramework.PSFCore.PSFCoreHost.Uninitialize">
            <summary>
            Reverses the initialization of the PSFramework library.
            Should be called when destroying the main runspace
            </summary>
        </member>
        <member name="P:PSFramework.PSFCore.PSFCoreHost.DebugMode">
            <summary>
            The master switch to enable debug mode.
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.PSFCoreHost.VerboseDebug">
            <summary>
            Whether to write debug messages to the screen
            </summary>
        </member>
        <member name="P:PSFramework.PSFCore.PSFCoreHost.DebugQueueSize">
            <summary>
            The total capacity of the debug queue
            </summary>
        </member>
        <member name="M:PSFramework.PSFCore.PSFCoreHost.WriteDebug(System.String,System.Object)">
            <summary>
            Write a debug message
            </summary>
            <param name="Label">The label to apply to the data</param>
            <param name="Data">The data to write</param>
        </member>
        <member name="F:PSFramework.PSFCore.PSFCoreHost.DebugData">
            <summary>
            The data storage containing the debug messages.
            </summary>
        </member>
        <member name="P:PSFramework.PSFCore.PSFCoreHost.PSVersion">
            <summary>
            Reliably access the PowerShell Version
            </summary>
        </member>
        <member name="T:PSFramework.PSFCore.PsfInternalAttribute">
            <summary>
            Attribute designating something as reserved as PSFramework-internal.
            Changes to any component marked thus is not considered a breaking change.
            </summary>
        </member>
        <member name="F:PSFramework.PSFCore.PsfInternalAttribute.Description">
            <summary>
            Allows specifying a description or comments along with the attribute.
            </summary>
        </member>
        <member name="T:PSFramework.Extension.ScriptBlockExt">
            <summary>
            Class that extends the scriptblock type
            </summary>
        </member>
        <member name="M:PSFramework.Extension.ScriptBlockExt.DoInvokeReturnAsIs(System.Management.Automation.ScriptBlock,System.Boolean,System.Int32,System.Object,System.Object,System.Object,System.Object[])">
            <summary>
            Implements the internal copy of DoInvokeReturnAsIs
            </summary>
            <param name="ScriptBlock">The scriptblock to execute</param>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="ErrorHandlingBehavior">How to handle errors. 2 should be the default</param>
            <param name="DollerUnder">The value to make available as $_</param>
            <param name="Input">The value to make available to $input</param>
            <param name="ScriptThis">The value to make available as $this</param>
            <param name="Args">The value to make available as $args</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Extension.ScriptBlockExt.Clone(System.Management.Automation.ScriptBlock)">
            <summary>
            Clones the specified scriptblock maintaining its language mode.
            </summary>
            <param name="ScriptBlock">The Scriptblock to clone</param>
            <returns>A clone of the scriptblock with the languagemode intact</returns>
        </member>
        <member name="M:PSFramework.Extension.ScriptBlockExt.ToGlobal(System.Management.Automation.ScriptBlock)">
            <summary>
            Resets the current scriptblock's sessionstate to the current runspace's global sessionstate.
            </summary>
            <param name="ScriptBlock">The scriptblock to globalize</param>
            <returns>The globalized scriptblock</returns>
        </member>
        <member name="M:PSFramework.Extension.ScriptBlockExt.ToLocal(System.Management.Automation.ScriptBlock)">
            <summary>
            Resets the current scriptblock's sessionstate to the current runspace's current sessionstate.
            </summary>
            <param name="ScriptBlock">The scriptblock to import</param>
            <returns>The imported scriptblock</returns>
        </member>
        <member name="M:PSFramework.Extension.ScriptBlockExt.Import(System.Management.Automation.ScriptBlock,System.Boolean)">
            <summary>
            Resets the current scriptblock's sessionstate to either the current runspace's current sessionstate or its global sessionstate.
            </summary>
            <param name="ScriptBlock">The scriptblock to import</param>
            <param name="Global">Whether to import into the global sessionstate</param>
            <returns>The imported ScriptBlock</returns>
        </member>
        <member name="T:PSFramework.ResultCache.ResultCache">
            <summary>
            The class containing all things related to the result cache functionality.
            </summary>
        </member>
        <member name="P:PSFramework.ResultCache.ResultCache.Result">
            <summary>
            The actually cached result
            </summary>
        </member>
        <member name="F:PSFramework.ResultCache.ResultCache.Function">
            <summary>
            The function that wrote the cache.
            </summary>
        </member>
        <member name="P:PSFramework.ResultCache.ResultCache.Timestamp">
            <summary>
            Returns, when the cache was last set
            </summary>
        </member>
        <member name="M:PSFramework.ResultCache.ResultCache.Clear">
            <summary>
            Clears all cache properties to null
            </summary>
        </member>
        <member name="T:PSFramework.Runspace.RunspaceBoundValue">
            <summary>
            Wrapper class that offers the tools to make Values runspace specific
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceBoundValue.OfferDefaultValue">
            <summary>
            Whether the default value should be offered when asking from a runspace without custom settings
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceBoundValue.DefaultValue">
            <summary>
            The default value to offer
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceBoundValue.Values">
            <summary>
            The values available on a "per runspace" basis
            </summary>
        </member>
        <member name="P:PSFramework.Runspace.RunspaceBoundValue.Value">
            <summary>
            The value to offer or set, specific per runspace from which it is called
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValue.PurgeExpired">
            <summary>
            Removes all value entries whose corresponding Runspace has been destroyed
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValue.Dispose">
            <summary>
            Destruction logic, eliminating all data stored in the object.
            Since handles to this object are automatically stored and maintained, it is impossible to otherwise guarantee releasing the object's data for the GC.
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValue.#ctor">
            <summary>
            Create an empty runspace bound value object
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValue.#ctor(System.Object)">
            <summary>
            Create a runspace bound value object with its initial value
            </summary>
            <param name="Value">The object to set as the initial value</param>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValue.#ctor(System.Object,System.Boolean)">
            <summary>
            Create a runspace bound value object with its initial value
            </summary>
            <param name="Value">The object to set as the initial value</param>
            <param name="OfferDefaultValue">Whether the initial / default value should be offered when accessed from runspaces that do not have a runspace-local value</param>
        </member>
        <member name="T:PSFramework.Runspace.RunspaceBoundValueGeneric`1">
            <summary>
            Generic implementation of RunspaceBoundValue
            </summary>
            <typeparam name="T">The type to use for wrapping</typeparam>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceBoundValueGeneric`1.DefaultValue">
            <summary>
            The default value to offer
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceBoundValueGeneric`1.Values">
            <summary>
            The values available on a "per runspace" basis
            </summary>
        </member>
        <member name="P:PSFramework.Runspace.RunspaceBoundValueGeneric`1.Value">
            <summary>
            The value to offer or set, specific per runspace from which it is called
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValueGeneric`1.PurgeExpired">
            <summary>
            Removes all value entries whose corresponding Runspace has been destroyed
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValueGeneric`1.#ctor">
            <summary>
            Create an empty runspace bound value object
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValueGeneric`1.#ctor(`0)">
            <summary>
            Create a runspace bound value object with its initial value
            </summary>
            <param name="Value">The object to set as the initial value</param>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceBoundValueGeneric`1.#ctor(`0,System.Boolean)">
            <summary>
            Create a runspace bound value object with its initial value
            </summary>
            <param name="Value">The object to set as the initial value</param>
            <param name="OfferDefaultValue">Whether the initial / default value should be offered when accessed from runspaces that do not have a runspace-local value</param>
        </member>
        <member name="T:PSFramework.Runspace.RunspaceContainer">
            <summary>
            Class that contains the logic necessary to manage a unique runspace
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceContainer.Name">
            <summary>
            The name of the runspace.
            </summary>
        </member>
        <member name="P:PSFramework.Runspace.RunspaceContainer.RunspaceGuid">
            <summary>
            The Guid of the running Runspace
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.SetScript(System.Management.Automation.ScriptBlock)">
            <summary>
            Sets the script to execute in the runspace. Will NOT take immediate effect. Only after restarting the runspace will it be used.
            </summary>
            <param name="Script">The scriptblock to execute</param>
        </member>
        <member name="P:PSFramework.Runspace.RunspaceContainer.State">
            <summary>
            The state the runspace currently is in.
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.Start">
            <summary>
            Starts the Runspace.
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.SetName(System.Management.Automation.Runspaces.Runspace)">
            <summary>
            Sets the name on a runspace. This WILL FAIL for PowerShell v3!
            </summary>
            <param name="Runspace">The runspace to be named</param>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.Stop">
            <summary>
            Gracefully stops the Runspace
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.Kill">
            <summary>
            Very ungracefully kills the runspace. Use only in the most dire emergency.
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.SignalStopped">
            <summary>
            Signals the registered runspace has stopped execution
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceContainer.#ctor(System.String,System.Management.Automation.ScriptBlock)">
            <summary>
            Creates a new runspace container with the basic information needed
            </summary>
            <param name="Name">The name of the Runspace</param>
            <param name="Script">The code using the runspace logic</param>
        </member>
        <member name="T:PSFramework.Runspace.RunspaceHost">
            <summary>
            Provides hosting for all registered runspaces
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceHost.StopTimeoutSeconds">
            <summary>
            The number of seconds before a Stop command is interrupted and instead the runspace is gracelessly shut down.
            </summary>
        </member>
        <member name="P:PSFramework.Runspace.RunspaceHost.RbvCleanupInterval">
            <summary>
            The interval (in milliseonds) in which Runspace-Bound Values will be leaned up
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.RunspaceHost.Runspaces">
            <summary>
            The dictionary containing the definitive list of unique Runspace
            </summary>
        </member>
        <member name="P:PSFramework.Runspace.RunspaceHost._RunspaceBoundValues">
            <summary>
            List of all runspace bound values in use
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceHost.StartRbvTimer">
            <summary>
            Starts the timer that in the background will periodically clean up runspace-bound variable-values that no longer have a hosting runspace.
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceHost.StopRbvTimer">
            <summary>
            Stops the timer that in the background will periodically clean up runspace-bound variable-values that no longer have a hosting runspace.
            Should only be called when destroying the primary runspace.
            </summary>
        </member>
        <member name="M:PSFramework.Runspace.RunspaceHost.PurgeAllRunspaceBoundVariables">
            <summary>
            Purge all RBVs of datasets from all expired runspaces
            </summary>
        </member>
        <member name="T:PSFramework.Runspace.PsfRunspaceState">
            <summary>
            Contains the state a managed, unique runspace can be in.
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.PsfRunspaceState.Running">
            <summary>
            The runspace is up and running
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.PsfRunspaceState.Stopping">
            <summary>
            The runspace has received the stop order, but has not yet obeyed it
            </summary>
        </member>
        <member name="F:PSFramework.Runspace.PsfRunspaceState.Stopped">
            <summary>
            The runspace has followed its order to stop and is currently disabled
            </summary>
        </member>
        <member name="T:PSFramework.Serialization.ClixmlDataStyle">
            <summary>
            The serialization output options available
            </summary>
        </member>
        <member name="F:PSFramework.Serialization.ClixmlDataStyle.String">
            <summary>
            Serialize to and from string
            </summary>
        </member>
        <member name="F:PSFramework.Serialization.ClixmlDataStyle.Byte">
            <summary>
            Serialize to and from byte
            </summary>
        </member>
        <member name="T:PSFramework.Serialization.ClixmlSerializer">
            <summary>
            Class providing tools to convert to and from clixml, compressed and uncompressed, string and bytes
            </summary>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToByteCompressed(System.Object)">
            <summary>
            Converts an object into compressed bytes
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <returns>A compressed byte array containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToByteCompressed(System.Object,System.Int32)">
            <summary>
            Converts an object into compressed bytes
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <param name="Depth">The depth to which to serialize</param>
            <returns>A compressed byte array containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToByte(System.Object)">
            <summary>
            Converts an object into bytes
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <returns>A byte array containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToByte(System.Object,System.Int32)">
            <summary>
            Converts an object into bytes
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <param name="Depth">Overrrides the default serialization depth</param>
            <returns>A byte array containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToStringCompressed(System.Object)">
            <summary>
            Converts an object into compressed string
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <returns>A compressed string containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToStringCompressed(System.Object,System.Int32)">
            <summary>
            Converts an object into compressed string
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <param name="Depth">The depth to which to serialize</param>
            <returns>A compressed string containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToString(System.Object)">
            <summary>
            Converts an object into string
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <returns>A string containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.ToString(System.Object,System.Int32)">
            <summary>
            Converts an object into string
            </summary>
            <param name="Item">The arbitrary object to serialize</param>
            <param name="Depth">Overrrides the default serialization depth</param>
            <returns>A string containing the serialized inputobject</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.FromByteCompressed(System.Byte[])">
            <summary>
            Deserializes an object that was serialized to compressed bytes
            </summary>
            <param name="Bytes">The compressed bytes to deserialize into an object</param>
            <returns>The deserialized object</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.FromStringCompressed(System.String)">
            <summary>
            Deserializes an object that was serialized to compressed string
            </summary>
            <param name="String">The compressed string to deserialize into an object</param>
            <returns>The deserialized object</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.FromByte(System.Byte[])">
            <summary>
            Deserializes an object that was serialized to bytes
            </summary>
            <param name="Bytes">The bytes to deserialize into an object</param>
            <returns>The deserialized object</returns>
        </member>
        <member name="M:PSFramework.Serialization.ClixmlSerializer.FromString(System.String)">
            <summary>
            Deserializes an object that was serialized to string
            </summary>
            <param name="String">The string to deserialize into an object</param>
            <returns>The deserialized object</returns>
        </member>
        <member name="T:PSFramework.Serialization.SerializationTypeConverter">
            <summary>
            Typeconverter that does the heavy lifting of maintaining type integrity across process borders.
            </summary>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.CanConvertFrom(System.Object,System.Type)">
            <summary>
            Whether the source can be converted to its destination
            </summary>
            <param name="sourceValue">The value to convert</param>
            <param name="destinationType">The type to convert to</param>
            <returns>Whether this action is possible</returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.ConvertFrom(System.Object,System.Type,System.IFormatProvider,System.Boolean)">
            <summary>
            Converts an object
            </summary>
            <param name="sourceValue">The data to convert</param>
            <param name="destinationType">The type to convert to</param>
            <param name="formatProvider">This will be ignored, but must be present</param>
            <param name="ignoreCase">This will be ignored, but must be present</param>
            <returns>The converted object</returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.CanConvertTo(System.Object,System.Type)">
            <summary>
            Whether the input object can be converted to the Destination type
            </summary>
            <param name="sourceValue">Input value</param>
            <param name="destinationType">The type to convert to</param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.ConvertTo(System.Object,System.Type,System.IFormatProvider,System.Boolean)">
            <summary>
            Converts an object
            </summary>
            <param name="sourceValue">The data to convert</param>
            <param name="destinationType">The type to convert to</param>
            <param name="formatProvider">This will be ignored, but must be present</param>
            <param name="ignoreCase">This will be ignored, but must be present</param>
            <returns>The converted object</returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.RegisterAssemblyResolver">
            <summary>
            Registers an assembly resolving event
            </summary>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.CanSerialize(System.Object)">
            <summary>
            Whether an object can be serialized
            </summary>
            <param name="obj">The object to test</param>
            <returns>Whether the object can be serialized</returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.CanSerialize(System.Type)">
            <summary>
            Whether a type can be serialized
            </summary>
            <param name="type">The type to test</param>
            <returns>Whether the specified type can be serialized</returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.TypeIsSerializable(System.Type)">
            <summary>
            The validation check on whether a type is serializable
            </summary>
            <param name="type">The type to test</param>
            <returns>Returns whether that type can be serialized</returns>
        </member>
        <member name="M:PSFramework.Serialization.SerializationTypeConverter.GetSerializationData(System.Management.Automation.PSObject)">
            <summary>
            Used to obtain the information to write
            </summary>
            <param name="psObject">The object to dissect</param>
            <returns>A memory stream.</returns>
        </member>
        <member name="T:PSFramework.TabExpansion.CompletionResultComparer">
            <summary>
            Compares two completion results
            </summary>
        </member>
        <member name="M:PSFramework.TabExpansion.CompletionResultComparer.Compare(System.Management.Automation.CompletionResult,System.Management.Automation.CompletionResult)">
            <summary>
            Compares two completion results
            </summary>
            <param name="Completer1">Completer to compare</param>
            <param name="Completer2">Completer to compare</param>
            <returns>-1, 0 or 1</returns>
        </member>
        <member name="T:PSFramework.TabExpansion.PsfArgumentCompleterAttribute">
            <summary>
            Allows integrating PSFramework tab expansion by attribute, rather than command.
            </summary>
        </member>
        <member name="M:PSFramework.TabExpansion.PsfArgumentCompleterAttribute.#ctor(System.String)">
            <summary>
            Create an argument completer offering but the name of the registered completion
            </summary>
            <param name="Completion">The completion scriptblock to use to provide completion information</param>
        </member>
        <member name="M:PSFramework.TabExpansion.PsfArgumentCompleterAttribute.#ctor(System.Management.Automation.ScriptBlock)">
            <summary>
            Create an argument completer offering a scriptblock that is supposed to produce completion data.
            </summary>
            <param name="ScriptBlock">The scriptblock to use for completion data generation</param>
        </member>
        <member name="M:PSFramework.TabExpansion.PsfArgumentCompleterAttribute.#ctor(System.Management.Automation.ScriptBlock,System.Boolean)">
            <summary>
            Create an argument completer offering a scriptblock that is supposed to produce completion data.
            </summary>
            <param name="ScriptBlock">The scriptblock to use for completion data generation</param>
            <param name="Global">Whether to execute the scriptblock in the global context.</param>
        </member>
        <member name="M:PSFramework.TabExpansion.PsfArgumentCompleterAttribute.#ctor(System.Management.Automation.ScriptBlock,System.String)">
            <summary>
            Create an argument completer offering a scriptblock that is supposed to produce completion data.
            </summary>
            <param name="ScriptBlock">The scriptblock to use for completion data generation</param>
            <param name="Name">The name to assign to this completion. Must be unique per scriptblock</param>
        </member>
        <member name="M:PSFramework.TabExpansion.PsfArgumentCompleterAttribute.#ctor(System.Management.Automation.ScriptBlock,System.String,System.Boolean)">
            <summary>
            Create an argument completer offering a scriptblock that is supposed to produce completion data.
            </summary>
            <param name="ScriptBlock">The scriptblock to use for completion data generation</param>
            <param name="Name">The name to assign to this completion. Must be unique per scriptblock</param>
            <param name="Global">Whether to execute the scriptblock in the global context.</param>
        </member>
        <member name="T:PSFramework.TabExpansion.ScriptContainer">
            <summary>
            Regular container to store scripts in, that are used in TEPP
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.Name">
            <summary>
            The name of the scriptblock
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.ScriptBlock">
            <summary>
            The scriptblock doing the logic
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.LastExecution">
            <summary>
            The last time the scriptblock was called. Must be updated by the scriptblock itself
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.LastDuration">
            <summary>
            The time it took to run the last time
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.InnerScriptBlock">
            <summary>
            Scriptblock for users using simple-TEPP acceleration
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.ErrorRecords">
            <summary>
            THe errors that occured during scriptblock execution.
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.LastResult">
            <summary>
            The values the last search returned
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.LastCompletion">
            <summary>
            The completion data for the values the last search returned
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.LastResultsValidity">
            <summary>
            How long are previous values valid, before a new execution becomes necessary.
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.ScriptContainer.Global">
            <summary>
            Whether to execute the scriptblock in the global scope
            </summary>
        </member>
        <member name="P:PSFramework.TabExpansion.ScriptContainer.ShouldExecute">
            <summary>
            Returns whether a new refresh of tab completion should be executed.
            </summary>
        </member>
        <member name="M:PSFramework.TabExpansion.ScriptContainer.Invoke">
            <summary>
            Returns the correct results, either by executing the scriptblock or consulting the cache
            </summary>
            <returns></returns>
        </member>
        <member name="T:PSFramework.TabExpansion.TabExpansionHost">
            <summary>
            Class that handles the static fields supporting the ÜSFramework TabExpansion implementation
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.TabExpansionHost.Scripts">
            <summary>
            Field containing the scripts that were registered.
            </summary>
        </member>
        <member name="P:PSFramework.TabExpansion.TabExpansionHost.Cache">
            <summary>
            The cache used by scripts utilizing TabExpansionPlusPlus for PSFramework
            </summary>
        </member>
        <member name="M:PSFramework.TabExpansion.TabExpansionHost.RegisterCompletion(System.String,System.Management.Automation.ScriptBlock,PSFramework.TabExpansion.TeppScriptMode,PSFramework.Parameter.TimeSpanParameter,System.Boolean)">
            <summary>
            Registers a new completion scriptblock
            </summary>
            <param name="Name">The name of the completion scriptblock</param>
            <param name="ScriptBlock">The scriptblock that will provide completion data</param>
            <param name="Mode">What completion mode to use</param>
            <param name="CacheDuration">How long the gathered pieces of data remain valid.</param>
            <param name="Global">Whether to globalize scriptblocks prior to invocation.</param>
        </member>
        <member name="M:PSFramework.TabExpansion.TabExpansionHost.RegisterCompletion(System.String,System.Management.Automation.ScriptBlock,PSFramework.TabExpansion.TeppScriptMode,PSFramework.Parameter.TimeSpanParameter,System.Boolean,System.Boolean)">
            <summary>
            Registers a new completion scriptblock
            </summary>
            <param name="Name">The name of the completion scriptblock</param>
            <param name="ScriptBlock">The scriptblock that will provide completion data</param>
            <param name="Mode">What completion mode to use</param>
            <param name="CacheDuration">How long the gathered pieces of data remain valid.</param>
            <param name="Global">Whether to globalize scriptblocks prior to invocation.</param>
            <param name="PassThru">Parameter is ignored. Needed to have a second signature that returns the script container.</param>
        </member>
        <member name="F:PSFramework.TabExpansion.TabExpansionHost.SimpleCompletionScript">
            <summary>
            The script used for providing tabcompletion for completers using the simplified notation.
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.TabExpansionHost.InputCompletionTypeData">
            <summary>
            Dictionary containing a list of hashtables to explicitly add properties when completing for specific output types.
            Entries must have three properties:
            - Name (Name of Property)
            - Type (Type, not Typename, of the property. May be empty)
            - TypeKnown (Boolean, whether the type is known)
            Used by the Tab Completion: PSFramework-Input-ObjectProperty
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.TabExpansionHost.InputCompletionCommandData">
            <summary>
            Dictionary containing a list of hashtables to explicitly add properties when completing for specific commands
            Entries must have three properties:
            - Name (Name of Property)
            - Type (Type, not Typename, of the property. May be empty)
            - TypeKnown (Boolean, whether the type is known)
            Used by the Tab Completion: PSFramework-Input-ObjectProperty
            </summary>
        </member>
        <member name="T:PSFramework.TabExpansion.TeppScriptMode">
            <summary>
            Whether the user wants to use simple tepp, full tepp or auto-detect
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.TeppScriptMode.Simple">
            <summary>
            Simple mode. The scriptblock provided by the user is expected to provide a list of strings. All the rest is processed by the system
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.TeppScriptMode.Full">
            <summary>
            In full mode, the user is expected to provide the full TEPP scriptblock.
            </summary>
        </member>
        <member name="F:PSFramework.TabExpansion.TeppScriptMode.Auto">
            <summary>
            In Auto-Detect mode, the system detects, whether the user intended to provide a simple mode or full mode script. This is determined by whether the scriptblock contains a parameter block or not.
            </summary>
        </member>
        <member name="T:PSFramework.TaskEngine.CacheItem">
            <summary>
            Runtime item handling the various meta-data for the TaskEngineCache values.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.CacheItem.Module">
            <summary>
            The module the cache item belongs to.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.CacheItem.Name">
            <summary>
            Name of the cache item.
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.CacheItem.Value">
            <summary>
            Value stored in the item
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.CacheItem.Collector">
            <summary>
            Scriptblock to execute to gather data to cache
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.CacheItem.CollectorArgument">
            <summary>
            An argument to pass to the collector scriptblock
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.CacheItem.LastSet">
            <summary>
            When was the value last updated
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.CacheItem.Expiration">
            <summary>
            How long are the values valid
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.CacheItem.Expired">
            <summary>
            Whether the cqached data has expired
            </summary>
        </member>
        <member name="M:PSFramework.TaskEngine.CacheItem.#ctor(System.String,System.String)">
            <summary>
            Create a new CacheItem
            </summary>
            <param name="Module">The module the item belongs to</param>
            <param name="Name">The name of the item.</param>
        </member>
        <member name="M:PSFramework.TaskEngine.CacheItem.GetValue">
            <summary>
            Obtain value for cached item. Run collector script if necessary, prevent parallel collector execution.
            </summary>
            <returns>The cached values</returns>
        </member>
        <member name="T:PSFramework.TaskEngine.Priority">
            <summary>
            How high the priority of the task. Higher priority tasks take precedence over low priority tasks.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.Priority.Trivial">
            <summary>
            This task is completely trivial and can be done whenever there is some spare time for it
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.Priority.Low">
            <summary>
            The task is not very significant, but should be dealt with at some point
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.Priority.Medium">
            <summary>
            Average priority task
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.Priority.High">
            <summary>
            An important task that will take precedence over most other tasks
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.Priority.Critical">
            <summary>
            A task so critical, that it should be considered to move it to synchronous execution instead.
            </summary>
        </member>
        <member name="T:PSFramework.TaskEngine.PsfTask">
            <summary>
            An individual task assigned to the maintenance engine
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Name">
            <summary>
            The name of the task to execute. No duplciates are possible.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Description">
            <summary>
            A description of what the task is/should be doing
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Once">
            <summary>
            Whether the task should be done once only
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Enabled">
            <summary>
            Whether the task is enabled. Only enabled tasks will be executed
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Interval">
            <summary>
            The interval at which the task should be performed
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Delay">
            <summary>
            If the task need not be performed right away, it can be delayed, in order to prioritize more important initialization tasks
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Registered">
            <summary>
            When was the task first registered. Duplicate registration calls will not increment this value.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.LastExecution">
            <summary>
            When was the task last executed.
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.PsfTask.NextExecution">
            <summary>
            When is the task due next.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.LastDuration">
            <summary>
            The time the last execution took
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.Priority">
            <summary>
            How important is this task?
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.ScriptBlock">
            <summary>
            The task code to execute
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.PsfTask.IsDue">
            <summary>
            Whether the task is due and should be executed
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.PsfTask.Pending">
            <summary>
            Returns, whether there are any actions still pending. The Task Engine runspace will terminate if there are no pending tasks left.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.LastError">
            <summary>
            The last error the task had.
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.PsfTask.State">
            <summary>
            The current state of the task.
            </summary>
        </member>
        <member name="T:PSFramework.TaskEngine.TaskHost">
            <summary>
            Host class providing access to resources needed to schedule and execute tasks in the background
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskHost.Tasks">
            <summary>
            The register of available tasks.
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.TaskHost.HasDueTasks">
            <summary>
            Whether there are any due tasks
            </summary>
        </member>
        <member name="P:PSFramework.TaskEngine.TaskHost.HasPendingTasks">
            <summary>
            Whether there are any tasks that still have an action pending
            </summary>
        </member>
        <member name="M:PSFramework.TaskEngine.TaskHost.GetNextTask(System.String[])">
            <summary>
            Returns the next task to perform. Returns null when there are no more tasks to perform
            </summary>
            <param name="Exclusions">List of tasks not to return, even if they are ready to execute again. This is used to avoid one misconfigured task starving all lower priority tasks, by including all tasks that have already run in a given cycle.</param>
            <returns>The next task to perform.</returns>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskHost.Cache">
            <summary>
            Cache where modules can store cached data provided by tasks
            </summary>
        </member>
        <member name="M:PSFramework.TaskEngine.TaskHost.GetCacheItem(System.String,System.String)">
            <summary>
            Return a cache item
            </summary>
            <param name="Module">The module the cached data belongs to</param>
            <param name="Name">The cache entry the setting</param>
            <returns>The cache item storing data and potentially data gathering script.</returns>
        </member>
        <member name="M:PSFramework.TaskEngine.TaskHost.NewCacheItem(System.String,System.String)">
            <summary>
            Creates a new cache item
            </summary>
            <param name="Module"></param>
            <param name="Name"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.TaskEngine.TaskHost.TestCacheItem(System.String,System.String)">
            <summary>
            Return whether a given cache item has been created already.
            </summary>
            <param name="Module"></param>
            <param name="Name"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.TaskEngine.TaskHost.ClearExpiredCacheData">
            <summary>
            Clears expired data in order to vacate memory.
            </summary>
        </member>
        <member name="T:PSFramework.TaskEngine.TaskState">
            <summary>
            The state of the task or its previous execution
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskState.New">
            <summary>
            Task is new, hasn't executed yet
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskState.Running">
            <summary>
            Task is currently running
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskState.Completed">
            <summary>
            Task has completed
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskState.Pending">
            <summary>
            Task is pending another execution
            </summary>
        </member>
        <member name="F:PSFramework.TaskEngine.TaskState.Error">
            <summary>
            Task had an error
            </summary>
        </member>
        <member name="T:PSFramework.Temp.TempItem">
            <summary>
            A temporary item
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItem.Type">
            <summary>
            What kind of item is this?
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItem.Exists">
            <summary>
            Does the item still exist?
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItem.Timeout">
            <summary>
            When will this temporary item expire?
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItem.Name">
            <summary>
            Name of the item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItem.Module">
            <summary>
            The module that owns the item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItem.ProviderName">
            <summary>
            The name of the provider implementing this
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItem.Delete">
            <summary>
            Remove the temporary item. Must remove itself from the Parent's list once completed.
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItem.Parent">
            <summary>
            Parent container
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItem.LastError">
            <summary>
            The last error that happened to the TempItem.
            Usually errors when trying to run Delete()
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItem.ToString">
            <summary>
            String representation of this object
            </summary>
            <returns>some text</returns>
        </member>
        <member name="T:PSFramework.Temp.TempItemContainer">
            <summary>
            Container unit for temporary items.
            Ensures keeping track of and disposing any and all temporary items.
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemContainer.Items">
            <summary>
            List of all temporary items registered
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemContainer.Providers">
            <summary>
            The list of temp item providers
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemContainer.#ctor">
            <summary>
            Create a default container for temporary items
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemContainer.Clear">
            <summary>
            Remove all temp items, cleaning up behind yourself
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemContainer.ClearExpired">
            <summary>
            Remove all temp items that are bveyond their timeout
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemContainer.Get(System.String,System.String)">
            <summary>
            Returns all temp items that meet the module and name condition (using wildcard evaluation)
            </summary>
            <param name="Module">Module to search by</param>
            <param name="Name">Name of the TempItem to search by</param>
            <returns>A list of TempItems matching the search patterns.</returns>
        </member>
        <member name="T:PSFramework.Temp.TempItemDirectory">
            <summary>
            A temporary directory, that shall be deleted in due time
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemDirectory.Type">
            <summary>
            The fact that this is a file
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemDirectory.Exists">
            <summary>
            Whether the directory still exists
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemDirectory.ProviderName">
            <summary>
            The provider implementing this temp item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemDirectory.Path">
            <summary>
            The full path to the directory
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemDirectory.Delete">
            <summary>
            Delete the directory and remove it from the list of temporary items
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemDirectory.#ctor(System.String,System.String,System.String,PSFramework.Temp.TempItemContainer)">
            <summary>
            Create a new temporary directory object (directory itself is not created)
            </summary>
            <param name="Name">Name of the temp directory</param>
            <param name="Module">Name of the module owning the temp directory</param>
            <param name="Path">Path to the temporary directory</param>
            <param name="Parent">The parent container.</param>
        </member>
        <member name="T:PSFramework.Temp.TempItemFile">
            <summary>
            A temporary file, that shall be deleted in due time
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemFile.Type">
            <summary>
            The fact that this is a file
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemFile.Exists">
            <summary>
            Whether the file still exists
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemFile.ProviderName">
            <summary>
            The provider implementing this temp item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemFile.Path">
            <summary>
            The full path to the file
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemFile.Delete">
            <summary>
            Delete the file and remove it from the list of temporary items
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemFile.#ctor(System.String,System.String,System.String,PSFramework.Temp.TempItemContainer)">
            <summary>
            Create a new temporary file object (file itself is not created)
            </summary>
            <param name="Name">Name of the temp file</param>
            <param name="Module">Name of the module owning the temp file</param>
            <param name="Path">Path to the temporary file</param>
            <param name="Parent">The parent container.</param>
        </member>
        <member name="T:PSFramework.Temp.TempItemGeneric">
            <summary>
            A generic temporary item. Implementation logic provided by temporary item providers.
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemGeneric.Type">
            <summary>
            This is a generic one. Yay.
            </summary>
        </member>
        <member name="P:PSFramework.Temp.TempItemGeneric.Exists">
            <summary>
            Whether the temp item even exists.
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemGeneric.ExistsScript">
            <summary>
            Scriptblock verifying the existence of the item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemGeneric.DeleteScript">
            <summary>
            Scriptblock deleting the temporary item.
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemGeneric.Data">
            <summary>
            Data that differentiates this item from others using the same provider
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemGeneric.CreationData">
            <summary>
            Data that was returned when running the creation scriptblock
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemGeneric.Delete">
            <summary>
            Kill it with fire.
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemGeneric.ToString">
            <summary>
            String representation of this object
            </summary>
            <returns>some text</returns>
        </member>
        <member name="M:PSFramework.Temp.TempItemGeneric.#ctor(System.String,System.String,System.String,System.Collections.Hashtable,PSFramework.Temp.TempItemContainer,System.Object)">
            <summary>
            Creates a generic temporary item
            </summary>
            <param name="Name">Name of the temp item</param>
            <param name="Module">Name of the module owning the temp item</param>
            <param name="ProviderName">Name of the provider offering the implementation logic of this item</param>
            <param name="Data">Data defining this temp item</param>
            <param name="Parent">The parent container of this item</param>
            <param name="CreationData">Data that was returned when running the creation scriptblock</param>
        </member>
        <member name="T:PSFramework.Temp.TempItemProvider">
            <summary>
            Extensibility tool, providing logic to custom-deploy temporary items
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemProvider.Name">
            <summary>
            Name of the provider
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemProvider.CreationScript">
            <summary>
            Scriptblock to execute as you create the temporary item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemProvider.ExistsScript">
            <summary>
            Scriptblock verifying the existence of the item
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemProvider.DeleteScript">
            <summary>
            Scriptblock deleting the temporary item.
            </summary>
        </member>
        <member name="M:PSFramework.Temp.TempItemProvider.#ctor(System.String,System.Management.Automation.ScriptBlock,System.Management.Automation.ScriptBlock,System.Management.Automation.ScriptBlock)">
            <summary>
            Create a new TempItemProvider
            </summary>
            <param name="Name">Name of the provider</param>
            <param name="CreationScript">Scriptblock to execute as you create the temporary item</param>
            <param name="ExistsScript">Scriptblock verifying the existence of the item</param>
            <param name="DeleteScript">Scriptblock deleting the temporary item.</param>
        </member>
        <member name="T:PSFramework.Temp.TempItemType">
            <summary>
            What kind of temp item is the object?
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemType.File">
            <summary>
            A regular file
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemType.Directory">
            <summary>
            A regular folder / directory
            </summary>
        </member>
        <member name="F:PSFramework.Temp.TempItemType.Generic">
            <summary>
            A generic item type
            </summary>
        </member>
        <member name="T:PSFramework.Utility.DynamicContentDictionary">
            <summary>
            A dynamic content object that implements a dictionary
            </summary>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentDictionary.Value">
            <summary>
            The value of the dynamic content object
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentDictionary.#ctor(System.String,System.Object)">
            <summary>
            Creates a dynamic content object concurrent dictionary 
            </summary>
            <param name="Name">The name of the setting</param>
            <param name="Value">The initial value of the object</param>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentDictionary.Reset">
            <summary>
            Resets the stack by reestablishing an empty dictionary.
            </summary>
        </member>
        <member name="T:PSFramework.Utility.DynamicContentList">
            <summary>
            A dynamic content object that implements a list
            </summary>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentList.Value">
            <summary>
            The value of the dynamic content object
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentList.#ctor(System.String,System.Object)">
            <summary>
            Creates a dynamic content object concurrent list 
            </summary>
            <param name="Name">The name of the setting</param>
            <param name="Value">The initial value of the object</param>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentList.Count">
            <summary>
            How many items are currently listed
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentList.ToArray">
            <summary>
            Returns the current list content as array
            </summary>
            <returns>The current queue content</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentList.Add(System.Object)">
            <summary>
            Adds an item to the list
            </summary>
            <param name="Item">The item to add</param>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentList.Take">
            <summary>
            Returns an object if there is anything to take from the list
            </summary>
            <returns>The next queued item</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentList.Reset">
            <summary>
            Resets the stack by reestablishing an empty list.
            </summary>
        </member>
        <member name="T:PSFramework.Utility.DynamicContentObject">
            <summary>
            Class that contains a value that can be updated from another runspace
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObject.Values">
            <summary>
            The full dictionary of dynamic objects
            </summary>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentObject.List">
            <summary>
            List of all dynamic content objects registered
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.Set(System.String,System.Object,PSFramework.Utility.DynamicContentObjectType)">
            <summary>
            Sets the value of a dynamic content object, creating a new one if needed
            </summary>
            <param name="Name">The name of the object</param>
            <param name="Value">The value to set</param>
            <param name="Type">The type of dynamic content object to create (if creatable)</param>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.Get(System.String)">
            <summary>
            Returns the Dynamic Content Object under the specified name
            </summary>
            <param name="Name">The name of the object to return</param>
            <returns>The Dynamic Content Object selected</returns>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObject.Value">
            <summary>
            The value stored in this object
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObject.Name">
            <summary>
            The name of the object
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.ConcurrentQueue(System.Boolean)">
            <summary>
            Turns the value into a concurrent queue.
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.ConcurrentStack(System.Boolean)">
            <summary>
            Turns the value into a concurrent stack
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.ConcurrentList(System.Boolean)">
            <summary>
            Turns the value into a concurrent list
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.ConcurrentDictionary(System.Boolean)">
            <summary>
            TUrns the value into a concurrent dictionary with case-insensitive string keys
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.ToString">
            <summary>
            General string representation of the value
            </summary>
            <returns>The string representation of the value</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentObject.#ctor(System.String,System.Object)">
            <summary>
            Creates a named value object that can be updated in the background
            </summary>
            <param name="Name">The name of the item</param>
            <param name="Value">The value of the item</param>
        </member>
        <member name="T:PSFramework.Utility.DynamicContentObjectType">
            <summary>
            The kind of dynamic content object was specified
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObjectType.Common">
            <summary>
            A regular object was requested
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObjectType.Queue">
            <summary>
            A queue was requested
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObjectType.List">
            <summary>
            A list was requested
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObjectType.Stack">
            <summary>
            A stack was requested
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicContentObjectType.Dictionary">
            <summary>
            A dictionary was requested
            </summary>
        </member>
        <member name="T:PSFramework.Utility.DynamicContentQueue">
            <summary>
            A dynamic content object that implements a queue
            </summary>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentQueue.Value">
            <summary>
            The value of the dynamic content object
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentQueue.#ctor(System.String,System.Object)">
            <summary>
            Creates a dynamic content object concurrent queue 
            </summary>
            <param name="Name">The name of the setting</param>
            <param name="Value">The initial value of the object</param>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentQueue.Count">
            <summary>
            How many items are currently queued
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentQueue.ToArray">
            <summary>
            Returns the current queue content as array
            </summary>
            <returns>The current queue content</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentQueue.Enqueue(System.Object)">
            <summary>
            Adds an item to the queue
            </summary>
            <param name="Item">The item to add</param>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentQueue.Dequeue">
            <summary>
            Returns an object if there is anything to take from the queue
            </summary>
            <returns>The next queued item</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentQueue.Reset">
            <summary>
            Resets the queue by reestablishing an empty queue.
            </summary>
        </member>
        <member name="T:PSFramework.Utility.DynamicContentStack">
            <summary>
            A dynamic content object that implements a stack
            </summary>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentStack.Value">
            <summary>
            The value of the dynamic content object
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentStack.#ctor(System.String,System.Object)">
            <summary>
            Creates a dynamic content object concurrent stack 
            </summary>
            <param name="Name">The name of the setting</param>
            <param name="Value">The initial value of the object</param>
        </member>
        <member name="P:PSFramework.Utility.DynamicContentStack.Count">
            <summary>
            How many items are currently stacked
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentStack.ToArray">
            <summary>
            Returns the current stack content as array
            </summary>
            <returns>The current queue content</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentStack.Push(System.Object)">
            <summary>
            Adds an item to the stack
            </summary>
            <param name="Item">The item to add</param>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentStack.Pop">
            <summary>
            Returns an object if there is anything to take from the stack
            </summary>
            <returns>The next queued item</returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicContentStack.Reset">
            <summary>
            Resets the stack by reestablishing an empty stack.
            </summary>
        </member>
        <member name="T:PSFramework.Utility.DynamicTransformationAttribute">
            <summary>
            Transformation attribute that can convert PSObjects or hashtables to the target type, so long as it has an empty constructor
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicTransformationAttribute.TargetType">
            <summary>
            The type to convert to
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicTransformationAttribute.AllowNull">
            <summary>
            Whether a null object is acceptable as input
            </summary>
        </member>
        <member name="F:PSFramework.Utility.DynamicTransformationAttribute.RequiredProperties">
            <summary>
            List of properties that must be specified on the input object
            </summary>
        </member>
        <member name="M:PSFramework.Utility.DynamicTransformationAttribute.Transform(System.Management.Automation.EngineIntrinsics,System.Object)">
            <summary>
            Converts input to output
            </summary>
            <param name="engineIntrinsics"></param>
            <param name="inputData"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Utility.DynamicTransformationAttribute.#ctor(System.Type,System.String[])">
            <summary>
            Creates the basic attribute specifying the target type
            </summary>
            <param name="TargetType">The type to convert to</param>
            <param name="RequiredProperties">Properties to require</param>
        </member>
        <member name="T:PSFramework.Utility.LimitedConcurrentQueue`1">
            <summary>
            A wrapper around a queue with a limited size. Excess items will be discarded
            </summary>
        </member>
        <member name="P:PSFramework.Utility.LimitedConcurrentQueue`1.Size">
            <summary>
            The maximum length of the limited queue
            </summary>
        </member>
        <member name="M:PSFramework.Utility.LimitedConcurrentQueue`1.Enqueue(`0)">
            <summary>
            Enqueues an object to the collection
            </summary>
            <param name="Item">The object to enqueue</param>
        </member>
        <member name="M:PSFramework.Utility.LimitedConcurrentQueue`1.TryAdd(`0)">
            <summary>
            Adds an item to the collection
            </summary>
            <param name="Item">The item to add</param>
            <returns>Whether adding the item succeeded</returns>
        </member>
        <member name="M:PSFramework.Utility.LimitedConcurrentQueue`1.#ctor">
            <summary>
            Creates a new, empty collection limited to a default max size of 10
            </summary>
        </member>
        <member name="M:PSFramework.Utility.LimitedConcurrentQueue`1.#ctor(System.Int32)">
            <summary>
            Creates a new, empty collection limited to the specified size
            </summary>
            <param name="Size">The maximum size</param>
        </member>
        <member name="T:PSFramework.Utility.Number">
            <summary>
            Wrapper class around double to facilitate human-friendly formatting in object properties
            </summary>
        </member>
        <member name="F:PSFramework.Utility.Number.Value">
            <summary>
            The actual value being wrapped
            </summary>
        </member>
        <member name="M:PSFramework.Utility.Number.ToString">
            <summary>
            Provides a user friendly representation of a number
            </summary>
            <returns>A user friendly number format</returns>
        </member>
        <member name="P:PSFramework.Utility.Number.SegmentSize">
            <summary>
            Size of each segment before the decimal seaparator when displaying numbers
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Number.SegmentSeparator">
            <summary>
            The seperator used inbetween each segment when displaying numbers
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Number.DecimalSeparator">
            <summary>
            The decimal seperator when displaying numbers
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Number.DecimalDigits">
            <summary>
            When displaying numbers, how many digits after the decimal seperator will be shown?
            </summary>
        </member>
        <member name="M:PSFramework.Utility.Number.#ctor">
            <summary>
            Create an empty number
            </summary>
        </member>
        <member name="M:PSFramework.Utility.Number.#ctor(System.Double)">
            <summary>
            Create a number with an initial value
            </summary>
            <param name="Value">The initial value of the number</param>
        </member>
        <member name="M:PSFramework.Utility.Number.CompareTo(PSFramework.Utility.Number)">
            <inheritdoc cref="M:System.IComparable`1.CompareTo(`0)"/>
            <remarks>For sorting</remarks>
        </member>
        <member name="M:PSFramework.Utility.Number.CompareTo(System.Object)">
            <inheritdoc cref="M:System.IComparable.CompareTo(System.Object)"/>
            <remarks>For sorting</remarks>
            <exception cref="T:System.ArgumentException">If you compare with something invalid.</exception>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Addition(PSFramework.Utility.Number,PSFramework.Utility.Number)">
            <summary>
            Adds two sizes
            </summary>
            <param name="a">The first size to add</param>
            <param name="b">The second size to add</param>
            <returns>The sum of both sizes</returns>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Subtraction(PSFramework.Utility.Number,PSFramework.Utility.Number)">
            <summary>
            Substracts two sizes
            </summary>
            <param name="a">The first size to substract</param>
            <param name="b">The second size to substract</param>
            <returns>The difference between both sizes</returns>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Multiply(PSFramework.Utility.Number,System.Double)">
            <summary>
            Multiplies two sizes with each other
            </summary>
            <param name="a">The size to multiply</param>
            <param name="b">The size to multiply with</param>
            <returns>A multiplied size.</returns>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Division(PSFramework.Utility.Number,System.Double)">
            <summary>
            Divides one size by another. 
            </summary>
            <param name="a">The size to divide</param>
            <param name="b">The size to divide with</param>
            <returns>Divided size (note: Cut off)</returns>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Multiply(PSFramework.Utility.Number,PSFramework.Utility.Number)">
            <summary>
            Multiplies two sizes with each other
            </summary>
            <param name="a">The size to multiply</param>
            <param name="b">The size to multiply with</param>
            <returns>A multiplied size.</returns>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Division(PSFramework.Utility.Number,PSFramework.Utility.Number)">
            <summary>
            Divides one size by another.
            </summary>
            <param name="a">The size to divide</param>
            <param name="b">The size to divide with</param>
            <returns>Divided size (note: Cut off)</returns>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(System.Int32)~PSFramework.Utility.Number">
            <summary>
            Implicitly converts int to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(System.Decimal)~PSFramework.Utility.Number">
            <summary>
            Implicitly converts int to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(PSFramework.Utility.Number)~System.Int32">
            <summary>
            Implicitly converts size to int
            </summary>
            <param name="a">The size to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(System.Int64)~PSFramework.Utility.Number">
            <summary>
            Implicitly converts long to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(PSFramework.Utility.Number)~System.Int64">
            <summary>
            Implicitly converts size to long
            </summary>
            <param name="a">The size to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(System.String)~PSFramework.Utility.Number">
            <summary>
            Implicitly converts string to size
            </summary>
            <param name="a">The string to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(System.Double)~PSFramework.Utility.Number">
            <summary>
            Implicitly converts double to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Number.op_Implicit(PSFramework.Utility.Number)~System.Double">
            <summary>
            Implicitly converts size to double
            </summary>
            <param name="a">The size to convert</param>
        </member>
        <member name="T:PSFramework.Utility.PsfException">
            <summary>
            Simple wrapper around Exception to integrate the PSFramework localization feature.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.PsfException.String">
            <summary>
            The full string representing the message
            </summary>
        </member>
        <member name="F:PSFramework.Utility.PsfException.StringValues">
            <summary>
            Any values to format into the message string
            </summary>
        </member>
        <member name="F:PSFramework.Utility.PsfException.CallStack">
            <summary>
            The script callstack at the time this exception was generated.
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfException.Message">
            <summary>
            The localized message to show
            </summary>
        </member>
        <member name="M:PSFramework.Utility.PsfException.#ctor">
            <summary>
            Create an empty eception
            </summary>
        </member>
        <member name="M:PSFramework.Utility.PsfException.#ctor(System.String)">
            <summary>
            Create a simple exception including a message string
            </summary>
            <param name="String">The string representing the message</param>
        </member>
        <member name="M:PSFramework.Utility.PsfException.#ctor(System.String,System.Exception,System.Object[])">
            <summary>
            Create a full exception object with all metadata
            </summary>
            <param name="String">The string representing the message</param>
            <param name="InnerException">An inner exception to pass through</param>
            <param name="StringValues">Any values to format into the message</param>
        </member>
        <member name="T:PSFramework.Utility.PsfScriptBlock">
            <summary>
            Wrapper class around the traditional scriptblock class, extending its capabilities
            </summary>
        </member>
        <member name="F:PSFramework.Utility.PsfScriptBlock.ScriptBlock">
            <summary>
            The original Scriptblock
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.LanguageMode">
            <summary>
            The language mode the scriptblock is running under
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.Ast">
            <summary>
            The Ast of he scriptblock
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.Attributes">
            <summary>
            The Attributes that have been assigned to the scriptblock
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.DebuggerHidden">
            <summary>
            Whether the scriptblock is visible to the debugger
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.File">
            <summary>
            What file the scriptblock comes from
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.Id">
            <summary>
            Unique ID of the scriptblock
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.IsConfiguration">
            <summary>
            Whether the scriptblock is a DSC configuration
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.IsFilter">
            <summary>
            Whether the scriptblock is a filter
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.Module">
            <summary>
            The module the scriptblock belongs to.
            Don't rely on this, as it may be subject to change.
            </summary>
        </member>
        <member name="P:PSFramework.Utility.PsfScriptBlock.StartPosition">
            <summary>
            Some text metadata of the scriptblock
            </summary>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.ToString">
            <summary>
            Returns the string representation of the scriptblock
            </summary>
            <returns>the string representation of the scriptblock</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.GetNewClosure">
            <summary>
            Wraps the original Scriptblock method GetNewClosure()
            </summary>
            <returns>A copy of the ScriptBlock with a new closure.</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.InvokeEx(System.Boolean,System.Object,System.Object,System.Object,System.Boolean,System.Boolean,System.Object[])">
            <summary>
            Do a rich invocation of the scriptblock
            </summary>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="DollerUnder">The value to make available as $_</param>
            <param name="Input">The value to make available to $input</param>
            <param name="ScriptThis">The value to make available as $this</param>
            <param name="ImportContext">Whether to first import the scriptblock into the current Context.</param>
            <param name="ImportGlobal">When importing the ScriptBlock, import it into the global Context instead.</param>
            <param name="Args">The value to make available as $args</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.InvokeEx(System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Do a rich invocation of the scriptblock
            </summary>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="ImportContext">Whether to first import the scriptblock into the current Context.</param>
            <param name="ImportGlobal">When importing the ScriptBlock, import it into the global Context instead.</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.InvokeEx(System.Object,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Do a rich invocation of the scriptblock
            </summary>
            <param name="Value">The value to offer as argument / input for the scriptblock</param>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="ImportContext">Whether to first import the scriptblock into the current Context.</param>
            <param name="ImportGlobal">When importing the ScriptBlock, import it into the global Context instead.</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.InvokeGlobal(System.Object)">
            <summary>
            Invoke the Scriptblock rehomed to the global scope
            </summary>
            <param name="Value">The value - if any - to offer as argument / input for the scriptblock</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.InvokeLocal(System.Object)">
            <summary>
            Invoke the Scriptblock rehomed to the current sessionstate
            </summary>
            <param name="Value">The value - if any - to offer as argument / input for the scriptblock</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.Invoke(System.Object[])">
            <summary>
            Invoke the scriptblock in legacy mode.
            </summary>
            <param name="args">Arguments to pass into the scriptblock</param>
            <returns>The results of the invocation</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.ToGlobal">
            <summary>
            Return a global clone of the scriptblock
            </summary>
            <returns>A global clone of the scriptblock</returns>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.#ctor(System.Management.Automation.ScriptBlock)">
            <summary>
            Create a new PsfScriptBlock by wrapping a regular scriptblock.
            </summary>
            <param name="Script">The Scriptblock to wrap</param>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.#ctor(System.Management.Automation.ScriptBlock,System.Boolean)">
            <summary>
            Create a new PsfScriptBlock by wrapping a regular scriptblock.
            </summary>
            <param name="Script">The Scriptblock to wrap</param>
            <param name="Unwrap">Whether to unwrap a scriptblock. When a scriptblock only contains a scriptblock (as happens when importing from psd1), this causes the class to use the inner scriptblock.</param>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.op_Implicit(PSFramework.Utility.PsfScriptBlock)~System.Management.Automation.ScriptBlock">
            <summary>
            Implicitly convert PsfScriptblock to ScriptBlock
            </summary>
            <param name="Script">The PsfScriptBlock to convert</param>
        </member>
        <member name="M:PSFramework.Utility.PsfScriptBlock.op_Implicit(System.Management.Automation.ScriptBlock)~PSFramework.Utility.PsfScriptBlock">
            <summary>
            Implicitly convert ScriptBlock to PsfScriptblock
            </summary>
            <param name="Script">The ScriptBlock to convert</param>
        </member>
        <member name="T:PSFramework.Utility.RegexHelper">
            <summary>
            Static class that holds useful regex patterns, ready for use
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.HostName">
            <summary>
            Pattern that checks for a valid hostname
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.HostNameEx">
            <summary>
            Pattern that checks for valid hostnames within a larger text
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.IPv4">
            <summary>
            Pattern that checks for a valid IPv4 address
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.IPv4Ex">
            <summary>
            Pattern that checks for valid IPv4 addresses within a larger text
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.IPv6">
            <summary>
            Will match a valid IPv6 address
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.IPv6Ex">
            <summary>
            Will match any IPv6 address within a larger text
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.ComputerTarget">
            <summary>
            Will match any string that in its entirety represents a valid target for dns- or ip-based targeting. Combination of HostName, IPv4 and IPv6
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.Guid">
            <summary>
            Will match a valid Guid
            </summary>
        </member>
        <member name="F:PSFramework.Utility.RegexHelper.GuidEx">
            <summary>
            Will match any number of valid Guids in a larger text
            </summary>
        </member>
        <member name="T:PSFramework.Utility.ScriptBlockItem">
            <summary>
            A scriptblock container item
            </summary>
        </member>
        <member name="F:PSFramework.Utility.ScriptBlockItem.Name">
            <summary>
            Name of the scriptblock
            </summary>
        </member>
        <member name="P:PSFramework.Utility.ScriptBlockItem.ScriptBlock">
            <summary>
            The scriptblock stored
            </summary>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.IsAvailable">
            <summary>
            Checks, whether the scriptblock is available in a given runspace.
            </summary>
            <returns>whether the scriptblock is available in the current runspace</returns>
        </member>
        <member name="F:PSFramework.Utility.ScriptBlockItem.Global">
            <summary>
            Whether the scriptblock should be invoked as global scriptblock
            </summary>
        </member>
        <member name="F:PSFramework.Utility.ScriptBlockItem.Local">
            <summary>
            Whether the scriptblock is local to its respective runspace.
            If set to true, each scriptblock will be unavailable to any runspace other than the one that defined it.
            However, each runspace can define its own instance of a given scriptblock.
            </summary>
        </member>
        <member name="P:PSFramework.Utility.ScriptBlockItem.CountRetrieved">
            <summary>
            The number of times this scriptblock has been used
            </summary>
        </member>
        <member name="P:PSFramework.Utility.ScriptBlockItem.LastRetrieved">
            <summary>
            When the scriptblock has last been used
            </summary>
        </member>
        <member name="P:PSFramework.Utility.ScriptBlockItem.Tag">
            <summary>
            A list of tags so the scriptblock can be found
            </summary>
        </member>
        <member name="P:PSFramework.Utility.ScriptBlockItem.Description">
            <summary>
            Full-text description of the scriptblock
            </summary>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.#ctor(System.String,System.Management.Automation.ScriptBlock,System.Boolean,System.Boolean,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            Create a new scriptblock item by offering both name and code
            </summary>
            <param name="Name">The name of the scriptblock</param>
            <param name="ScriptBlock">The scriptblock</param>
            <param name="Global">Whether the scriptblock should be invoked as global scriptblock</param>
            <param name="Local">Whether the scriptblock is local to the current runspace only</param>
            <param name="Tag">An optional list of tags</param>
            <param name="Description">An optional description</param>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.InvokeGlobal(System.Object)">
            <summary>
            Invoke the Scriptblock rehomed to the global scope
            </summary>
            <param name="Value">The value - if any - to offer as argument / input for the scriptblock</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.Invoke(System.Object[])">
            <summary>
            Invoke the Scriptblock as configured
            </summary>
            <param name="args">The value - if any - to offer as argument for the scriptblock</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.InvokeEx(System.Boolean,System.Object,System.Object,System.Object,System.Boolean,System.Boolean,System.Object[])">
            <summary>
            Do a rich invocation of the scriptblock
            </summary>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="DollerUnder">The value to make available as $_</param>
            <param name="Input">The value to make available to $input</param>
            <param name="ScriptThis">The value to make available as $this</param>
            <param name="ImportContext">Whether to first import the scriptblock into the current Context.</param>
            <param name="ImportGlobal">When importing the ScriptBlock, import it into the global Context instead.</param>
            <param name="Args">The value to make available as $args</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.InvokeEx(System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Do a rich invocation of the scriptblock
            </summary>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="ImportContext">Whether to first import the scriptblock into the current Context.</param>
            <param name="ImportGlobal">When importing the ScriptBlock, import it into the global Context instead.</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="M:PSFramework.Utility.ScriptBlockItem.InvokeEx(System.Object,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Do a rich invocation of the scriptblock
            </summary>
            <param name="Value">The value to offer as argument / input for the scriptblock</param>
            <param name="UseLocalScope">Whether a new scope should be created for this</param>
            <param name="ImportContext">Whether to first import the scriptblock into the current Context.</param>
            <param name="ImportGlobal">When importing the ScriptBlock, import it into the global Context instead.</param>
            <returns>Whatever output this scriptblock generates</returns>
        </member>
        <member name="T:PSFramework.Utility.ScriptTransformationAttribute">
            <summary>
            Allow custom argument transformation logic defined in script
            </summary>
        </member>
        <member name="F:PSFramework.Utility.ScriptTransformationAttribute.Conversions">
            <summary>
            List of registered conversion logics
            </summary>
        </member>
        <member name="F:PSFramework.Utility.ScriptTransformationAttribute.Name">
            <summary>
            Name of the conversion logic to use
            </summary>
        </member>
        <member name="F:PSFramework.Utility.ScriptTransformationAttribute.TargetType">
            <summary>
            The target type we try to generate
            </summary>
        </member>
        <member name="M:PSFramework.Utility.ScriptTransformationAttribute.Transform(System.Management.Automation.EngineIntrinsics,System.Object)">
            <summary>
            Converts input to output
            </summary>
            <param name="engineIntrinsics"></param>
            <param name="inputData"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Utility.ScriptTransformationAttribute.#ctor(System.String,System.Type)">
            <summary>
            Creates a new and awesome transformation attribute
            </summary>
            <param name="Name"></param>
            <param name="TargetType"></param>
        </member>
        <member name="T:PSFramework.Utility.Size">
            <summary>
            Class that reports File size.
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Byte">
            <summary>
            Number of bytes contained in whatever object uses this object as a property
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Kilobyte">
            <summary>
            Kilobyte representation of the bytes
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Megabyte">
            <summary>
            Megabyte representation of the bytes
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Gigabyte">
            <summary>
            Gigabyte representation of the bytes
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Terabyte">
            <summary>
            Terabyte representation of the bytes
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Digits">
            <summary>
            Number if digits behind the dot.
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Size.Style">
            <summary>
            How the size object should be displayed.
            </summary>
        </member>
        <member name="M:PSFramework.Utility.Size.ToString">
            <summary>
            Shows the default string representation of size
            </summary>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Utility.Size.Equals(System.Object)">
            <summary>
            Simple equality test
            </summary>
            <param name="obj">The object to test it against</param>
            <returns>True if equal, false elsewise</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.GetHashCode">
            <inheritdoc cref="M:System.Int64.GetHashCode"/>
            <remarks>The hashcode of the underlying size</remarks>
        </member>
        <member name="M:PSFramework.Utility.Size.#ctor">
            <summary>
            Creates an empty size.
            </summary>
        </member>
        <member name="M:PSFramework.Utility.Size.#ctor(System.Int64)">
            <summary>
            Creates a size with some content
            </summary>
            <param name="Byte">The length in bytes to set the size to</param>
        </member>
        <member name="M:PSFramework.Utility.Size.CompareTo(PSFramework.Utility.Size)">
            <inheritdoc cref="M:System.IComparable`1.CompareTo(`0)"/>
            <remarks>For sorting</remarks>
        </member>
        <member name="M:PSFramework.Utility.Size.CompareTo(System.Object)">
            <inheritdoc cref="M:System.IComparable.CompareTo(System.Object)"/>
            <remarks>For sorting</remarks>
            <exception cref="T:System.ArgumentException">If you compare with something invalid.</exception>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Addition(PSFramework.Utility.Size,PSFramework.Utility.Size)">
            <summary>
            Adds two sizes
            </summary>
            <param name="a">The first size to add</param>
            <param name="b">The second size to add</param>
            <returns>The sum of both sizes</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Subtraction(PSFramework.Utility.Size,PSFramework.Utility.Size)">
            <summary>
            Substracts two sizes
            </summary>
            <param name="a">The first size to substract</param>
            <param name="b">The second size to substract</param>
            <returns>The difference between both sizes</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Multiply(PSFramework.Utility.Size,System.Double)">
            <summary>
            Multiplies two sizes with each other
            </summary>
            <param name="a">The size to multiply</param>
            <param name="b">The size to multiply with</param>
            <returns>A multiplied size.</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Division(PSFramework.Utility.Size,System.Double)">
            <summary>
            Divides one size by another. 
            </summary>
            <param name="a">The size to divide</param>
            <param name="b">The size to divide with</param>
            <returns>Divided size (note: Cut off)</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Multiply(PSFramework.Utility.Size,PSFramework.Utility.Size)">
            <summary>
            Multiplies two sizes with each other
            </summary>
            <param name="a">The size to multiply</param>
            <param name="b">The size to multiply with</param>
            <returns>A multiplied size.</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Division(PSFramework.Utility.Size,PSFramework.Utility.Size)">
            <summary>
            Divides one size by another.
            </summary>
            <param name="a">The size to divide</param>
            <param name="b">The size to divide with</param>
            <returns>Divided size (note: Cut off)</returns>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(System.Int32)~PSFramework.Utility.Size">
            <summary>
            Implicitly converts int to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(System.Decimal)~PSFramework.Utility.Size">
            <summary>
            Implicitly converts int to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(PSFramework.Utility.Size)~System.Int32">
            <summary>
            Implicitly converts size to int
            </summary>
            <param name="a">The size to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(System.Int64)~PSFramework.Utility.Size">
            <summary>
            Implicitly converts long to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(PSFramework.Utility.Size)~System.Int64">
            <summary>
            Implicitly converts size to long
            </summary>
            <param name="a">The size to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(System.String)~PSFramework.Utility.Size">
            <summary>
            Implicitly converts string to size
            </summary>
            <param name="a">The string to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(System.Double)~PSFramework.Utility.Size">
            <summary>
            Implicitly converts double to size
            </summary>
            <param name="a">The number to convert</param>
        </member>
        <member name="M:PSFramework.Utility.Size.op_Implicit(PSFramework.Utility.Size)~System.Double">
            <summary>
            Implicitly converts size to double
            </summary>
            <param name="a">The size to convert</param>
        </member>
        <member name="T:PSFramework.Utility.SizeStyle">
            <summary>
            How size objects should be displayed
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Dynamic">
            <summary>
            The size object is styled dependend on the number stored within.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Plain">
            <summary>
            The size object is shown as a plain number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Byte">
            <summary>
            The size object is styled as a byte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.B">
            <summary>
            The size object is styled as a byte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Kilobyte">
            <summary>
            The size object is styled as a kilobyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.KB">
            <summary>
            The size object is styled as a kilobyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Megabyte">
            <summary>
            The size object is styled as a megabyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.MB">
            <summary>
            The size object is styled as a megabyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Gigabyte">
            <summary>
            The size object is styled as a Gigabyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.GB">
            <summary>
            The size object is styled as a Gigabyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.Terabyte">
            <summary>
            The size object is styled as a Terabyte number
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SizeStyle.TB">
            <summary>
            The size object is styled as a Terabyte number
            </summary>
        </member>
        <member name="T:PSFramework.Utility.SupportData">
            <summary>
            List of data items New-PSFSupportPackage will export
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.None">
            <summary>
            Nothing. Be stingy, will ya?!
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Message">
            <summary>
            All messages logged to the PSFramework that are still in memory
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.ErrorMessage">
            <summary>
            All error records logged to the PSFramework that are still in memory
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Screenshot">
            <summary>
            A "screenshot" record of the console buffer
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.OperatingSystem">
            <summary>
            Data about the OS, gathered from WMI
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.CPU">
            <summary>
            Data about the processors, gathered from WMI
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Ram">
            <summary>
            Data about the ram, gathered from WMI
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.PSVersion">
            <summary>
            Data about the PowerShell version
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.History">
            <summary>
            Input history
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Module">
            <summary>
            List of modules that are imported
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.SnapIns">
            <summary>
            List of snapins that are loaded
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Assemblies">
            <summary>
            List of assmeblies that have been imported
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Exceptions">
            <summary>
            All exception records written to the global $error variable
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.ExtensionData">
            <summary>
            Data provided by foreign modules
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.All">
            <summary>
            All data ill be exported
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Messages">
            <summary>
            PSFramework messages
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Critical">
            <summary>
            The most critical error data, including: Messages, error messages, error records and PSVersion
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.PSResource">
            <summary>
            PSVersion, Modules, Snapins and Assemblies
            </summary>
        </member>
        <member name="F:PSFramework.Utility.SupportData.Environment">
            <summary>
            Operating System, CPU &amp; RAM
            </summary>
        </member>
        <member name="T:PSFramework.Utility.Throttle">
            <summary>
            Class implementing a throttling mechanism / watcher
            </summary>
        </member>
        <member name="F:PSFramework.Utility.Throttle.Limit">
            <summary>
            The maximum number of slots per interval
            </summary>
        </member>
        <member name="F:PSFramework.Utility.Throttle.Interval">
            <summary>
            The interval over which slots are limited
            </summary>
        </member>
        <member name="P:PSFramework.Utility.Throttle.Count">
            <summary>
            The number of slots currently taken
            </summary>
        </member>
        <member name="M:PSFramework.Utility.Throttle.#ctor(System.Int32,PSFramework.Parameter.TimeSpanParameter)">
            <summary>
            Create a new throttle object
            </summary>
            <param name="Limit">How many slots are available per interval?</param>
            <param name="Interval">hat is the interval over which slots are measured?</param>
        </member>
        <member name="M:PSFramework.Utility.Throttle.GetSlot(PSFramework.Parameter.TimeSpanParameter)">
            <summary>
            Obtain an execution slots from the throttle
            </summary>
            <param name="Timeout">How long are you willing to wait for a slot before giving up?</param>
        </member>
        <member name="M:PSFramework.Utility.Throttle.Purge">
            <summary>
            Clean up any expired slots
            </summary>
        </member>
        <member name="T:PSFramework.Utility.TimeRange">
            <summary>
            Container carrying a time range.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.TimeRange.Start">
            <summary>
            The start time of the time range
            </summary>
        </member>
        <member name="F:PSFramework.Utility.TimeRange.End">
            <summary>
            The end time of the time range
            </summary>
        </member>
        <member name="P:PSFramework.Utility.TimeRange.Duration">
            <summary>
            The total duration of the time range
            </summary>
        </member>
        <member name="M:PSFramework.Utility.TimeRange.#ctor">
            <summary>
            Create an empty time range
            </summary>
        </member>
        <member name="M:PSFramework.Utility.TimeRange.#ctor(System.DateTime)">
            <summary>
            Create a time range with the start filled in
            </summary>
            <param name="Start">The start time of the time range</param>
        </member>
        <member name="M:PSFramework.Utility.TimeRange.#ctor(System.DateTime,System.DateTime)">
            <summary>
            Create a fully filled out time range
            </summary>
            <param name="Start">The start time of the time range</param>
            <param name="End">The end time of the time range</param>
        </member>
        <member name="M:PSFramework.Utility.TimeRange.IsInRange(System.DateTime)">
            <summary>
            Checks whether a DateTime is within the defined start and end times.
            </summary>
            <param name="Timestamp">The timestamp to validate</param>
            <returns>Whether the timestamp is within the defined start and end times</returns>
        </member>
        <member name="T:PSFramework.Utility.TimeRangeContainer">
            <summary>
            A management wrapper handlign a series of TimeRanges.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.TimeRangeContainer.TimeRanges">
            <summary>
            The stored time ranges
            </summary>
        </member>
        <member name="M:PSFramework.Utility.TimeRangeContainer.Start(System.DateTime)">
            <summary>
            Start a new time range
            </summary>
            <param name="Start">The starting point of the time range.</param>
        </member>
        <member name="M:PSFramework.Utility.TimeRangeContainer.End(System.DateTime)">
            <summary>
            End the last open time range
            </summary>
            <param name="End">The end time of the time range</param>
        </member>
        <member name="M:PSFramework.Utility.TimeRangeContainer.IsInRange(System.DateTime)">
            <summary>
            Tests, whether the input is within any of the stored ranges.
            </summary>
            <param name="Timestamp">The timestamp to test for being in range.</param>
            <returns>Whether the timestamp actually is in any of the possible ranges.</returns>
        </member>
        <member name="M:PSFramework.Utility.TimeRangeContainer.RemoveBefore(System.DateTime)">
            <summary>
            Removes all time ranges that have ended before the specified timestamp
            </summary>
            <param name="Timestamp">The delimiting timestamp</param>
        </member>
        <member name="T:PSFramework.Utility.TypeTransformationAttribute">
            <summary>
            Tries to convert input type into the target type, using PowerShell type coercion.
            Implemented to have a bool parameter accept a switch value.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.TypeTransformationAttribute.TargetType">
            <summary>
            The type to convert to
            </summary>
        </member>
        <member name="M:PSFramework.Utility.TypeTransformationAttribute.Transform(System.Management.Automation.EngineIntrinsics,System.Object)">
            <summary>
            Converts input to output
            </summary>
            <param name="engineIntrinsics"></param>
            <param name="inputData"></param>
            <returns></returns>
        </member>
        <member name="M:PSFramework.Utility.TypeTransformationAttribute.#ctor(System.Type)">
            <summary>
            Creates the basic attribute specifying the target type
            </summary>
            <param name="TargetType">The type to convert to</param>
        </member>
        <member name="T:PSFramework.Utility.UtilityHost">
            <summary>
            Contains static resources of various kinds. Primarily for internal consumption.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.PrimaryRunspace">
            <summary>
            The ID for the primary (or front end) Runspace. Used for stuff that should only happen on the user-runspace.
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.SizeDigits">
            <summary>
            The number of digits a size object shows by default
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.SizeStyle">
            <summary>
            The way size objects are usually displayed
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.NumberSegmentSize">
            <summary>
            Size of each segment before the decimal seaparator when displaying numbers
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.NumberSegmentSeparator">
            <summary>
            The seperator used inbetween each segment when displaying numbers
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.NumberDecimalSeparator">
            <summary>
            The decimal seperator when displaying numbers
            </summary>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.NumberDecimalDigits">
            <summary>
            When displaying numbers, how many digits after the decimal seperator will be shown?
            </summary>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.IsLocalhost(System.String)">
            <summary>
            Tests whether a given string is the local host.
            Does NOT use DNS resolution, DNS aliases will NOT be recognized!
            </summary>
            <param name="Name">The name to test for being local host</param>
            <returns>Whether the name is localhost</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.IsValidComputerTarget(System.String)">
            <summary>
            Tests whether a given string is a valid target for targeting as a computer. Will first convert from idn name.
            </summary>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.IsLike(System.String,System.String,System.Boolean)">
            <summary>
            Implement's VB's Like operator logic.
            </summary>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.CharListToSet(System.String)">
            <summary>
            Converts a string of characters to a HashSet of characters. If the string
            contains character ranges, such as A-Z, all characters in the range are
            also added to the returned set of characters.
            </summary>
            <param name="charList">Character list string</param>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.CompressString(System.String)">
            <summary>
            Compress string using default zip algorithms
            </summary>
            <param name="String">The string to compress</param>
            <returns>Returns a compressed string.</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.ExpandString(System.String)">
            <summary>
            Expand a string using default zig algorithms
            </summary>
            <param name="CompressedString">The compressed string to expand</param>
            <returns>Returns an expanded string.</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetExecutionContextFromTLS">
            <summary>
            Returns the execution context for the current runspace based on the current thread.
            </summary>
            <returns>The current execution context</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetRunspaces">
            <summary>
            Returns the list of runspaces available in the process
            </summary>
            <returns>The lists of currently known runspaces</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.RemovePowerShellAlias(System.String,System.Boolean)">
            <summary>
            Removes an alias from the global list of aliases
            </summary>
            <param name="Name">The name of the laias to kill</param>
            <param name="Force">Whether to remove ReadOnly items</param>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPrivateProperty(System.String,System.Object)">
            <summary>
            Returns the value of a private property on an object
            </summary>
            <param name="Name">The name of the property</param>
            <param name="Instance">The object from which to read the property from</param>
            <returns>The value of the property content (may be null)</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.SetPrivateProperty(System.String,System.Object,System.Object)">
            <summary>
            Updates the value of a private property
            </summary>
            <param name="Name">The name of the property to update</param>
            <param name="Instance">The object that contains the property to update</param>
            <param name="Value">The value to apply</param>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPublicProperty(System.String,System.Object)">
            <summary>
            Returns the value of a public property on an object
            </summary>
            <param name="Name">The name of the property</param>
            <param name="Instance">The object from which to read the property from</param>
            <returns>The value of the property content (may be null)</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPrivateStaticProperty(System.Type,System.String)">
            <summary>
            Returns the value of a private static property on a type
            </summary>
            <param name="StaticType">The type to pick from</param>
            <param name="Name">The name of the property to retrieve</param>
            <returns>The value of the property content (may be null)</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPrivateStaticField(System.Type,System.String)">
            <summary>
            Returns the value of a private static field on a type
            </summary>
            <param name="StaticType">The type to pick from</param>
            <param name="Name">The name of the field to retrieve</param>
            <returns>The value of the field content (may be null)</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPrivateField(System.String,System.Object)">
            <summary>
            Returns the value of a private field on an object
            </summary>
            <param name="Name">The name of the field</param>
            <param name="Instance">The object from which to read the field from</param>
            <returns>The value of the field content (may be null)</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPrivateStaticMethod(System.Type,System.String)">
            <summary>
            Returns a static private method of a type
            </summary>
            <param name="StaticType">The type to search in</param>
            <param name="Name">The name of the method to retrieve</param>
            <returns>The method object requested</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetPrivateMethod(System.String,System.Object)">
            <summary>
            Returns the private method on an object
            </summary>
            <param name="Name">The name of the method</param>
            <param name="Instance">The object from which to read the method from</param>
            <returns>The method sought</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.InvokePrivateMethod(System.String,System.Object,System.Object[])">
            <summary>
            Executes a private method of an object
            </summary>
            <param name="Name">Name of the method to invoke</param>
            <param name="Instance">The object whose method to run</param>
            <param name="Arguments">Arguments to pass to the method</param>
            <returns>Whatever the private method may return</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.InvokePrivateStaticMethod(System.Type,System.String,System.Object[])">
            <summary>
            Executes a static private method of a type
            </summary>
            <param name="StaticType">The type containing the method</param>
            <param name="Name">The name of the method to invoke</param>
            <param name="Arguments">The arguments to pass to the method</param>
            <returns>The result of the method invoked</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.InvokeConstructor(System.Type,System.Object[],System.Reflection.BindingFlags)">
            <summary>
            Execute a constructor for the specified type, creating a new instance of it.
            It will try each constructor with the correct number of arguments until it finds a working one, so ... not REALLY efficient.
            May have side-effects when used on types that do significant logic in their constructors even when they fail.
            </summary>
            <param name="Type">The type to create an instance of.</param>
            <param name="Arguments">Arguments to pass to the constructor.</param>
            <param name="Flags">Search flags selecting the constructor. Defaults to both public and private.</param>
            <returns>The created instance of the type</returns>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.InvokeConstructor``1(System.Object[],System.Reflection.BindingFlags)">
            <summary>
            Execute a constructor for the specified type, creating a new instance of it.
            It will try each constructor with the correct number of arguments until it finds a working one, so ... not REALLY efficient.
            May have side-effects when used on types that do significant logic in their constructors even when they fail.
            </summary>
            <param name="Arguments">Arguments to pass to the constructor.</param>
            <param name="Flags">Search flags selecting the constructor. Defaults to both public and private.</param>
            <returns>The created instance of the type</returns>
        </member>
        <member name="P:PSFramework.Utility.UtilityHost.Callstack">
            <summary>
            Returns the current callstack
            </summary>
        </member>
        <member name="P:PSFramework.Utility.UtilityHost.FriendlyCallstack">
            <summary>
            Returns a user-friendly callstack representation that is a snapshot in time.
            </summary>
        </member>
        <member name="P:PSFramework.Utility.UtilityHost._CallstackNew">
            <summary>
            Returns the current callstack on PS4+
            </summary>
        </member>
        <member name="P:PSFramework.Utility.UtilityHost._CallstackOld">
            <summary>
            Returns the current callstack on PS3
            </summary>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.GetCallerInfo(System.Int32)">
            <summary>
            Get information on whoever called your PowerShell code
            </summary>
            <param name="Level">How many levels to go down the script callstack</param>
            <returns>Metadata describing who called</returns>
        </member>
        <member name="F:PSFramework.Utility.UtilityHost.ScriptBlocks">
            <summary>
            Stored scriptblocks that can be retrieved on demand anywhere within the process
            </summary>
        </member>
        <member name="M:PSFramework.Utility.UtilityHost.ImportScriptBlock(System.Management.Automation.ScriptBlock,System.Boolean)">
            <summary>
            Imports a scriptblock into the current sessionstate, without affecting its language mode.
            Note: Be wary where you import to, as the thus imported code can affect local variables that might be trusted.
            </summary>
            <param name="ScriptBlock">The code to localize.</param>
            <param name="Global">Add the code to the global sessionstate, not the current sessionstate.</param>
        </member>
        <member name="T:PSFramework.Validation.NoResultsActionPreference">
            <summary>
            The preference on how to deal with no legal values being returned. Used by PsfValidateSet
            </summary>
        </member>
        <member name="F:PSFramework.Validation.NoResultsActionPreference.Continue">
            <summary>
            Allow to continue
            </summary>
        </member>
        <member name="F:PSFramework.Validation.NoResultsActionPreference.Error">
            <summary>
            Fail in fire and blood
            </summary>
        </member>
        <member name="T:PSFramework.Validation.PsfValidateLanguageMode">
            <summary>
            Validation attribute that tests a scriptblock's language mode.
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateLanguageMode.Modes">
            <summary>
            The legal language modes, defaults to FullLanguage
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateLanguageMode.ValidateElement(System.Object)">
            <summary>
            Validation routine used by the engine
            </summary>
            <param name="element">The object to validate must be a scriptblock.</param>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateLanguageMode.#ctor">
            <summary>
            Creates a default instance, validating for full language.
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateLanguageMode.#ctor(System.Management.Automation.PSLanguageMode[])">
            <summary>
            Creates a custom instance, validating for the specified modes.
            </summary>
            <param name="Modes">The modes to test against.</param>
        </member>
        <member name="T:PSFramework.Validation.PsfValidatePatternAttribute">
            <summary>
            Validates that each parameter argument matches the RegexPattern
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidatePatternAttribute.RegexPattern">
            <summary>
            Gets the Regex pattern to be used in the validation
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidatePatternAttribute.Options">
            <summary>
            Gets or sets the Regex options to be used in the validation
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidatePatternAttribute.ErrorMessage">
             <summary>
             Gets or sets the custom error message pattern that is displayed to the user.
            
             The text representation of the object being validated and the validating regex is passed as
             the first and second formatting parameters to the ErrorMessage formatting pattern.
             <example>
             [PsfValidatePattern("\s+", ErrorMessage="The text '{0}' did not pass validation of regex '{1}'")]
             </example>
             </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidatePatternAttribute.ErrorString">
            <summary>
            The stored localized string to use for error messages
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidatePatternAttribute.ValidateElement(System.Object)">
            <summary>
            Validates that each parameter argument matches the RegexPattern
            </summary>
            <param name="element">object to validate</param>
            <exception cref="T:System.Management.Automation.ValidationMetadataException">if <paramref name="element"/> is not a string
             that matches the pattern
             and for invalid arguments</exception>
        </member>
        <member name="M:PSFramework.Validation.PsfValidatePatternAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the PsfValidatePatternAttribute class
            </summary>
            <param name="regexPattern">Pattern string to match</param>
            <exception cref="T:System.ArgumentException">for invalid arguments</exception>
        </member>
        <member name="T:PSFramework.Validation.PsfValidatePSVersion">
            <summary>
            Validation attribute that ensures a parameter is only used in a given PS Version
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidatePSVersion.Version">
            <summary>
            The minimum version required
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidatePSVersion.FeatureName">
            <summary>
            The name of the feature that can override this test
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidatePSVersion.ValidateElement(System.Object)">
            <summary>
            Validation routine used by the engine
            </summary>
            <param name="element">The object to validate does not matter and is ignored.</param>
        </member>
        <member name="M:PSFramework.Validation.PsfValidatePSVersion.#ctor(System.String)">
            <summary>
            Create a new validation attribute with a preconfigured minimum version
            </summary>
            <param name="MinimumVersion">The minimum version required</param>
        </member>
        <member name="M:PSFramework.Validation.PsfValidatePSVersion.#ctor(System.String,System.String)">
            <summary>
            Create a new validation attribute with a preconfigured minimum version
            </summary>
            <param name="MinimumVersion">The minimum version required</param>
            <param name="FeatureName">An optional featureflag that can override this validation</param>
        </member>
        <member name="T:PSFramework.Validation.PsfValidateScriptAttribute">
            <summary>
            Class for validating against a script block.
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidateScriptAttribute.ErrorMessage">
            <summary>
            Gets or sets the custom error message that is displayed to the user.
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateScriptAttribute.ErrorString">
            <summary>
            The stored localized string to use for error messages
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidateScriptAttribute.ScriptBlock">
            <summary>
            Gets the scriptblock to be used in the validation
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidateScriptAttribute.Global">
            <summary>
            Whether the scriptblock should be invoked as global scriptblock
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidateScriptAttribute.ScriptBlockName">
            <summary>
            Name of a stored scriptblock to use
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateScriptAttribute.ValidateElement(System.Object)">
            <summary>
            Validates that each parameter argument matches the scriptblock
            </summary>
            <param name="element">object to validate</param>
            <exception cref="T:System.Management.Automation.ValidationMetadataException">if <paramref name="element"/> is invalid</exception>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateScriptAttribute.#ctor(System.Management.Automation.ScriptBlock)">
            <summary>
            Initializes a new instance of the ValidateScriptBlockAttribute class
            </summary>
            <param name="ScriptBlock">Scriptblock to match</param>
            <exception cref="T:System.ArgumentException">for invalid arguments</exception>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateScriptAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the ValidateScriptBlockAttribute class
            </summary>
        </member>
        <member name="T:PSFramework.Validation.PsfValidateSetAttribute">
            <summary>
            Class for validating against a set of legal values. Set can be dynamically calculated.
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateSetAttribute.ScriptBlock">
            <summary>
            Gets the scriptblock to be used in the validation
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateSetAttribute.Values">
            <summary>
            Gets a list of string values to be used in the validation
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateSetAttribute.TabCompletion">
            <summary>
            Name of the Tab Completion scriptblock to use for validate set.
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateSetAttribute.NoResults">
            <summary>
            The action to take when no results are being returned.
            </summary>
        </member>
        <member name="P:PSFramework.Validation.PsfValidateSetAttribute.ErrorMessage">
            <summary>
            Custom error message to display
            </summary>
        </member>
        <member name="F:PSFramework.Validation.PsfValidateSetAttribute.ErrorString">
            <summary>
            The stored localized string to use for error messages
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateSetAttribute.ValidateElement(System.Object)">
            <summary>
            Validates that each parameter argument matches the set of legal values
            </summary>
            <param name="element">object to validate</param>
            <exception cref="T:System.Management.Automation.ValidationMetadataException">if <paramref name="element"/> is invalid</exception>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateSetAttribute.#ctor">
            <summary>
            Empty constructor for other attributes
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateSetAttribute.GetValues">
            <summary>
            Returns the values provided by the options specified.
            </summary>
            <returns>The legal values you may provide.</returns>
        </member>
        <member name="T:PSFramework.Validation.PsfValidateTrustedDataAttribute">
            <summary>
            Validation attribute that works equivalent to the ValidateTrustedDataAttribute, but can be used on PS3+ (for no effect on those).
            </summary>
        </member>
        <member name="M:PSFramework.Validation.PsfValidateTrustedDataAttribute.Validate(System.Object,System.Management.Automation.EngineIntrinsics)">
            <summary>
            Validates that the parameter argument is not untrusted
            </summary>
            <param name="arguments">Object to validate</param>
            <param name="engineIntrinsics">
            The engine APIs for the context under which the validation is being
            evaluated.
            </param>
            <exception cref="T:System.Management.Automation.ValidationMetadataException">
            if the argument is untrusted.
            </exception>
        </member>
    </members>
</doc>
