
[ClassVersion("*******"), FriendlyName("xWindowsOptionalFeature")]
class DSC_xWindowsOptionalFeature : OMI_BaseResource
{
    [Key, Description("The name of the Windows optional feature to enable or disable.")] String Name;
    [Write, Description("Specifies whether the feature should be enabled or disabled. To enable the feature, set this property to Present. To disable the feature, set the property to Absent. The default value is Present."), ValueMap{"Present", "Absent"}, Values{"Present", "Absent"}] String Ensure;
    [Write, Description("Specifies that all files associated with the feature should be removed if the feature is being disabled.")] Boolean RemoveFilesOnDisable;
    [Write, Description("Specifies whether or not DISM contacts Windows Update (WU) when searching for the source files to enable the feature. If $true, DISM will not contact WU.")] Boolean NoWindowsUpdateCheck;
    [Write, Description("The maximum output level to show in the log. Accepted values are: ErrorsOnly (only errors are logged), ErrorsAndWarning (errors and warnings are logged), and ErrorsAndWarningAndInformation (errors, warnings, and debug information are logged)."), ValueMap{"ErrorsOnly", "ErrorsAndWarning", "ErrorsAndWarningAndInformation"}, Values{"ErrorsOnly", "ErrorsAndWarning", "ErrorsAndWarningAndInformation"}] String LogLevel;
    [Write, Description("The path to the log file to log this operation. There is no default value, but if not set, the log will appear at %WINDIR%\\Logs\\Dism\\dism.log.")] String LogPath;
    [Read, Description("The custom properties retrieved from the Windows optional feature as an array of strings.")] String CustomProperties[];
    [Read, Description("The description retrieved from the Windows optional feature.")] String Description;
    [Read, Description("The display name retrieved from the Windows optional feature.")] String DisplayName;
};
