﻿<?xml version="1.0" encoding="utf-8"?>
<helpItems schema="maml" xmlns="http://msh">
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>ConvertTo-PSFHashtable</command:name>
      <command:verb>ConvertTo</command:verb>
      <command:noun>PSFHashtable</command:noun>
      <maml:description>
        <maml:para>Converts an object into a hashtable.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Converts an object into a hashtable.</maml:para>
      <maml:para>- Use -Exclude to selectively blacklist properties / keys</maml:para>
      <maml:para>- Use -Include to selectively whitelist properties / keys</maml:para>
      <maml:para>- Use -Inherit to inherit values from variables when missing keys explicitly included in -Include</maml:para>
      <maml:para></maml:para>
      <maml:para>Optimized to selectively convert $PSBoundParameters for passing through parameters to internal command calls.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>ConvertTo-PSFHashtable</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Exclude</maml:name>
          <maml:Description>
            <maml:para>The propertynames to exclude. Must be full property-names, no wildcard/regex matching.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Include</maml:name>
          <maml:Description>
            <maml:para>The propertynames to include. Must be full property-names, no wildcard/regex matching.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>IncludeEmpty</maml:name>
          <maml:Description>
            <maml:para>By default, only properties on the input object are included. In order to force all properties defined in -Include to be included, specify this switch. Keys added through this have an empty ($null) value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Inherit</maml:name>
          <maml:Description>
            <maml:para>By default, only properties on the input object are included. With this parameter, missing keys are substituted for by looking in the caller scope for variables with the same name. This is explicitly designed to allow inheriting default parameter values when cloning $PSBoundParameters.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
          <maml:name>InputObject</maml:name>
          <maml:Description>
            <maml:para>The object(s) to convert</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSObject[]</command:parameterValue>
          <dev:type>
            <maml:name>PSObject[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>CaseSensitive</maml:name>
          <maml:Description>
            <maml:para>Make Include and Exclude name-filtering case sensitive.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Exclude</maml:name>
        <maml:Description>
          <maml:para>The propertynames to exclude. Must be full property-names, no wildcard/regex matching.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Include</maml:name>
        <maml:Description>
          <maml:para>The propertynames to include. Must be full property-names, no wildcard/regex matching.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>IncludeEmpty</maml:name>
        <maml:Description>
          <maml:para>By default, only properties on the input object are included. In order to force all properties defined in -Include to be included, specify this switch. Keys added through this have an empty ($null) value.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Inherit</maml:name>
        <maml:Description>
          <maml:para>By default, only properties on the input object are included. With this parameter, missing keys are substituted for by looking in the caller scope for variables with the same name. This is explicitly designed to allow inheriting default parameter values when cloning $PSBoundParameters.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
        <maml:name>InputObject</maml:name>
        <maml:Description>
          <maml:para>The object(s) to convert</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSObject[]</command:parameterValue>
        <dev:type>
          <maml:name>PSObject[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>CaseSensitive</maml:name>
        <maml:Description>
          <maml:para>Make Include and Exclude name-filtering case sensitive.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Collections.Hashtable</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------------- Example 1 --------------------------</maml:title>
        <dev:code>Get-ChildItem | ConvertTo-PSFHashtable</dev:code>
        <dev:remarks>
          <maml:para>Scans all items in the current path and converts those objects into hashtables.</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------------------- Example 2 --------------------------</maml:title>
        <dev:code>$parameters = $PSBoundParameters | ConvertTo-PSFHashtable -Include ComputerName, Credential, Target -Inherit</dev:code>
        <dev:remarks>
          <maml:para>Clones the bound parameters into a new hashtable that can now be used for splatting- Only parameters explicitly specified or with default values will be included.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Invoke-PSFCallback</command:name>
      <command:verb>Invoke</command:verb>
      <command:noun>PSFCallback</command:noun>
      <maml:description>
        <maml:para>Invokes all registered callback scripts applicable to the calling command.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Invokes all registered callback scripts applicable to the calling command.</maml:para>
      <maml:para>Use Register-PSFCallback to register scriptblocks that get applied.</maml:para>
      <maml:para>By calling Invoke-PSFCallback - which will not do anything unless somebody registers callback scriptblocks - a module can provide extensions points to which other modules can attach, without the implementing module needing to know those external modules.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Invoke-PSFCallback</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Data</maml:name>
          <maml:Description>
            <maml:para>Additional data to provide to the callback scriptblock.</maml:para>
            <maml:para>This can be useful to implement input-driven workflows. For example, it would allow a callback scriptblock to load configuration, based on the server being processed.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>Enables - if $true - terminating exceptions when a single callback scriptblock fails. The terminating exception is thrown in the context of the calling command, not Invoke-PSFCallback, so it is unneccessary - and impossible - to handle within a try/catch block.</maml:para>
            <maml:para>If set to $false (default), failure flags the calling command for failure, as detected by Test-PSFFunctionInterrupt. In that case, Invoke-PSFCallback will log the error, but not directly terminate the calling command.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>The $PSCmdlet object of the calling command. If this value is not provided, it will autoamtically be picked up. Providing it improves performance slightly, as it removes the need to look it up.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Data</maml:name>
        <maml:Description>
          <maml:para>Additional data to provide to the callback scriptblock.</maml:para>
          <maml:para>This can be useful to implement input-driven workflows. For example, it would allow a callback scriptblock to load configuration, based on the server being processed.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnableException</maml:name>
        <maml:Description>
          <maml:para>Enables - if $true - terminating exceptions when a single callback scriptblock fails. The terminating exception is thrown in the context of the calling command, not Invoke-PSFCallback, so it is unneccessary - and impossible - to handle within a try/catch block.</maml:para>
          <maml:para>If set to $false (default), failure flags the calling command for failure, as detected by Test-PSFFunctionInterrupt. In that case, Invoke-PSFCallback will log the error, but not directly terminate the calling command.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PSCmdlet</maml:name>
        <maml:Description>
          <maml:para>The $PSCmdlet object of the calling command. If this value is not provided, it will autoamtically be picked up. Providing it improves performance slightly, as it removes the need to look it up.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
        <dev:type>
          <maml:name>PSCmdlet</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>None</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>----------------- Example 1 : Basic Invocation -----------------</maml:title>
        <dev:code>PS C:\&gt; Invoke-PSFCallback</dev:code>
        <dev:remarks>
          <maml:para>Simply by calling the command, any registered scriptblocks that apply to your command get triggered.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>-------------- Example 2 : Providing Information --------------</maml:title>
        <dev:code>PS C:\&gt; Invoke-PSFCallback -Data $Server</dev:code>
        <dev:remarks>
          <maml:para>Executes all applicable, registered callback scriptblocks.</maml:para>
          <maml:para>Provides the information stored in $Server to the scriptblock(s) being this executed.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>--------------- Example 3 : The full invocation ---------------</maml:title>
        <dev:code>PS C:\&gt; Invoke-PSFCallback -Data $Server -EnableException $true -PSCmdlet $PSCmdlet</dev:code>
        <dev:remarks>
          <maml:para>Executes all applicable, registered callback scriptblocks.</maml:para>
          <maml:para>Provides the information stored in $Server to the scriptblock(s) being this executed.</maml:para>
          <maml:para>If any of the executed callback scriptblocks fails with a terminating exception, the command calling Invoke-PSFCallback also fails in a terminating exception (no try/catch necessary).</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Invoke-PSFProtectedCommand</command:name>
      <command:verb>Invoke</command:verb>
      <command:noun>PSFProtectedCommand</command:noun>
      <maml:description>
        <maml:para>Combines should process, try/catch error handling and logging in one package.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This command is designed to do away with the script code overhead of:</maml:para>
      <maml:para>- Prompting for ShouldProcess if needed</maml:para>
      <maml:para>- Including try/catch error handling for the purpose of the PSFramework opt-in exceptions concept</maml:para>
      <maml:para>- Logging execution</maml:para>
      <maml:para>As such it is intended to help produce more readable code in less time.</maml:para>
      <maml:para>Note: This command can only be used from an advanced function unless specifying the -PSCmdlet parameter.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Invoke-PSFProtectedCommand</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Action</maml:name>
          <maml:Description>
            <maml:para>The action that will be performed on the target.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:Description>
            <maml:para>If this switch is enabled, you will be prompted for confirmation before executing any operations that change state.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Continue</maml:name>
          <maml:Description>
            <maml:para>When execution fails, unless exceptions where opted into, this command will call continue, moving on with the next item to process.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>This parameters disables user-friendly warnings and enables the throwing of exceptions. This is less user friendly, but allows catching exceptions in calling scripts.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>This command will automatically pick up the $PSCmdlet variable of its caller. Use this parameter to override this and specify what $PSCmdlet variable to use. The $PSCmdlet variable will be used for terminating exceptions.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ScriptBlock</maml:name>
          <maml:Description>
            <maml:para>The actual scriptblock to execute. Make sure any errors you want to trigger failure are terminating.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Target</maml:name>
          <maml:Description>
            <maml:para>The target object to work against.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:Description>
            <maml:para>if this switch is enabled, no actions are performed but informational messages will be displayed that explain what would happen if the command were to run.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContinueLabel</maml:name>
          <maml:Description>
            <maml:para>When used together with -Continue, this allows you to pick the loop to continue with.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryCount</maml:name>
          <maml:Description>
            <maml:para>How many times the command should attempt to try an action again, before giving up.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryErrorType</maml:name>
          <maml:Description>
            <maml:para>When using -RetryCount to attempt a failed action again, this is a bit unspecific. Often we want to retry to deal with specific scenarios ... with specific errors. Using this parameter, it becomes possible to specify just what exception types can only trigger a retry attempt.</maml:para>
            <maml:para>Note: Exceptions of type ActionPreferenceStopException will be unwrapped to the original exception BEFORE performing the comparison.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryWait</maml:name>
          <maml:Description>
            <maml:para>The interval the command will wait between failed attempts. Defaults to 5 seconds.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">TimeSpanParameter</command:parameterValue>
          <dev:type>
            <maml:name>TimeSpanParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Tag</maml:name>
          <maml:Description>
            <maml:para>What tags - if any - to include in all messages.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryCondition</maml:name>
          <maml:Description>
            <maml:para>Only when this scriptblock returns $true will it try again. The script receives two input items:</maml:para>
            <maml:para>- $_ : The exception (not error record) thrown</maml:para>
            <maml:para>- $args[0] : The Target object specified in the -Target parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Invoke-PSFProtectedCommand</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ActionString</maml:name>
          <maml:Description>
            <maml:para>Localized string of the action that will be performed on the target. Omit the modulename in the string. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ActionStringValues</maml:name>
          <maml:Description>
            <maml:para>Specify values to format into the localization string specified. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
          <dev:type>
            <maml:name>Object[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:Description>
            <maml:para>If this switch is enabled, you will be prompted for confirmation before executing any operations that change state.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Continue</maml:name>
          <maml:Description>
            <maml:para>When execution fails, unless exceptions where opted into, this command will call continue, moving on with the next item to process.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>This parameters disables user-friendly warnings and enables the throwing of exceptions. This is less user friendly, but allows catching exceptions in calling scripts.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>This command will automatically pick up the $PSCmdlet variable of its caller. Use this parameter to override this and specify what $PSCmdlet variable to use. The $PSCmdlet variable will be used for terminating exceptions.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ScriptBlock</maml:name>
          <maml:Description>
            <maml:para>The actual scriptblock to execute. Make sure any errors you want to trigger failure are terminating.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Target</maml:name>
          <maml:Description>
            <maml:para>The target object to work against.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:Description>
            <maml:para>if this switch is enabled, no actions are performed but informational messages will be displayed that explain what would happen if the command were to run.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ContinueLabel</maml:name>
          <maml:Description>
            <maml:para>When used together with -Continue, this allows you to pick the loop to continue with.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryCount</maml:name>
          <maml:Description>
            <maml:para>How many times the command should attempt to try an action again, before giving up.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryErrorType</maml:name>
          <maml:Description>
            <maml:para>When using -RetryCount to attempt a failed action again, this is a bit unspecific. Often we want to retry to deal with specific scenarios ... with specific errors. Using this parameter, it becomes possible to specify just what exception types can only trigger a retry attempt.</maml:para>
            <maml:para>Note: Exceptions of type ActionPreferenceStopException will be unwrapped to the original exception BEFORE performing the comparison.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryWait</maml:name>
          <maml:Description>
            <maml:para>The interval the command will wait between failed attempts. Defaults to 5 seconds.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">TimeSpanParameter</command:parameterValue>
          <dev:type>
            <maml:name>TimeSpanParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Tag</maml:name>
          <maml:Description>
            <maml:para>What tags - if any - to include in all messages.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>RetryCondition</maml:name>
          <maml:Description>
            <maml:para>Only when this scriptblock returns $true will it try again. The script receives two input items:</maml:para>
            <maml:para>- $_ : The exception (not error record) thrown</maml:para>
            <maml:para>- $args[0] : The Target object specified in the -Target parameter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Action</maml:name>
        <maml:Description>
          <maml:para>The action that will be performed on the target.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ActionString</maml:name>
        <maml:Description>
          <maml:para>Localized string of the action that will be performed on the target. Omit the modulename in the string. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ActionStringValues</maml:name>
        <maml:Description>
          <maml:para>Specify values to format into the localization string specified. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
        <dev:type>
          <maml:name>Object[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:Description>
          <maml:para>If this switch is enabled, you will be prompted for confirmation before executing any operations that change state.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Continue</maml:name>
        <maml:Description>
          <maml:para>When execution fails, unless exceptions where opted into, this command will call continue, moving on with the next item to process.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnableException</maml:name>
        <maml:Description>
          <maml:para>This parameters disables user-friendly warnings and enables the throwing of exceptions. This is less user friendly, but allows catching exceptions in calling scripts.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PSCmdlet</maml:name>
        <maml:Description>
          <maml:para>This command will automatically pick up the $PSCmdlet variable of its caller. Use this parameter to override this and specify what $PSCmdlet variable to use. The $PSCmdlet variable will be used for terminating exceptions.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
        <dev:type>
          <maml:name>PSCmdlet</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ScriptBlock</maml:name>
        <maml:Description>
          <maml:para>The actual scriptblock to execute. Make sure any errors you want to trigger failure are terminating.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
        <dev:type>
          <maml:name>ScriptBlock</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Target</maml:name>
        <maml:Description>
          <maml:para>The target object to work against.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:Description>
          <maml:para>if this switch is enabled, no actions are performed but informational messages will be displayed that explain what would happen if the command were to run.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ContinueLabel</maml:name>
        <maml:Description>
          <maml:para>When used together with -Continue, this allows you to pick the loop to continue with.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>RetryCount</maml:name>
        <maml:Description>
          <maml:para>How many times the command should attempt to try an action again, before giving up.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>RetryErrorType</maml:name>
        <maml:Description>
          <maml:para>When using -RetryCount to attempt a failed action again, this is a bit unspecific. Often we want to retry to deal with specific scenarios ... with specific errors. Using this parameter, it becomes possible to specify just what exception types can only trigger a retry attempt.</maml:para>
          <maml:para>Note: Exceptions of type ActionPreferenceStopException will be unwrapped to the original exception BEFORE performing the comparison.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>RetryWait</maml:name>
        <maml:Description>
          <maml:para>The interval the command will wait between failed attempts. Defaults to 5 seconds.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">TimeSpanParameter</command:parameterValue>
        <dev:type>
          <maml:name>TimeSpanParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Tag</maml:name>
        <maml:Description>
          <maml:para>What tags - if any - to include in all messages.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>RetryCondition</maml:name>
        <maml:Description>
          <maml:para>Only when this scriptblock returns $true will it try again. The script receives two input items:</maml:para>
          <maml:para>- $_ : The exception (not error record) thrown</maml:para>
          <maml:para>- $args[0] : The Target object specified in the -Target parameter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
        <dev:type>
          <maml:name>ScriptBlock</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes>
      <command:inputType>
        <dev:type>
          <maml:name>Keine</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:inputType>
    </command:inputTypes>
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>System.Object</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------- Example 1 : Killing with maximum prejudice ----------</maml:title>
        <dev:code>PS C:\&gt; Invoke-PSFProtectedCommand -Action "Doing Something" -Target $computer -ScriptBlock {
    Get-Something -ComputerName $computer -ErrorAction Stop
} -EnableException $true</dev:code>
        <dev:remarks>
          <maml:para>Invokes the specified scriptblock, honoring ShouldProcess, logging execution and potential failure. Failure will lead to a terminating exception.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------------- Example 2 : Killing it softly ----------------</maml:title>
        <dev:code>PS C:\&gt; Invoke-PSFProtectedCommand -Action "Doing Something" -Target $computer -ScriptBlock {
    Get-Something -ComputerName $computer -ErrorAction Stop
}
PS C:\&gt; if (Test-PSFFunctionInterrupt) { return }</dev:code>
        <dev:remarks>
          <maml:para>Invokes the specified scriptblock, honoring ShouldProcess, logging execution and potential failure. Failure will lead to a warning with the command terminating silently, unless the calling command's module opted into inheriting the '-EnableException' parameter (in which case the caller of the command calling Invoke-PSFProtectedCommand gets to pick whether this is throwing an exception or not)</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks />
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Remove-PSFNull</command:name>
      <command:verb>Remove</command:verb>
      <command:noun>PSFNull</command:noun>
      <maml:description>
        <maml:para>Filters out null objects.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This cmdlet can be used to filter out $null objects, empty collections and empty strings from the pipeline.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Remove-PSFNull</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
          <maml:name>InputObject</maml:name>
          <maml:Description>
            <maml:para>The items to filter</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSObject</command:parameterValue>
          <dev:type>
            <maml:name>PSObject</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AllowEmptyCollections</maml:name>
          <maml:Description>
            <maml:para>By default, empty collections are dropped from the output. Using this switch, they are passe along.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AllowEmptyStrings</maml:name>
          <maml:Description>
            <maml:para>By default, empty strings are discarded. Setting this switch causes them to be passed through instead.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Enumerate</maml:name>
          <maml:Description>
            <maml:para>By default, output is not enumerated (Lists are sent along the pipeline as Lists, not individual items). If Remove-PSFNull should enumerate output after all, set this switch.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
        <maml:name>InputObject</maml:name>
        <maml:Description>
          <maml:para>The items to filter</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSObject</command:parameterValue>
        <dev:type>
          <maml:name>PSObject</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AllowEmptyCollections</maml:name>
        <maml:Description>
          <maml:para>By default, empty collections are dropped from the output. Using this switch, they are passe along.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AllowEmptyStrings</maml:name>
        <maml:Description>
          <maml:para>By default, empty strings are discarded. Setting this switch causes them to be passed through instead.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Enumerate</maml:name>
        <maml:Description>
          <maml:para>By default, output is not enumerated (Lists are sent along the pipeline as Lists, not individual items). If Remove-PSFNull should enumerate output after all, set this switch.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>--------------- Example 1: Sweeping the Pipeline ---------------</maml:title>
        <dev:code>C:\PS&gt; Get-Something | Remove-PSFNull | Do-Something</dev:code>
        <dev:remarks>
          <maml:para>In this example, Remove-PSFNull cleans up the pipeline from null-equivalent objects before passing output along to Do-Something</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Documentation:</maml:linkText>
        <maml:uri>https://psframework.org/documentation/commands/PSFramework/Remove-PSFNull.html</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Select-PSFObject</command:name>
      <command:verb>Select</command:verb>
      <command:noun>PSFObject</command:noun>
      <maml:description>
        <maml:para>Wrapper around Select-Object, extends property parameter.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>Wrapper around Select-Object, extends property parameter.</maml:para>
      <maml:para>This function allows specifying in-line transformation of the properties specified without needing to use complex hashtables. For example, renaming a property becomes as simple as "Length as Size"</maml:para>
      <maml:para>Also supported:</maml:para>
      <maml:para>- Specifying a typename</maml:para>
      <maml:para>- Picking the default display properties</maml:para>
      <maml:para>- Adding to an existing object without destroying its type</maml:para>
      <maml:para>See the description of the Property parameter for an exhaustive list of legal notations for in-line transformations.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Select-PSFObject</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
          <maml:name>InputObject</maml:name>
          <maml:Description>
            <maml:para>The object(s) to select from.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSObject</command:parameterValue>
          <dev:type>
            <maml:name>PSObject</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Property</maml:name>
          <maml:Description>
            <maml:para>The properties to select. - Supports hashtables, which will be passed through to Select-Object.</maml:para>
            <maml:para>- Supports renaming as it is possible in SQL: "Length AS Size" will select the Length property but rename it to size.</maml:para>
            <maml:para>- Supports casting to a specified type: "Address to IPAddress" or "Length to int".</maml:para>
            <maml:para>Uses PowerShell type-conversion. - Supports parsing numbers to sizes: "Length size GB:2" Converts numeric input (presumed to be bytes) to gigabyte with two decimals.   Also supports toggling on Unit descriptors by adding another element: "Length size GB:2:1" - Supports selecting properties from objects in other variables: "ComputerName from VarName" (Will insert the property 'ComputerName' from variable $VarName)</maml:para>
            <maml:para>- Supports filtering when selecting from outside objects: "ComputerName from VarName where ObjectId = Id" (Will insert the property 'ComputerName' from the object in variable $VarName, whose ObjectId property is equal to the inputs Id property)</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExcludeProperty</maml:name>
          <maml:Description>
            <maml:para>Properties to not list.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExpandProperty</maml:name>
          <maml:Description>
            <maml:para>Properties to expand.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Unique</maml:name>
          <maml:Description>
            <maml:para>Do not list multiples of the same value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Last</maml:name>
          <maml:Description>
            <maml:para>Select the last n items.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>First</maml:name>
          <maml:Description>
            <maml:para>Select the first n items.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Skip</maml:name>
          <maml:Description>
            <maml:para>Skip the first (or last if used with -Last) n items.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Wait</maml:name>
          <maml:Description>
            <maml:para>Indicates that the cmdlet turns off optimization. Windows PowerShell runs commands in the order that they appear in the command pipeline and lets them generate all objects. By default, if you include a Select-PSFObject command with the First or Index parameters in a command pipeline, Windows PowerShell stops the command that generates the objects as soon as the selected number of objects is generated.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ShowProperty</maml:name>
          <maml:Description>
            <maml:para>Only the specified properties will be shown by default. Supersedes ShowExcludeProperty</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ShowExcludeProperty</maml:name>
          <maml:Description>
            <maml:para>Hides the specified properties from the default display style of the output object. Is ignored if used together with ShowProperty.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>TypeName</maml:name>
          <maml:Description>
            <maml:para>Adds a typename to the selected object. Will automatically prefix the module.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>KeepInputObject</maml:name>
          <maml:Description>
            <maml:para>Keeps the original input object, adding new properties the select added without removing any. This allows you to use this command to add properties without losing the input object's type.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Alias</maml:name>
          <maml:Description>
            <maml:para>Create an alias property. This can be in simple SQL notation, such as "Length as Size" or a hashtable with the alias name being the key and the referenced property being the value.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectAliasParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectAliasParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ScriptMethod</maml:name>
          <maml:Description>
            <maml:para>Add script methods to the object. This can be done using either:</maml:para>
            <maml:para>- String syntax 'GetDouble =&gt; $this.Length * 2'</maml:para>
            <maml:para>- Hashtable defining any number of methods, with the name being the key and the scriptblock being the value.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectScriptMethodParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectScriptMethodParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ScriptProperty</maml:name>
          <maml:Description>
            <maml:para>Add script properties to the object. This parameter takes a hashtable that can be either simply Name --&gt; Scriptblock binding (for readonly properties) or Name --&gt; Hashtable binding, with the inner hashtable containing two keys: get &amp; set. Each pointing at their respective scriptblock.</maml:para>
            <maml:para>-ScriptProperty @{</maml:para>
            <maml:para>    DoubleSize = { $this.Length * 2}</maml:para>
            <maml:para>    MegaSize = @{</maml:para>
            <maml:para>        get = { $this.Length * 10 }</maml:para>
            <maml:para>        set = { $this.Length = $_ / 10 }</maml:para>
            <maml:para>    }</maml:para>
            <maml:para>}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectScriptPropertyParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectScriptPropertyParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Select-PSFObject</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
          <maml:name>InputObject</maml:name>
          <maml:Description>
            <maml:para>The object(s) to select from.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSObject</command:parameterValue>
          <dev:type>
            <maml:name>PSObject</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Property</maml:name>
          <maml:Description>
            <maml:para>The properties to select. - Supports hashtables, which will be passed through to Select-Object.</maml:para>
            <maml:para>- Supports renaming as it is possible in SQL: "Length AS Size" will select the Length property but rename it to size.</maml:para>
            <maml:para>- Supports casting to a specified type: "Address to IPAddress" or "Length to int".</maml:para>
            <maml:para>Uses PowerShell type-conversion. - Supports parsing numbers to sizes: "Length size GB:2" Converts numeric input (presumed to be bytes) to gigabyte with two decimals.   Also supports toggling on Unit descriptors by adding another element: "Length size GB:2:1" - Supports selecting properties from objects in other variables: "ComputerName from VarName" (Will insert the property 'ComputerName' from variable $VarName)</maml:para>
            <maml:para>- Supports filtering when selecting from outside objects: "ComputerName from VarName where ObjectId = Id" (Will insert the property 'ComputerName' from the object in variable $VarName, whose ObjectId property is equal to the inputs Id property)</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExcludeProperty</maml:name>
          <maml:Description>
            <maml:para>Properties to not list.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ExpandProperty</maml:name>
          <maml:Description>
            <maml:para>Properties to expand.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Unique</maml:name>
          <maml:Description>
            <maml:para>Do not list multiples of the same value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SkipLast</maml:name>
          <maml:Description>
            <maml:para>Skip the last n items.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ShowProperty</maml:name>
          <maml:Description>
            <maml:para>Only the specified properties will be shown by default. Supersedes ShowExcludeProperty</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ShowExcludeProperty</maml:name>
          <maml:Description>
            <maml:para>Hides the specified properties from the default display style of the output object. Is ignored if used together with ShowProperty.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>TypeName</maml:name>
          <maml:Description>
            <maml:para>Adds a typename to the selected object. Will automatically prefix the module.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>KeepInputObject</maml:name>
          <maml:Description>
            <maml:para>Keeps the original input object, adding new properties the select added without removing any. This allows you to use this command to add properties without losing the input object's type.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Alias</maml:name>
          <maml:Description>
            <maml:para>Create an alias property. This can be in simple SQL notation, such as "Length as Size" or a hashtable with the alias name being the key and the referenced property being the value.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectAliasParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectAliasParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ScriptMethod</maml:name>
          <maml:Description>
            <maml:para>Add script methods to the object. This can be done using either:</maml:para>
            <maml:para>- String syntax 'GetDouble =&gt; $this.Length * 2'</maml:para>
            <maml:para>- Hashtable defining any number of methods, with the name being the key and the scriptblock being the value.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectScriptMethodParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectScriptMethodParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ScriptProperty</maml:name>
          <maml:Description>
            <maml:para>Add script properties to the object. This parameter takes a hashtable that can be either simply Name --&gt; Scriptblock binding (for readonly properties) or Name --&gt; Hashtable binding, with the inner hashtable containing two keys: get &amp; set. Each pointing at their respective scriptblock.</maml:para>
            <maml:para>-ScriptProperty @{</maml:para>
            <maml:para>    DoubleSize = { $this.Length * 2}</maml:para>
            <maml:para>    MegaSize = @{</maml:para>
            <maml:para>        get = { $this.Length * 10 }</maml:para>
            <maml:para>        set = { $this.Length = $_ / 10 }</maml:para>
            <maml:para>    }</maml:para>
            <maml:para>}</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">SelectScriptPropertyParameter[]</command:parameterValue>
          <dev:type>
            <maml:name>SelectScriptPropertyParameter[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Select-PSFObject</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
          <maml:name>InputObject</maml:name>
          <maml:Description>
            <maml:para>The object(s) to select from.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSObject</command:parameterValue>
          <dev:type>
            <maml:name>PSObject</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Unique</maml:name>
          <maml:Description>
            <maml:para>Do not list multiples of the same value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Wait</maml:name>
          <maml:Description>
            <maml:para>Indicates that the cmdlet turns off optimization. Windows PowerShell runs commands in the order that they appear in the command pipeline and lets them generate all objects. By default, if you include a Select-PSFObject command with the First or Index parameters in a command pipeline, Windows PowerShell stops the command that generates the objects as soon as the selected number of objects is generated.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Index</maml:name>
          <maml:Description>
            <maml:para>Specifies an array of objects based on their index values. Enter the indexes in a comma-separated list.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32[]</command:parameterValue>
          <dev:type>
            <maml:name>Int32[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ShowProperty</maml:name>
          <maml:Description>
            <maml:para>Only the specified properties will be shown by default. Supersedes ShowExcludeProperty</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ShowExcludeProperty</maml:name>
          <maml:Description>
            <maml:para>Hides the specified properties from the default display style of the output object. Is ignored if used together with ShowProperty.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>TypeName</maml:name>
          <maml:Description>
            <maml:para>Adds a typename to the selected object. Will automatically prefix the module.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>KeepInputObject</maml:name>
          <maml:Description>
            <maml:para>Keeps the original input object, adding new properties the select added without removing any. This allows you to use this command to add properties without losing the input object's type.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="True (ByValue)" position="named" aliases="none">
        <maml:name>InputObject</maml:name>
        <maml:Description>
          <maml:para>The object(s) to select from.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSObject</command:parameterValue>
        <dev:type>
          <maml:name>PSObject</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Property</maml:name>
        <maml:Description>
          <maml:para>The properties to select. - Supports hashtables, which will be passed through to Select-Object.</maml:para>
          <maml:para>- Supports renaming as it is possible in SQL: "Length AS Size" will select the Length property but rename it to size.</maml:para>
          <maml:para>- Supports casting to a specified type: "Address to IPAddress" or "Length to int".</maml:para>
          <maml:para>Uses PowerShell type-conversion. - Supports parsing numbers to sizes: "Length size GB:2" Converts numeric input (presumed to be bytes) to gigabyte with two decimals.   Also supports toggling on Unit descriptors by adding another element: "Length size GB:2:1" - Supports selecting properties from objects in other variables: "ComputerName from VarName" (Will insert the property 'ComputerName' from variable $VarName)</maml:para>
          <maml:para>- Supports filtering when selecting from outside objects: "ComputerName from VarName where ObjectId = Id" (Will insert the property 'ComputerName' from the object in variable $VarName, whose ObjectId property is equal to the inputs Id property)</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">SelectParameter[]</command:parameterValue>
        <dev:type>
          <maml:name>SelectParameter[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ExcludeProperty</maml:name>
        <maml:Description>
          <maml:para>Properties to not list.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ExpandProperty</maml:name>
        <maml:Description>
          <maml:para>Properties to expand.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Unique</maml:name>
        <maml:Description>
          <maml:para>Do not list multiples of the same value.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Last</maml:name>
        <maml:Description>
          <maml:para>Select the last n items.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>First</maml:name>
        <maml:Description>
          <maml:para>Select the first n items.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Skip</maml:name>
        <maml:Description>
          <maml:para>Skip the first (or last if used with -Last) n items.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SkipLast</maml:name>
        <maml:Description>
          <maml:para>Skip the last n items.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Wait</maml:name>
        <maml:Description>
          <maml:para>Indicates that the cmdlet turns off optimization. Windows PowerShell runs commands in the order that they appear in the command pipeline and lets them generate all objects. By default, if you include a Select-PSFObject command with the First or Index parameters in a command pipeline, Windows PowerShell stops the command that generates the objects as soon as the selected number of objects is generated.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Index</maml:name>
        <maml:Description>
          <maml:para>Specifies an array of objects based on their index values. Enter the indexes in a comma-separated list.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32[]</command:parameterValue>
        <dev:type>
          <maml:name>Int32[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ShowProperty</maml:name>
        <maml:Description>
          <maml:para>Only the specified properties will be shown by default. Supersedes ShowExcludeProperty</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ShowExcludeProperty</maml:name>
        <maml:Description>
          <maml:para>Hides the specified properties from the default display style of the output object. Is ignored if used together with ShowProperty.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>TypeName</maml:name>
        <maml:Description>
          <maml:para>Adds a typename to the selected object. Will automatically prefix the module.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>KeepInputObject</maml:name>
        <maml:Description>
          <maml:para>Keeps the original input object, adding new properties the select added without removing any. This allows you to use this command to add properties without losing the input object's type.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Alias</maml:name>
        <maml:Description>
          <maml:para>Create an alias property. This can be in simple SQL notation, such as "Length as Size" or a hashtable with the alias name being the key and the referenced property being the value.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">SelectAliasParameter[]</command:parameterValue>
        <dev:type>
          <maml:name>SelectAliasParameter[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ScriptMethod</maml:name>
        <maml:Description>
          <maml:para>Add script methods to the object. This can be done using either:</maml:para>
          <maml:para>- String syntax 'GetDouble =&gt; $this.Length * 2'</maml:para>
          <maml:para>- Hashtable defining any number of methods, with the name being the key and the scriptblock being the value.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">SelectScriptMethodParameter[]</command:parameterValue>
        <dev:type>
          <maml:name>SelectScriptMethodParameter[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ScriptProperty</maml:name>
        <maml:Description>
          <maml:para>Add script properties to the object. This parameter takes a hashtable that can be either simply Name --&gt; Scriptblock binding (for readonly properties) or Name --&gt; Hashtable binding, with the inner hashtable containing two keys: get &amp; set. Each pointing at their respective scriptblock.</maml:para>
          <maml:para>-ScriptProperty @{</maml:para>
          <maml:para>    DoubleSize = { $this.Length * 2}</maml:para>
          <maml:para>    MegaSize = @{</maml:para>
          <maml:para>        get = { $this.Length * 10 }</maml:para>
          <maml:para>        set = { $this.Length = $_ / 10 }</maml:para>
          <maml:para>    }</maml:para>
          <maml:para>}</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">SelectScriptPropertyParameter[]</command:parameterValue>
        <dev:type>
          <maml:name>SelectScriptPropertyParameter[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------------- Example 1: Renaming a property ----------------</maml:title>
        <dev:code>Get-ChildItem | Select-PSFObject Name, "Length as Size"</dev:code>
        <dev:remarks>
          <maml:para>Selects the properties Name and Length, renaming Length to Size in the process.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------------------ Example 2: Converting type ------------------</maml:title>
        <dev:code>Import-Csv .\file.csv | Select-PSFObject Name, "Length as Size to PSFSize"</dev:code>
        <dev:remarks>
          <maml:para>Selects the properties Name and Length, renaming Length to Size and converting it to [Example] (a userfriendly representation of size numbers without turning the data into string)</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------- Example 3: Selecting from another object 1 ----------</maml:title>
        <dev:code>$obj = [PSCustomObject]@{ Name = "Foo" }
Get-ChildItem | Select-PSFObject FullName, Length, "Name from obj"</dev:code>
        <dev:remarks>
          <maml:para>Selects the properties FullName and Length from the input and the Name property from the object stored in $obj</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------- Example 4: Selecting from another object 2 ----------</maml:title>
        <dev:code>$list = @()
$list += [PSCustomObject]@{ Type = "Foo"; ID = 1 }
$list += [PSCustomObject]@{ Type = "Bar"; ID = 2 }
$obj | Select-PSFObject Name, "ID from list WHERE Type = Name"</dev:code>
        <dev:remarks>
          <maml:para>This allows you to LEFT JOIN contents of another variable. Note that it can only do simple property-matching at this point.</maml:para>
          <maml:para>It will select Name from the objects stored in $obj, and for each of those the ID Property on any object in $list that has a Type property of equal value as Name on the input.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>---------------- Example 5: Naming and styling ----------------</maml:title>
        <dev:code>Get-ChildItem | Select-PSFObject Name, Length, FullName, Used, LastWriteTime, Mode -TypeName MyType -ShowExcludeProperty Mode, Used</dev:code>
        <dev:remarks>
          <maml:para>Lists all items in the current path, selects the properties specified (whether they exist or not) , then ...</maml:para>
          <maml:para>- Sets the name to "MyType"</maml:para>
          <maml:para>- Hides the properties "Mode" and "Used" from the default display set, causing them to be hidden from default view</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Documentation</maml:linkText>
        <maml:uri>https://psframework.org/documentation/commands/PSFramework/Select-PSFObject.html</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Set-PSFConfig</command:name>
      <command:verb>Set</command:verb>
      <command:noun>PSFConfig</command:noun>
      <maml:description>
        <maml:para>Sets configuration entries.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This function creates or changes configuration values. These can be used to provide dynamic configuration information outside the PowerShell variable system.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Set-PSFConfig</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>FullName</maml:name>
          <maml:Description>
            <maml:para>The full name of a configuration element. Must be namespaced &lt;Module&gt;.&lt;Name&gt;. The name can have any number of sub-segments, in order to better group configurations thematically.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Value</maml:name>
          <maml:Description>
            <maml:para>The value to assign to the named configuration element.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Description</maml:name>
          <maml:Description>
            <maml:para>Using this, the configuration setting is given a description, making it easier for a user to comprehend, what a specific setting is for.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Validation</maml:name>
          <maml:Description>
            <maml:para>The name of the validation script used for input validation. These can be used to validate make sure that input is of the proper data type. New validation scripts can be registered using Register-PSFConfigValidation</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Handler</maml:name>
          <maml:Description>
            <maml:para>A scriptblock that is executed when a value is being set. Is only executed if the validation was successful (assuming there was a validation, of course)</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Hidden</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter hides the configuration from casual discovery. Configurations with this set will only be returned by Get-Config, if the parameter "-Force" is used. This should be set for all system settings a user should have no business changing (e.g. for Infrastructure related settings such as mail server).</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Default</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter causes the system to treat this configuration as a default setting. If the configuration already exists, no changes will be performed. Useful in scenarios where for some reason it is not practical to automatically set defaults before loading user profiles.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Initialize</maml:name>
          <maml:Description>
            <maml:para>Use this when setting configurations as part of module import. When initializing a configuration, it will only do a thing if the configuration hasn't already been initialized (So if you load the module multiple times or in multiple runspaces, it won't make a difference) Also, if there already was a non-initialized setting set for a given configuration, it will then try to set the old value again. This value will be processed by handlers, if any are set.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisableValidation</maml:name>
          <maml:Description>
            <maml:para>This parameters disables the input validation - if any - when processing a setting. Normally this shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisableHandler</maml:name>
          <maml:Description>
            <maml:para>This parameter disables the configuration handlers. Configuration handlers are designed to automatically process input set to a config value, in addition to writing the value. In many cases, this is used to improve performance, by forking the value location also to a static C#-field, which is then used, rather than searching a Hashtable. Normally these shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>Replaces user friendly yellow warnings with bloody red exceptions of doom! Use this if you want the function to throw terminating errors you want to catch.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SimpleExport</maml:name>
          <maml:Description>
            <maml:para>Enabling this will cause the module to use friendly json notation on export to file. This may result in loss of data precision, but makes it easier to edit settings in file.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ModuleExport</maml:name>
          <maml:Description>
            <maml:para>Using 'Export-PSFConfig -ModuleName &lt;ModuleName&gt;' settings flagged with this switch will be exported to a default path if they have been changed from the initial default value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PassThru</maml:name>
          <maml:Description>
            <maml:para>Return the changed configuration setting object.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AllowDelete</maml:name>
          <maml:Description>
            <maml:para>By default, settings that have been once defined are considered unremovable. Some workflows however require being able to fully dump configuration settings. Enable this switch to make a configuration setting deletable.</maml:para>
            <maml:para>Note:</maml:para>
            <maml:para>- Settings that are initialized, can only be declared deletable during initialization. Later attempts to change this, as well as previous settings will be ignored.</maml:para>
            <maml:para>- Settings that are defined and enforced by policy cannot be deleted no matter what.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Set-PSFConfig</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>FullName</maml:name>
          <maml:Description>
            <maml:para>The full name of a configuration element. Must be namespaced &lt;Module&gt;.&lt;Name&gt;. The name can have any number of sub-segments, in order to better group configurations thematically.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Description</maml:name>
          <maml:Description>
            <maml:para>Using this, the configuration setting is given a description, making it easier for a user to comprehend, what a specific setting is for.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Validation</maml:name>
          <maml:Description>
            <maml:para>The name of the validation script used for input validation. These can be used to validate make sure that input is of the proper data type. New validation scripts can be registered using Register-PSFConfigValidation</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Handler</maml:name>
          <maml:Description>
            <maml:para>A scriptblock that is executed when a value is being set. Is only executed if the validation was successful (assuming there was a validation, of course)</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Hidden</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter hides the configuration from casual discovery. Configurations with this set will only be returned by Get-Config, if the parameter "-Force" is used. This should be set for all system settings a user should have no business changing (e.g. for Infrastructure related settings such as mail server).</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Default</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter causes the system to treat this configuration as a default setting. If the configuration already exists, no changes will be performed. Useful in scenarios where for some reason it is not practical to automatically set defaults before loading user profiles.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Initialize</maml:name>
          <maml:Description>
            <maml:para>Use this when setting configurations as part of module import. When initializing a configuration, it will only do a thing if the configuration hasn't already been initialized (So if you load the module multiple times or in multiple runspaces, it won't make a difference) Also, if there already was a non-initialized setting set for a given configuration, it will then try to set the old value again. This value will be processed by handlers, if any are set.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisableValidation</maml:name>
          <maml:Description>
            <maml:para>This parameters disables the input validation - if any - when processing a setting. Normally this shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisableHandler</maml:name>
          <maml:Description>
            <maml:para>This parameter disables the configuration handlers. Configuration handlers are designed to automatically process input set to a config value, in addition to writing the value. In many cases, this is used to improve performance, by forking the value location also to a static C#-field, which is then used, rather than searching a Hashtable. Normally these shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>Replaces user friendly yellow warnings with bloody red exceptions of doom! Use this if you want the function to throw terminating errors you want to catch.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PersistedValue</maml:name>
          <maml:Description>
            <maml:para>In most circumstances an internal parameter. Applies the serialized value to a setting. Used for restoring data from configuration files that should only be deserialized when the module consuming it is already imported.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PersistedType</maml:name>
          <maml:Description>
            <maml:para>In most circumstances an internal parameter. Paired with PersistedValue, used to specify the data type of the serialized object set in its serialized state.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ConfigurationValueType</command:parameterValue>
          <dev:type>
            <maml:name>ConfigurationValueType</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SimpleExport</maml:name>
          <maml:Description>
            <maml:para>Enabling this will cause the module to use friendly json notation on export to file. This may result in loss of data precision, but makes it easier to edit settings in file.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ModuleExport</maml:name>
          <maml:Description>
            <maml:para>Using 'Export-PSFConfig -ModuleName &lt;ModuleName&gt;' settings flagged with this switch will be exported to a default path if they have been changed from the initial default value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PassThru</maml:name>
          <maml:Description>
            <maml:para>Return the changed configuration setting object.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AllowDelete</maml:name>
          <maml:Description>
            <maml:para>By default, settings that have been once defined are considered unremovable. Some workflows however require being able to fully dump configuration settings. Enable this switch to make a configuration setting deletable.</maml:para>
            <maml:para>Note:</maml:para>
            <maml:para>- Settings that are initialized, can only be declared deletable during initialization. Later attempts to change this, as well as previous settings will be ignored.</maml:para>
            <maml:para>- Settings that are defined and enforced by policy cannot be deleted no matter what.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Set-PSFConfig</maml:name>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Name</maml:name>
          <maml:Description>
            <maml:para>Name of the configuration entry. If an entry of exactly this non-casesensitive name already exists, its value will be overwritten. Duplicate names across different modules are possible and will be treated separately. If a name contains namespace notation and no module is set, the first namespace element will be used as module instead of name. Example: -Name "Nordwind.Server" Is Equivalent to -Name "Server" -Module "Nordwind"</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Module</maml:name>
          <maml:Description>
            <maml:para>This allows grouping configuration elements into groups based on the module/component they serve. If this parameter is not set, the configuration element must have a module name in the name parameter (the first segment will be taken, irrespective of whether that makes sense).</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Value</maml:name>
          <maml:Description>
            <maml:para>The value to assign to the named configuration element.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Description</maml:name>
          <maml:Description>
            <maml:para>Using this, the configuration setting is given a description, making it easier for a user to comprehend, what a specific setting is for.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Validation</maml:name>
          <maml:Description>
            <maml:para>The name of the validation script used for input validation. These can be used to validate make sure that input is of the proper data type. New validation scripts can be registered using Register-PSFConfigValidation</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Handler</maml:name>
          <maml:Description>
            <maml:para>A scriptblock that is executed when a value is being set. Is only executed if the validation was successful (assuming there was a validation, of course)</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
          <dev:type>
            <maml:name>ScriptBlock</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Hidden</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter hides the configuration from casual discovery. Configurations with this set will only be returned by Get-Config, if the parameter "-Force" is used. This should be set for all system settings a user should have no business changing (e.g. for Infrastructure related settings such as mail server).</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Default</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter causes the system to treat this configuration as a default setting. If the configuration already exists, no changes will be performed. Useful in scenarios where for some reason it is not practical to automatically set defaults before loading user profiles.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Initialize</maml:name>
          <maml:Description>
            <maml:para>Use this when setting configurations as part of module import. When initializing a configuration, it will only do a thing if the configuration hasn't already been initialized (So if you load the module multiple times or in multiple runspaces, it won't make a difference) Also, if there already was a non-initialized setting set for a given configuration, it will then try to set the old value again. This value will be processed by handlers, if any are set.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisableValidation</maml:name>
          <maml:Description>
            <maml:para>This parameters disables the input validation - if any - when processing a setting. Normally this shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>DisableHandler</maml:name>
          <maml:Description>
            <maml:para>This parameter disables the configuration handlers. Configuration handlers are designed to automatically process input set to a config value, in addition to writing the value. In many cases, this is used to improve performance, by forking the value location also to a static C#-field, which is then used, rather than searching a Hashtable. Normally these shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>Replaces user friendly yellow warnings with bloody red exceptions of doom! Use this if you want the function to throw terminating errors you want to catch.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>SimpleExport</maml:name>
          <maml:Description>
            <maml:para>Enabling this will cause the module to use friendly json notation on export to file. This may result in loss of data precision, but makes it easier to edit settings in file.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ModuleExport</maml:name>
          <maml:Description>
            <maml:para>Using 'Export-PSFConfig -ModuleName &lt;ModuleName&gt;' settings flagged with this switch will be exported to a default path if they have been changed from the initial default value.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PassThru</maml:name>
          <maml:Description>
            <maml:para>Return the changed configuration setting object.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>AllowDelete</maml:name>
          <maml:Description>
            <maml:para>By default, settings that have been once defined are considered unremovable. Some workflows however require being able to fully dump configuration settings. Enable this switch to make a configuration setting deletable.</maml:para>
            <maml:para>Note:</maml:para>
            <maml:para>- Settings that are initialized, can only be declared deletable during initialization. Later attempts to change this, as well as previous settings will be ignored.</maml:para>
            <maml:para>- Settings that are defined and enforced by policy cannot be deleted no matter what.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>FullName</maml:name>
        <maml:Description>
          <maml:para>The full name of a configuration element. Must be namespaced &lt;Module&gt;.&lt;Name&gt;. The name can have any number of sub-segments, in order to better group configurations thematically.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Name</maml:name>
        <maml:Description>
          <maml:para>Name of the configuration entry. If an entry of exactly this non-casesensitive name already exists, its value will be overwritten. Duplicate names across different modules are possible and will be treated separately. If a name contains namespace notation and no module is set, the first namespace element will be used as module instead of name. Example: -Name "Nordwind.Server" Is Equivalent to -Name "Server" -Module "Nordwind"</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Module</maml:name>
        <maml:Description>
          <maml:para>This allows grouping configuration elements into groups based on the module/component they serve. If this parameter is not set, the configuration element must have a module name in the name parameter (the first segment will be taken, irrespective of whether that makes sense).</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Value</maml:name>
        <maml:Description>
          <maml:para>The value to assign to the named configuration element.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Description</maml:name>
        <maml:Description>
          <maml:para>Using this, the configuration setting is given a description, making it easier for a user to comprehend, what a specific setting is for.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Validation</maml:name>
        <maml:Description>
          <maml:para>The name of the validation script used for input validation. These can be used to validate make sure that input is of the proper data type. New validation scripts can be registered using Register-PSFConfigValidation</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Handler</maml:name>
        <maml:Description>
          <maml:para>A scriptblock that is executed when a value is being set. Is only executed if the validation was successful (assuming there was a validation, of course)</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ScriptBlock</command:parameterValue>
        <dev:type>
          <maml:name>ScriptBlock</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Hidden</maml:name>
        <maml:Description>
          <maml:para>Setting this parameter hides the configuration from casual discovery. Configurations with this set will only be returned by Get-Config, if the parameter "-Force" is used. This should be set for all system settings a user should have no business changing (e.g. for Infrastructure related settings such as mail server).</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Default</maml:name>
        <maml:Description>
          <maml:para>Setting this parameter causes the system to treat this configuration as a default setting. If the configuration already exists, no changes will be performed. Useful in scenarios where for some reason it is not practical to automatically set defaults before loading user profiles.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Initialize</maml:name>
        <maml:Description>
          <maml:para>Use this when setting configurations as part of module import. When initializing a configuration, it will only do a thing if the configuration hasn't already been initialized (So if you load the module multiple times or in multiple runspaces, it won't make a difference) Also, if there already was a non-initialized setting set for a given configuration, it will then try to set the old value again. This value will be processed by handlers, if any are set.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>DisableValidation</maml:name>
        <maml:Description>
          <maml:para>This parameters disables the input validation - if any - when processing a setting. Normally this shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>DisableHandler</maml:name>
        <maml:Description>
          <maml:para>This parameter disables the configuration handlers. Configuration handlers are designed to automatically process input set to a config value, in addition to writing the value. In many cases, this is used to improve performance, by forking the value location also to a static C#-field, which is then used, rather than searching a Hashtable. Normally these shouldn't be circumvented, but just in case, it can be disabled.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnableException</maml:name>
        <maml:Description>
          <maml:para>Replaces user friendly yellow warnings with bloody red exceptions of doom! Use this if you want the function to throw terminating errors you want to catch.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PersistedValue</maml:name>
        <maml:Description>
          <maml:para>In most circumstances an internal parameter. Applies the serialized value to a setting. Used for restoring data from configuration files that should only be deserialized when the module consuming it is already imported.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PersistedType</maml:name>
        <maml:Description>
          <maml:para>In most circumstances an internal parameter. Paired with PersistedValue, used to specify the data type of the serialized object set in its serialized state.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ConfigurationValueType</command:parameterValue>
        <dev:type>
          <maml:name>ConfigurationValueType</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>SimpleExport</maml:name>
        <maml:Description>
          <maml:para>Enabling this will cause the module to use friendly json notation on export to file. This may result in loss of data precision, but makes it easier to edit settings in file.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ModuleExport</maml:name>
        <maml:Description>
          <maml:para>Using 'Export-PSFConfig -ModuleName &lt;ModuleName&gt;' settings flagged with this switch will be exported to a default path if they have been changed from the initial default value.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PassThru</maml:name>
        <maml:Description>
          <maml:para>Return the changed configuration setting object.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>AllowDelete</maml:name>
        <maml:Description>
          <maml:para>By default, settings that have been once defined are considered unremovable. Some workflows however require being able to fully dump configuration settings. Enable this switch to make a configuration setting deletable.</maml:para>
          <maml:para>Note:</maml:para>
          <maml:para>- Settings that are initialized, can only be declared deletable during initialization. Later attempts to change this, as well as previous settings will be ignored.</maml:para>
          <maml:para>- Settings that are defined and enforced by policy cannot be deleted no matter what.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues>
      <command:returnValue>
        <dev:type>
          <maml:name>PSFramework.Configuration.Config</maml:name>
        </dev:type>
        <maml:description>
          <maml:para></maml:para>
        </maml:description>
      </command:returnValue>
    </command:returnValues>
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>---------------------- Example 1: Simple ----------------------</maml:title>
        <dev:code>C:\PS&gt; Set-PSFConfig -FullName 'MyModule.User' -Value "Friedrich"</dev:code>
        <dev:remarks>
          <maml:para>Creates or updates a configuration entry under the module "MyModule" named "User" with the value "Friedrich"</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----------------- Example 2: Module Definition -----------------</maml:title>
        <dev:code>C:\PS&gt; Set-PSFConfig -Name 'mymodule.User' -Value "Friedrich" -Description "The user under which the show must go on." -Handler $scriptBlock -Initialize -Validation String</dev:code>
        <dev:remarks>
          <maml:para>Creates a configuration entry ... - Named "mymodule.user"</maml:para>
          <maml:para>- With the value "Friedrich"</maml:para>
          <maml:para>- It adds a description as noted</maml:para>
          <maml:para>- It registers the scriptblock stored in $scriptBlock as handler</maml:para>
          <maml:para>- It initializes the script.</maml:para>
          <maml:para>This block only executes the first time a it is run like this. Subsequent calls will be ignored. - It registers the basic string input type validator This is the default example for modules using the configuration system. Note: While the -Handler parameter is optional, it is important to add it at the initial initialize call, if you are planning to add it. Only then will the system validate previous settings (such as what a user might have placed in his user profile)</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------------------- Example 3: Hiding things -------------------</maml:title>
        <dev:code>C:\PS&gt; Set-PSFConfig 'Company' 'ConfigLink' 'https://www.example.com/config.xml' -Hidden</dev:code>
        <dev:remarks>
          <maml:para>Creates a configuration entry named "ConfigLink" in the "Company" module with the value 'https://www.example.com/config.xml'. This entry is hidden from casual discovery using Get-PSFConfig.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----------------- Example 4: Default Settings -----------------</maml:title>
        <dev:code>C:\PS&gt; Set-PSFConfig -FullName 'Network.Firewall' -Value '10.0.0.2' -Default</dev:code>
        <dev:remarks>
          <maml:para>Creates a configuration entry named "Firewall" in the "Network" module with the value '10.0.0.2' This is only set, if the setting does not exist yet. If it does, this command will apply no changes.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Documentation</maml:linkText>
        <maml:uri>https://psframework.org/documentation/commands/PSFramework/Set-PSFConfig.html</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Test-PSFShouldProcess</command:name>
      <command:verb>Test</command:verb>
      <command:noun>PSFShouldProcess</command:noun>
      <maml:description>
        <maml:para>Implements the shouldprocess question.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This command can be used in other commands to implement the ShouldProcess question asked if using the command requires confirmation. This replaces / wraps the traditional ShouldProcess call, makes it easier to read in script and allows mocking it.</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Test-PSFShouldProcess</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>The $PSCmdlet variable of the calling function. Used to ensure the actual prompt logic as intended is being used.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Target</maml:name>
          <maml:Description>
            <maml:para>The target that is being processed.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Action</maml:name>
          <maml:Description>
            <maml:para>The action that will be performed on the target.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:Description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:Description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Test-PSFShouldProcess</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>The $PSCmdlet variable of the calling function. Used to ensure the actual prompt logic as intended is being used.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Target</maml:name>
          <maml:Description>
            <maml:para>The target that is being processed.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ActionString</maml:name>
          <maml:Description>
            <maml:para>Localized string of the action that will be performed on the target. Omit the modulename in the string. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ActionStringValues</maml:name>
          <maml:Description>
            <maml:para>Specify values to format into the localization string specified. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
          <dev:type>
            <maml:name>Object[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
          <maml:name>Confirm</maml:name>
          <maml:Description>
            <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
          <maml:name>WhatIf</maml:name>
          <maml:Description>
            <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PSCmdlet</maml:name>
        <maml:Description>
          <maml:para>The $PSCmdlet variable of the calling function. Used to ensure the actual prompt logic as intended is being used.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
        <dev:type>
          <maml:name>PSCmdlet</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Target</maml:name>
        <maml:Description>
          <maml:para>The target that is being processed.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Action</maml:name>
        <maml:Description>
          <maml:para>The action that will be performed on the target.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ActionString</maml:name>
        <maml:Description>
          <maml:para>Localized string of the action that will be performed on the target. Omit the modulename in the string. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ActionStringValues</maml:name>
        <maml:Description>
          <maml:para>Specify values to format into the localization string specified. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
        <dev:type>
          <maml:name>Object[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="cf">
        <maml:name>Confirm</maml:name>
        <maml:Description>
          <maml:para>Prompts you for confirmation before running the cmdlet.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="wi">
        <maml:name>WhatIf</maml:name>
        <maml:Description>
          <maml:para>Shows what would happen if the cmdlet runs. The cmdlet is not run.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------------- Example 1: Basic Usage --------------------</maml:title>
        <dev:code>if (Test-PSFShouldProcess -PSCmdlet $PSCmdlet -Target $item -Action "Performing some arbitrary action") {

}</dev:code>
        <dev:remarks>
          <maml:para>This will test whether the user should be prompted for confirmation, will do so if necessary and report back the results.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Documentation</maml:linkText>
        <maml:uri>https://psframework.org/documentation/commands/PSFramework/Test-PSFShouldProcess.html</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
  <command:command xmlns:maml="http://schemas.microsoft.com/maml/2004/10" xmlns:command="http://schemas.microsoft.com/maml/dev/command/2004/10" xmlns:dev="http://schemas.microsoft.com/maml/dev/2004/10" xmlns:MSHelp="http://msdn.microsoft.com/mshelp">
    <command:details>
      <command:name>Write-PSFMessage</command:name>
      <command:verb>Write</command:verb>
      <command:noun>PSFMessage</command:noun>
      <maml:description>
        <maml:para>This function receives messages, then logs and reports them.</maml:para>
      </maml:description>
    </command:details>
    <maml:description>
      <maml:para>This function receives messages, then logs and reports them. It is designed to fully replace most Write-* commands, such as Write-Verbose or Write-Warning. Messages can be retrieved after the fact - whether they were shown on screen or not - using Get-PSFMessage. This includes messages written in other runspaces. All messages are logged in a runspace-safe manner, preventing write conflicts.</maml:para>
      <maml:para>This function will handle:</maml:para>
      <maml:para>- Warning output</maml:para>
      <maml:para>- Error management for non-terminating errors (For errors that terminate execution or continue on with the next object use "Stop-PSFFunction")</maml:para>
      <maml:para>- Logging</maml:para>
      <maml:para>- Verbose output</maml:para>
      <maml:para>- Message output to users</maml:para>
      <maml:para>For the complex description on how this works and how users and developers can influence it, run: Get-Help about_psf_message</maml:para>
    </maml:description>
    <command:syntax>
      <command:syntaxItem>
        <maml:name>Write-PSFMessage</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Level</maml:name>
          <maml:Description>
            <maml:para>This parameter represents the verbosity of the message. The lower the number, the more important it is for a human user to read the message. By default, the levels are distributed like this:</maml:para>
            <maml:para>- 1-3 Direct verbose output to the user (using Write-Host)</maml:para>
            <maml:para>- 4-6 Output only visible when requesting extra verbosity (using Write-Verbose)</maml:para>
            <maml:para>- 1-9 Debugging information, written using Write-Debug</maml:para>
            <maml:para>In addition, it is possible to select the level "Warning" which moves the message out of the configurable range: The user will always be shown this message, unless he silences the entire verbosity.</maml:para>
            <maml:para>Possible levels: Critical (1), Important / Output / Host (2), Significant (3), VeryVerbose (4), Verbose (5), SomewhatVerbose (6), System (7), Debug (8), InternalComment (9), Warning (666) Either one of the strings or its respective number will do as input.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">MessageLevel</command:parameterValue>
          <dev:type>
            <maml:name>MessageLevel</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>[PSFramework.Message.MessageLevel]::Verbose</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Message</maml:name>
          <maml:Description>
            <maml:para>The message to write/log. The function name and timestamp will automatically be prepended.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Tag</maml:name>
          <maml:Description>
            <maml:para>Tags to add to the message written. This allows filtering and grouping by category of message, targeting specific messages.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>FunctionName</maml:name>
          <maml:Description>
            <maml:para>The name of the calling function. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ModuleName</maml:name>
          <maml:Description>
            <maml:para>The name of the module, the calling function is part of. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>File</maml:name>
          <maml:Description>
            <maml:para>The file in which Write-PSFMessage was called. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Line</maml:name>
          <maml:Description>
            <maml:para>The line on which Write-PSFMessage was called. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ErrorRecord</maml:name>
          <maml:Description>
            <maml:para>If an error record should be noted with the message, add the full record here. Especially designed for use with Warning-mode, it can legally be used in either mode. The error will be added to the $Error variable and enqueued in the logging/debugging system.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ErrorRecord[]</command:parameterValue>
          <dev:type>
            <maml:name>ErrorRecord[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Exception</maml:name>
          <maml:Description>
            <maml:para>Allows specifying an inner exception as input object. This will be passed on to the logging and used for messages. When specifying both ErrorRecord AND Exception, Exception wins, but ErrorRecord is still used for record metadata.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Exception</command:parameterValue>
          <dev:type>
            <maml:name>Exception</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Once</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter will cause this function to write the message only once per session. The string passed here and the calling function's name are used to create a unique ID, which is then used to register the action in the configuration system. Thus will the lockout only be written if called once and not burden the system unduly. This lockout will be written as a hidden value, to see it use Get-PSFConfig -Force.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>OverrideExceptionMessage</maml:name>
          <maml:Description>
            <maml:para>Disables automatic appending of exception messages. Use in cases where you already have a speaking message interpretation and do not need the original message.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Target</maml:name>
          <maml:Description>
            <maml:para>Add the object the message is all about, in order to simplify debugging / troubleshooting. For example, when calling this from a function targeting a remote computer, the computername could be specified here, allowing all messages to easily be correlated to the object processed.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>This parameters disables user-friendly warnings and enables the throwing of exceptions. This is less user friendly, but allows catching exceptions in calling scripts.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Breakpoint</maml:name>
          <maml:Description>
            <maml:para>The breakpoint switch enables breaking on this debug message. By default, Write-PSFMessage will not interrupt execution if the -Debug parameter is specified, even when writing to the debug stream.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Format">
          <maml:name>StringValues</maml:name>
          <maml:Description>
            <maml:para>The values to format into the localized string defined. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
          <dev:type>
            <maml:name>Object[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoNewLine</maml:name>
          <maml:Description>
            <maml:para>Does not include a new line entry when writing a message to screen. Note: The message will still be logged as a single entry, even if you later add additional text.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Data</maml:name>
          <maml:Description>
            <maml:para>Specify additional data points that are furnished to the logging providers. Data provided may be arbitrary and will not be touched by the logging system itself.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Hashtable</command:parameterValue>
          <dev:type>
            <maml:name>Hashtable</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>The cmdlet object to use for writing errors. Offer the $PSCmdlet variable of the calling function for this parameter.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
      <command:syntaxItem>
        <maml:name>Write-PSFMessage</maml:name>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Level</maml:name>
          <maml:Description>
            <maml:para>This parameter represents the verbosity of the message. The lower the number, the more important it is for a human user to read the message. By default, the levels are distributed like this:</maml:para>
            <maml:para>- 1-3 Direct verbose output to the user (using Write-Host)</maml:para>
            <maml:para>- 4-6 Output only visible when requesting extra verbosity (using Write-Verbose)</maml:para>
            <maml:para>- 1-9 Debugging information, written using Write-Debug</maml:para>
            <maml:para>In addition, it is possible to select the level "Warning" which moves the message out of the configurable range: The user will always be shown this message, unless he silences the entire verbosity.</maml:para>
            <maml:para>Possible levels: Critical (1), Important / Output / Host (2), Significant (3), VeryVerbose (4), Verbose (5), SomewhatVerbose (6), System (7), Debug (8), InternalComment (9), Warning (666) Either one of the strings or its respective number will do as input.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">MessageLevel</command:parameterValue>
          <dev:type>
            <maml:name>MessageLevel</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>[PSFramework.Message.MessageLevel]::Verbose</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Tag</maml:name>
          <maml:Description>
            <maml:para>Tags to add to the message written. This allows filtering and grouping by category of message, targeting specific messages.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
          <dev:type>
            <maml:name>String[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>FunctionName</maml:name>
          <maml:Description>
            <maml:para>The name of the calling function. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ModuleName</maml:name>
          <maml:Description>
            <maml:para>The name of the module, the calling function is part of. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>File</maml:name>
          <maml:Description>
            <maml:para>The file in which Write-PSFMessage was called. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Line</maml:name>
          <maml:Description>
            <maml:para>The line on which Write-PSFMessage was called. Will be automatically set, but can be overridden when necessary.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
          <dev:type>
            <maml:name>Int32</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>ErrorRecord</maml:name>
          <maml:Description>
            <maml:para>If an error record should be noted with the message, add the full record here. Especially designed for use with Warning-mode, it can legally be used in either mode. The error will be added to the $Error variable and enqueued in the logging/debugging system.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">ErrorRecord[]</command:parameterValue>
          <dev:type>
            <maml:name>ErrorRecord[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Exception</maml:name>
          <maml:Description>
            <maml:para>Allows specifying an inner exception as input object. This will be passed on to the logging and used for messages. When specifying both ErrorRecord AND Exception, Exception wins, but ErrorRecord is still used for record metadata.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Exception</command:parameterValue>
          <dev:type>
            <maml:name>Exception</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Once</maml:name>
          <maml:Description>
            <maml:para>Setting this parameter will cause this function to write the message only once per session. The string passed here and the calling function's name are used to create a unique ID, which is then used to register the action in the configuration system. Thus will the lockout only be written if called once and not burden the system unduly. This lockout will be written as a hidden value, to see it use Get-PSFConfig -Force.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>OverrideExceptionMessage</maml:name>
          <maml:Description>
            <maml:para>Disables automatic appending of exception messages. Use in cases where you already have a speaking message interpretation and do not need the original message.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Target</maml:name>
          <maml:Description>
            <maml:para>Add the object the message is all about, in order to simplify debugging / troubleshooting. For example, when calling this from a function targeting a remote computer, the computername could be specified here, allowing all messages to easily be correlated to the object processed.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
          <dev:type>
            <maml:name>Object</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>EnableException</maml:name>
          <maml:Description>
            <maml:para>This parameters disables user-friendly warnings and enables the throwing of exceptions. This is less user friendly, but allows catching exceptions in calling scripts.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
          <dev:type>
            <maml:name>Boolean</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Breakpoint</maml:name>
          <maml:Description>
            <maml:para>The breakpoint switch enables breaking on this debug message. By default, Write-PSFMessage will not interrupt execution if the -Debug parameter is specified, even when writing to the debug stream.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>String</maml:name>
          <maml:Description>
            <maml:para>The key to the localized message (omitting the module name). For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
          <dev:type>
            <maml:name>String</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Format">
          <maml:name>StringValues</maml:name>
          <maml:Description>
            <maml:para>The values to format into the localized string defined. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
          <dev:type>
            <maml:name>Object[]</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>NoNewLine</maml:name>
          <maml:Description>
            <maml:para>Does not include a new line entry when writing a message to screen. Note: The message will still be logged as a single entry, even if you later add additional text.</maml:para>
          </maml:Description>
          <dev:type>
            <maml:name>SwitchParameter</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>False</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>Data</maml:name>
          <maml:Description>
            <maml:para>Specify additional data points that are furnished to the logging providers. Data provided may be arbitrary and will not be touched by the logging system itself.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">Hashtable</command:parameterValue>
          <dev:type>
            <maml:name>Hashtable</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
        <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
          <maml:name>PSCmdlet</maml:name>
          <maml:Description>
            <maml:para>The cmdlet object to use for writing errors. Offer the $PSCmdlet variable of the calling function for this parameter.</maml:para>
          </maml:Description>
          <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
          <dev:type>
            <maml:name>PSCmdlet</maml:name>
            <maml:uri />
          </dev:type>
          <dev:defaultValue>None</dev:defaultValue>
        </command:parameter>
      </command:syntaxItem>
    </command:syntax>
    <command:parameters>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Level</maml:name>
        <maml:Description>
          <maml:para>This parameter represents the verbosity of the message. The lower the number, the more important it is for a human user to read the message. By default, the levels are distributed like this:</maml:para>
          <maml:para>- 1-3 Direct verbose output to the user (using Write-Host)</maml:para>
          <maml:para>- 4-6 Output only visible when requesting extra verbosity (using Write-Verbose)</maml:para>
          <maml:para>- 1-9 Debugging information, written using Write-Debug</maml:para>
          <maml:para>In addition, it is possible to select the level "Warning" which moves the message out of the configurable range: The user will always be shown this message, unless he silences the entire verbosity.</maml:para>
          <maml:para>Possible levels: Critical (1), Important / Output / Host (2), Significant (3), VeryVerbose (4), Verbose (5), SomewhatVerbose (6), System (7), Debug (8), InternalComment (9), Warning (666) Either one of the strings or its respective number will do as input.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">MessageLevel</command:parameterValue>
        <dev:type>
          <maml:name>MessageLevel</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>[PSFramework.Message.MessageLevel]::Verbose</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Message</maml:name>
        <maml:Description>
          <maml:para>The message to write/log. The function name and timestamp will automatically be prepended.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Tag</maml:name>
        <maml:Description>
          <maml:para>Tags to add to the message written. This allows filtering and grouping by category of message, targeting specific messages.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String[]</command:parameterValue>
        <dev:type>
          <maml:name>String[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>FunctionName</maml:name>
        <maml:Description>
          <maml:para>The name of the calling function. Will be automatically set, but can be overridden when necessary.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ModuleName</maml:name>
        <maml:Description>
          <maml:para>The name of the module, the calling function is part of. Will be automatically set, but can be overridden when necessary.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>File</maml:name>
        <maml:Description>
          <maml:para>The file in which Write-PSFMessage was called. Will be automatically set, but can be overridden when necessary.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Line</maml:name>
        <maml:Description>
          <maml:para>The line on which Write-PSFMessage was called. Will be automatically set, but can be overridden when necessary.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Int32</command:parameterValue>
        <dev:type>
          <maml:name>Int32</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>ErrorRecord</maml:name>
        <maml:Description>
          <maml:para>If an error record should be noted with the message, add the full record here. Especially designed for use with Warning-mode, it can legally be used in either mode. The error will be added to the $Error variable and enqueued in the logging/debugging system.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">ErrorRecord[]</command:parameterValue>
        <dev:type>
          <maml:name>ErrorRecord[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Exception</maml:name>
        <maml:Description>
          <maml:para>Allows specifying an inner exception as input object. This will be passed on to the logging and used for messages. When specifying both ErrorRecord AND Exception, Exception wins, but ErrorRecord is still used for record metadata.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Exception</command:parameterValue>
        <dev:type>
          <maml:name>Exception</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Once</maml:name>
        <maml:Description>
          <maml:para>Setting this parameter will cause this function to write the message only once per session. The string passed here and the calling function's name are used to create a unique ID, which is then used to register the action in the configuration system. Thus will the lockout only be written if called once and not burden the system unduly. This lockout will be written as a hidden value, to see it use Get-PSFConfig -Force.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>OverrideExceptionMessage</maml:name>
        <maml:Description>
          <maml:para>Disables automatic appending of exception messages. Use in cases where you already have a speaking message interpretation and do not need the original message.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Target</maml:name>
        <maml:Description>
          <maml:para>Add the object the message is all about, in order to simplify debugging / troubleshooting. For example, when calling this from a function targeting a remote computer, the computername could be specified here, allowing all messages to easily be correlated to the object processed.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object</command:parameterValue>
        <dev:type>
          <maml:name>Object</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>EnableException</maml:name>
        <maml:Description>
          <maml:para>This parameters disables user-friendly warnings and enables the throwing of exceptions. This is less user friendly, but allows catching exceptions in calling scripts.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Boolean</command:parameterValue>
        <dev:type>
          <maml:name>Boolean</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Breakpoint</maml:name>
        <maml:Description>
          <maml:para>The breakpoint switch enables breaking on this debug message. By default, Write-PSFMessage will not interrupt execution if the -Debug parameter is specified, even when writing to the debug stream.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="true" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>String</maml:name>
        <maml:Description>
          <maml:para>The key to the localized message (omitting the module name). For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">String</command:parameterValue>
        <dev:type>
          <maml:name>String</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="Format">
        <maml:name>StringValues</maml:name>
        <maml:Description>
          <maml:para>The values to format into the localized string defined. For more details on the PSFramework localization feature, see the help on Import-PSFLocalizedString.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Object[]</command:parameterValue>
        <dev:type>
          <maml:name>Object[]</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>NoNewLine</maml:name>
        <maml:Description>
          <maml:para>Does not include a new line entry when writing a message to screen. Note: The message will still be logged as a single entry, even if you later add additional text.</maml:para>
        </maml:Description>
        <command:parameterValue required="false" variableLength="false">SwitchParameter</command:parameterValue>
        <dev:type>
          <maml:name>SwitchParameter</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>False</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>Data</maml:name>
        <maml:Description>
          <maml:para>Specify additional data points that are furnished to the logging providers. Data provided may be arbitrary and will not be touched by the logging system itself.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">Hashtable</command:parameterValue>
        <dev:type>
          <maml:name>Hashtable</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
      <command:parameter required="false" variableLength="true" globbing="false" pipelineInput="False" position="named" aliases="none">
        <maml:name>PSCmdlet</maml:name>
        <maml:Description>
          <maml:para>The cmdlet object to use for writing errors. Offer the $PSCmdlet variable of the calling function for this parameter.</maml:para>
        </maml:Description>
        <command:parameterValue required="true" variableLength="false">PSCmdlet</command:parameterValue>
        <dev:type>
          <maml:name>PSCmdlet</maml:name>
          <maml:uri />
        </dev:type>
        <dev:defaultValue>None</dev:defaultValue>
      </command:parameter>
    </command:parameters>
    <command:inputTypes />
    <command:returnValues />
    <maml:alertSet>
      <maml:alert>
        <maml:para></maml:para>
      </maml:alert>
    </maml:alertSet>
    <command:examples>
      <command:example>
        <maml:title>-------------- Example 1: Simple verbose message --------------</maml:title>
        <dev:code>Write-PSFMessage -Message "Connecting to $computer"</dev:code>
        <dev:remarks>
          <maml:para>Writes the message "Connecting to $computer" to verbose. Will also log the message.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----------------- Example 2: Writing a warning -----------------</maml:title>
        <dev:code>Write-PSFMessage -Level Warning -Message "Failed to retrieve additional network adapter information from $computer"</dev:code>
        <dev:remarks>
          <maml:para>Writes the message "Failed to retrieve additional network adapter information from $computer" as a warning. Will also log the message.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------------ Example 3: Including extra information ------------</maml:title>
        <dev:code>Write-PSFMessage -Level Verbose -Message "Connecting to $computer" -Target $computer</dev:code>
        <dev:remarks>
          <maml:para>Writes the message "Connecting to $computer" to verbose. Includes the variable $computer in the message. This has no effect on the text shown but will be available for debugging purposes. Will also log the message.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------------ Example 4: Writing a message once only ------------</maml:title>
        <dev:code>Write-PSFMessage -Level Host -Message "This command has been deprecated, use 'Get-NewExample' instead" -Once 'Get-Example'</dev:code>
        <dev:remarks>
          <maml:para>Writes the message "This command has been deprecated, use 'Get-NewExample' instead" to the screen. This message will only be shown once per powershell process. Will also log the message.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>--------------- Example 5: Passing on exceptions ---------------</maml:title>
        <dev:code>Write-PSFMessage -Level Warning -Message "Failed to retrieve additional network adapter information from $computer" -Target $computer -ErrorRecord $_</dev:code>
        <dev:remarks>
          <maml:para>This example assumes to be executed within a catch block. Writes the message "Failed to retrieve additional network adapter information from $computer" as a warning. Will also append the message of the exception to the text. Will also add the error record to the error log Will also log the message.</maml:para>
          <maml:para>-</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------------- Example 6: Overriding Error Messages -------------</maml:title>
        <dev:code>Write-PSFMessage -Level Warning -Message "Failed to change network adapter settings, this command requires elevation. Restart your console as admin and try again" -Target $computer -ErrorRecord $_ -OverrideExceptionMessage</dev:code>
        <dev:remarks>
          <maml:para>Similar to Example 5, this shows how to write an error message with debugging information attached. The key difference here is that this assumes the error message has been properly interpreted for the end user, making it no longer desirable to automatically append the exception message.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>----------------- Example 7: Tagging messages -----------------</maml:title>
        <dev:code>Write-PSFMessage -Message "Connecting to $computer" -Target $computer -Tag 'connect','CIM'</dev:code>
        <dev:remarks>
          <maml:para>This shows how to use the -Tag parameter to add tags to a message. These can later be used with Get-PSFMessage or the logs to filter messages by. Tags can also be used to apply rules to, such as where to log to.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>--------- Example 8: Overriding debugging information ---------</maml:title>
        <dev:code>Write-PSFMessage -Message "Connecting to $computer" -Line 21 -File "Connect-Computer.ps1"</dev:code>
        <dev:remarks>
          <maml:para>The message system contains a lot of extra information that can be useful when debugging, such as file and line it was written from. However depending on your build and import practices, those might not be a perfect fit for you. Thus it is possible to override them, to aid with debugging your code.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>------ Example 9: Explicitly specifying meta information ------</maml:title>
        <dev:code>Write-PSFMessage -Message "Doing something" -FunctionName "Get-BeerParallel" -ModuleName "BeerFactory"</dev:code>
        <dev:remarks>
          <maml:para>By default, Write-PSFMessage automatically gathers module and function information. However, this doesn't always work, for example on scriptblocks that are run in separate runspaces. For those instances, you can manually specify the command to display. Note: Logging is runspace-safe, so you can use this command parallel in multiple runspaces. All messages from all runspaces are visible with Get-PSFMessage.</maml:para>
          <maml:para>_</maml:para>
        </dev:remarks>
      </command:example>
      <command:example>
        <maml:title>--------------- Example 10: Supporting Debugging ---------------</maml:title>
        <dev:code>Write-PSFMessage -Level Debug -Message "Executing SQL Query 'SELECT * FROM table'" -Breakpoint</dev:code>
        <dev:remarks>
          <maml:para>By using the breakpoint parameter in this message, PowerShell will break into the debugger at this position, if the command writing this message is run with the -Debug parameter. By default, Write-PSFMessage will never interrupt execution when -Debug is specified, in contrast to how Write-Debug acts.</maml:para>
        </dev:remarks>
      </command:example>
    </command:examples>
    <command:relatedLinks>
      <maml:navigationLink>
        <maml:linkText>Online Documentation</maml:linkText>
        <maml:uri>https://psframework.org/documentation/commands/PSFramework/Write-PSFMessage.html</maml:uri>
      </maml:navigationLink>
    </command:relatedLinks>
  </command:command>
</helpItems>