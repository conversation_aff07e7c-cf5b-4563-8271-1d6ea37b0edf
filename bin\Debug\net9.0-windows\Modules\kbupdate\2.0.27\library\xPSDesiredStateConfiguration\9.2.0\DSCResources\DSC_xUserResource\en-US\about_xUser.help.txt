.NAME
    xUser

.DESCRIPTION
    Provides a mechanism to manage local users on a target node.

.PARAMETER UserName
    Key - String
    Indicates the account name for which you want to ensure a specific state.

.PARAMETER Ensure
    Write - String
    Allowed values: Present, Absent
    Specified if the user account is present or absent.

.PARAMETER FullName
    Write - String
    The full name of the user account.

.PARAMETER Description
    Write - String
    Indicates the description you want to use for the user account.

.PARAMETER Password
    Write - Instance
    Indicates the password you want to use for this account.

.PARAMETER Disabled
    Write - Boolean
    Indicates if the account is enabled. Set this property to $true to ensure that this account is disabled, and set it to $false to ensure that it is enabled. Defaults to $false.

.PARAMETER PasswordNeverExpires
    Write - Boolean
    Indicates if the password will expire. To ensure that the password for this account will never expire, set this property to $true, and set it to $false if the password will expire.

.PARAMETER PasswordChangeRequired
    Write - Boolean
    Indicates if the user must change the password at the next sign in. Set this property to $true if the user must change the password.

.PARAMETER PasswordChangeNotAllowed
    Write - Boolean
    Indicates if the user can change the password. Set this property to $true to ensure that the user cannot change the password, and set it to $false to allow the user to change the password.

.EXAMPLE 1


#Requires -Module xPSDesiredStateConfiguration

<#
    .DESCRIPTION
        Configuration that creates a local user account using the given credentials.

    .PARAMETER Credential
        Credentials to use to create the local user account.

    .EXAMPLE
        xUser_CreateUser_Config -Credential (Get-Credential)

        Compiles a configuration that creates a local user account.
#>
Configuration xUser_CreateUser_Config
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $Credential
    )

    Import-DscResource -ModuleName xPSDesiredStateConfiguration

    Node localhost
    {
        xUser CreateUserAccount
        {
            Ensure   = 'Present'
            UserName = Split-Path -Path $Credential.UserName -Leaf
            Password = $Credential
        }
    }
}

.EXAMPLE 2


#Requires -Module xPSDesiredStateConfiguration

<#
    .DESCRIPTION
        Configuration that creates a local user account using the given credentials.

    .PARAMETER Credential
        Credentials to use to create the local user account.

    .PARAMETER FullName
        Full name of the local user account. Defaults to the name passed in the credentials.

    .PARAMETER Description
        Description of the local user account. Defaults to no description.

    .PARAMETER PasswordNeverExpires
        To ensure that the password for this account will never expire, set this
        property to $true, and set it to $false if the password will expire.
        Defaults to $false.

    .PARAMETER PasswordChangeRequired
        If the user must change the password at the next sign in. Set this
        property to $true if the user must change the password. Defaults to
        $false.

    .PARAMETER PasswordChangeNotAllowed
        If the user can change the password. Set this property to $true to ensure
        that the user cannot change the password, and set it to $false to allow
        the user to change the password. Defaults to $false.

    .PARAMETER Disabled
        If the account is enabled. Set this property to $true to ensure that
        this account is disabled, and set it to $false to ensure that it is
        enabled. Defaults to $false.

    .NOTES
        If you want to create a user with minimal attributes, every parameter,
        except username and password, can be deleted since they are optional.

        If the parameters are present then they will be evaluated to be in
        desired state, meaning if for example Description parameter is left as
        the default value, then the desired state is to have no description on
        the local user account.

    .EXAMPLE
        xUser_CreateUserDetailed_Config -Credential = (Get-Credential) -FullName = 'MyUser' -Description = 'My local user account' -PasswordNeverExpires = $true -PasswordChangeRequired = $false -PasswordChangeNotAllowed = $false -Disabled = $false

        Compiles a configuration that creates a local user account.
#>
Configuration xUser_CreateUserDetailed_Config
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $Credential,

        [Parameter()]
        [System.String]
        $FullName,

        [Parameter()]
        [System.String]
        $Description,

        [Parameter()]
        [System.Boolean]
        $PasswordNeverExpires,

        [Parameter()]
        [System.Boolean]
        $PasswordChangeRequired,

        [Parameter()]
        [System.Boolean]
        $PasswordChangeNotAllowed,

        [Parameter()]
        [System.Boolean]
        $Disabled
    )

    Import-DscResource -ModuleName xPSDesiredStateConfiguration

    Node localhost
    {
        if (-not $FullName)
        {
            $FullName = $Credential.UserName
        }

        xUser CreateUserAccount
        {
            Ensure                   = 'Present'
            UserName                 = Split-Path -Path $Credential.UserName -Leaf
            Password                 = $Credential
            FullName                 = $FullName
            Description              = $Description
            PasswordNeverExpires     = $PasswordNeverExpires
            PasswordChangeRequired   = $PasswordChangeRequired
            PasswordChangeNotAllowed = $PasswordChangeNotAllowed
            Disabled                 = $Disabled
        }
    }
}

.EXAMPLE 3


#Requires -Module xPSDesiredStateConfiguration

<#
    .DESCRIPTION
        Configuration that removes a local user account using the given username.

    .PARAMETER UserName
        The username of the local user account to remove.

    .EXAMPLE
        xUser_RemoveUser_Config -UserName 'MyUser'

        Compiles a configuration that removes a local user account.
#>
Configuration xUser_RemoveUser_Config
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $UserName
    )

    Import-DscResource -ModuleName xPSDesiredStateConfiguration

    Node localhost
    {
        xUser RemoveUserAccount
        {
            Ensure   = 'Absent'
            UserName = $UserName
        }
    }
}

