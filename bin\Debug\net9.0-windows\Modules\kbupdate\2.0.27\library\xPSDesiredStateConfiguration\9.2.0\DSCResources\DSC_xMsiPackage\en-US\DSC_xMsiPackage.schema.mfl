[Description("Provides a mechanism to install and uninstall .msi packages.") : Amended,AMENDMENT, LOCALE("MS_409")]
class DSC_xMsiPackage : OMI_BaseResource
{
  [Key,Description("The identifying number used to uniquely identify this package") : Amended] String ProductId;
  [Description("The path, URL or UNC path to the package") : Amended] String Path;
  [Description("Indicates whether to Ensure that the package is Present or Absent (default Present)") : Amended] String Ensure;
  [Description("The arguments to be passed to the package during addition or removal") : Amended] String Arguments;
  [Description("The credentials to be used for mounting the UNC path (if applicable)") : Amended] String Credential;
  [Description("The path to log the output of the MSI") : Amended] String LogPath;
  [Description("The expected hash value of the file found in the Path location.") : Amended] String FileHash;
  [Description("The algorithm used to generate the FileHash value.  Defaults to SHA256") : Amended] String HashAlgorithm;
  [Description("The subject that must match the signer certificate of the digital signature. Wildcards are allowed.") : Amended] String SignerSubject;
  [Description("The certificate thumbprint which must match the signer certificate of the digital signature.") : Amended] String SignerThumbprint;
  [Description("PowerShell code used to validate SSL certificates of HTTPS url assigned to Path.") : Amended] String ServerCertificateValidationCallback;
  [Description("The credentials under which to run the installation") : Amended] String RunAsCredential;
  [Description("The display name of the identified package") : Amended] String Name;
  [Description("The path to the identified package") : Amended] String InstallSource;
  [Description("The date that the identified package was last serviced or its install date, whichever is later") : Amended] String InstalledOn;
  [Description("The size of the identified package in MB") : Amended] UInt32 Size;
  [Description("The version number of the identified package") : Amended] String Version;
  [Description("The description of the identified package") : Amended] String PackageDescription;
  [Description("The publisher for the identified package") : Amended] String Publisher;
};
