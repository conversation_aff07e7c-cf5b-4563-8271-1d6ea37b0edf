
[ClassVersion("1.0.0"),FriendlyName("xGroup")]
class DSC_xGroupResource : OMI_BaseResource
{
  [Key, Description("The name of the group to create, modify, or remove.")] String GroupName;
  [Write, Description("Indicates if the group should exist or not."), ValueMap{"Present", "Absent"}, Values{"Present", "Absent"}] String Ensure;
  [Write, Description("The description the group should have.")] String Description;
  [Write, Description("The members the group should have. This property will replace all the current group members with the specified members. Members should be specified as strings in the format of their domain qualified name, UPN ,distinguished name or username (for local machine accounts). Using either the MembersToExclude or MembersToInclude properties in the same configuration as this property will generate an error.")] String Members[];
  [Write, Description("The members the group should include. This property will only add members to a group. Members should be specified as strings in the format of their domain qualified name, UPN ,distinguished name or username (for local machine accounts). Using the Members property in the same configuration as this property will generate an error.")] String MembersToInclude[];
  [Write, Description("The members the group should exclude. This property will only remove members from a group. Members should be specified as strings in the format of their domain qualified name, UPN ,distinguished name or username (for local machine accounts). Using the Members property in the same configuration as this property will generate an error.")] String MembersToExclude[];
  [Write, Description("A credential to resolve non-local group members."), EmbeddedInstance("MSFT_Credential")] String Credential;
};
