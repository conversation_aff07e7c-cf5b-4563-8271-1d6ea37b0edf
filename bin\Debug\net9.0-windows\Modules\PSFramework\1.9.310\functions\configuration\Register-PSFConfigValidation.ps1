﻿function Register-PSFConfigValidation
{
	<#
		.SYNOPSIS
			Registers a validation scriptblock for use with the configuration system.
		
		.DESC<PERSON><PERSON><PERSON><PERSON>
			Registers a validation scriptblock for use with the configuration system.
	
			The scriptblock must be designed according to a few guidelines:
			- It must not throw exceptions
			- It must accept a single parameter (the value to be tested)
			- It must return an object with two properties: 'Message', 'Value' and 'Success'.
			The Success property should be boolean and indicate whether the value is valid.
			The Value property contains the validated input. The scriptblock may legally convert the input (For example from string to int in case of integer validation)
			The message contains a string that will be passed along to an exception in case the input is NOT valid.
		
		.PARAMETER Name
			The name under which to register the validation scriptblock
		
		.PARAMETER ScriptBlock
			The scriptblock to register
		
		.EXAMPLE
			PS C:\> Register-PSFConfigValidation -Name IntPositive -ScriptBlock $scriptblock
	
			Registers the scriptblock stored in $scriptblock as validation with the name IntPositive
	#>
	[CmdletBinding(HelpUri = 'https://psframework.org/documentation/commands/PSFramework/Register-PSFConfigValidation')]
	Param (
		[Parameter(Mandatory = $true)]
		[string]
		$Name,
		
		[Parameter(Mandatory = $true)]
		[ScriptBlock]
		$ScriptBlock
	)
	process
	{
		[PSFramework.Configuration.ConfigurationHost]::Validation[$Name] = $ScriptBlock
	}
}