$errorActionPreference = 'Stop'
Set-StrictMode -Version 'Latest'

$modulePath = Join-Path -Path (Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent) -ChildPath 'Modules'

# Import the shared modules
Import-Module -Name (Join-Path -Path $modulePath `
    -ChildPath (Join-Path -Path 'xPSDesiredStateConfiguration.Common' `
        -ChildPath 'xPSDesiredStateConfiguration.Common.psm1'))

Import-Module -Name (Join-Path -Path $modulePath -ChildPath 'DscResource.Common')
<#
    .SYNOPSIS
        DSC Composite Resource uploads file or folder to an SMB share.

    .DESCRIPTION
        This is a DSC Composite resource that can be used to upload
        a file or folder into an SMB file share. The SMB file share
        does not have to be currently mounted. It will be mounted
        during the upload process using the optional Credential
        and then dismounted after completion of the upload.

    .PARAMETER DestinationPath
        The destination SMB share path to upload the file or folder to.

    .PARAMETER SourcePath
        The source path of the file or folder to upload.

    .PARAMETER Credential
        Credentials to access the destination SMB share path where file
        or folder should be uploaded.

    .PARAMETER certificateThumbprint
        Thumbprint of the certificate which should be used for encryption/decryption.

    .EXAMPLE
        $securePassword = ConvertTo-SecureString -String 'password' -AsPlainText -Force
        $credential = New-Object -TypeName System.Management.Automation.PSCredential -ArgumentList 'domain\user', $securePassword
        xFileUpload `
            -DestinationPath '\\machine\share\destinationfolder' `
            -SourcePath 'C:\folder\file.txt' `
            -Credential $credential
#>
configuration xFileUpload
{
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingConvertToSecureStringWithPlainText', '')]
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('DscResource.AnalyzerRules\Measure-Keyword', '', Justification = 'Script resource name is seen as a keyword if this is not used.')]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $DestinationPath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $SourcePath,

        [Parameter()]
        [System.Management.Automation.Credential()]
        [System.Management.Automation.PSCredential]
        $Credential,

        [Parameter()]
        [System.String]
        $CertificateThumbprint
    )

    $cacheLocation = "$env:ProgramData\Microsoft\Windows\PowerShell\configuration\BuiltinProvCache\DSC_xFileUpload"

    if ($Credential)
    {
        $username = $Credential.UserName

        # Encrypt password
        $password = Invoke-Command `
            -ScriptBlock $getEncryptedPassword `
            -ArgumentList $Credential, $CertificateThumbprint
    }

    Script FileUpload
    {
        # Get script is not implemented cause reusing Script resource's schema does not make sense
        GetScript  = {
            return @{}
        };

        SetScript  = {
            # Generating credential object if password and username are specified
            $Credential = $null

            if (($using:password) -and ($using:username))
            {
                # Validate that certificate thumbprint is specified
                if (-not $using:CertificateThumbprint)
                {
                    $errorMessage = 'Certificate thumbprint has to be specified if credentials are present.'
                    Invoke-Command `
                        -ScriptBlock $using:throwTerminatingError `
                        -ArgumentList 'CertificateThumbprintIsRequired', $errorMessage, 'InvalidData'
                }

                Write-Debug -Message 'Username and password specified.'

                # Decrypt password
                $decryptedPassword = Invoke-Command `
                    -ScriptBlock $using:getDecryptedPassword `
                    -ArgumentList $using:password, $using:CertificateThumbprint

                # Generate credential
                $securePassword = ConvertTo-SecureString -String $decryptedPassword -AsPlainText -Force
                $Credential = New-Object `
                    -TypeName System.Management.Automation.PSCredential `
                    -ArgumentList ($using:username, $securePassword)
            }

            # Validate DestinationPath is UNC path
            if (-not ($using:DestinationPath -as [System.Uri]).isUnc)
            {
                $errorMessage = "Destination path $using:DestinationPath is not a valid UNC path."
                Invoke-Command `
                    -ScriptBlock $using:throwTerminatingError `
                    -ArgumentList 'DestinationPathIsNotUNCFailure', $errorMessage, 'InvalidData'
            }

            # Verify source is localpath
            if (-not (($using:SourcePath -as [System.Uri]).Scheme -match 'file'))
            {
                $errorMessage = "Source path $using:SourcePath has to be local path."
                Invoke-Command `
                    -ScriptBlock $using:throwTerminatingError `
                    -ArgumentList 'SourcePathIsNotLocalFailure', $errorMessage, 'InvalidData'
            }

            # Check whether source path is existing file or directory
            $sourcePathType = $null

            if (-not (Test-Path -Path $using:SourcePath))
            {
                $errorMessage = "Source path $using:SourcePath does not exist."
                Invoke-Command `
                    -ScriptBlock $using:throwTerminatingError `
                    -ArgumentList 'SourcePathDoesNotExistFailure', $errorMessage, 'InvalidData'
            }
            else
            {
                $item = Get-Item -Path $using:SourcePath

                switch ($item.GetType().Name)
                {
                    'FileInfo'
                    {
                        $sourcePathType = 'File'
                    }

                    'DirectoryInfo'
                    {
                        $sourcePathType = 'Directory'
                    }
                }
            }

            Write-Debug -Message "SourcePath $using:SourcePath is of type: $sourcePathType"

            $psDrive = $null

            # Mount the drive only if Credentials are specified and it's currently not accessible
            if ($Credential)
            {
                if (Test-Path -Path $using:DestinationPath -ErrorAction Ignore)
                {
                    Write-Debug -Message "Destination path $using:DestinationPath is already accessible. No mount needed."
                }
                else
                {
                    $psDriveArgs = @{
                        Name       = ([System.Guid]::NewGuid())
                        PSProvider = 'FileSystem'
                        Root       = $using:DestinationPath
                        Scope      = 'Private'
                        Credential = $Credential
                    }

                    try
                    {
                        Write-Debug -Message "Create psdrive with destination path $using:DestinationPath..."
                        $psDrive = New-PSDrive @psDriveArgs -ErrorAction Stop
                    }
                    catch
                    {
                        $errorMessage = "Cannot access destination path $using:DestinationPath with given Credential"
                        Invoke-Command `
                            -ScriptBlock $using:throwTerminatingError `
                            -ArgumentList 'DestinationPathNotAccessibleFailure', $errorMessage, 'InvalidData'
                    }
                }
            }

            try
            {
                # Get expected destination path
                $expectedDestinationPath = $null

                if (-not (Test-Path -Path $using:DestinationPath))
                {
                    # DestinationPath has to exist
                    $errorMessage = 'Invalid parameter values: DestinationPath does not exist, but has to be existing directory.'
                    Throw-TerminatingError -ErrorMessage $errorMessage -ErrorCategory 'InvalidData' -ErrorId 'DestinationPathDoesNotExistFailure'
                }
                else
                {
                    $item = Get-Item -Path $using:DestinationPath

                    switch ($item.GetType().Name)
                    {
                        'FileInfo'
                        {
                            # DestinationPath cannot be file
                            $errorMessage = 'Invalid parameter values: DestinationPath is file, but has to be existing directory.'
                            Invoke-Command `
                                -ScriptBlock $using:throwTerminatingError `
                                -ArgumentList 'DestinationPathCannotBeFileFailure', $errorMessage, 'InvalidData'
                        }

                        'DirectoryInfo'
                        {
                            $expectedDestinationPath = Join-Path `
                                -Path $using:DestinationPath `
                                -ChildPath (Split-Path -Path $using:SourcePath -Leaf)
                        }
                    }

                    Write-Debug -Message "ExpectedDestinationPath is $expectedDestinationPath"
                }

                # Copy destination path
                try
                {
                    Write-Debug -Message "Copying $using:SourcePath to $using:DestinationPath"
                    Copy-Item -Path $using:SourcePath -Destination $using:DestinationPath -Recurse -Force -ErrorAction Stop
                }
                catch
                {
                    $errorMessage = "Could not copy source path $using:SourcePath to $using:DestinationPath : $($_.Exception)"
                    Invoke-Command `
                        -ScriptBlock $using:throwTerminatingError `
                        -ArgumentList 'CopyDirectoryOverFileFailure', $errorMessage, 'InvalidData'
                }

                # Verify whether expectedDestinationPath was created
                if (-not (Test-Path -Path $expectedDestinationPath))
                {
                    $errorMessage = "Destination path $using:DestinationPath could not be created"
                    Invoke-Command `
                        -ScriptBlock $using:throwTerminatingError `
                        -ArgumentList 'DestinationPathNotCreatedFailure', $errorMessage, 'InvalidData'
                }
                # If expectedDestinationPath exists
                else
                {
                    Write-Verbose -Message "$sourcePathType $expectedDestinationPath has been successfully created"

                    # Update cache
                    $uploadedItem = Get-Item -Path $expectedDestinationPath
                    $lastWriteTime = $uploadedItem.LastWriteTimeUtc
                    $inputObject = @{}
                    $inputObject['LastWriteTimeUtc'] = $lastWriteTime
                    $key = [System.String]::Join('', @($using:DestinationPath, $using:SourcePath, $expectedDestinationPath)).GetHashCode().ToString()
                    $path = Join-Path $using:cacheLocation $key

                    if (-not (Test-Path -Path $using:cacheLocation))
                    {
                        New-Item -Path $using:cacheLocation -ItemType Directory | Out-Null
                    }

                    Write-Debug -Message "Updating cache for DestinationPath = $using:DestinationPath and SourcePath = $using:SourcePath. CacheKey = $key"
                    Export-CliXml -Path $path -InputObject $inputObject -Force
                }
            }
            finally
            {
                # Remove PSDrive
                if ($psDrive)
                {
                    Write-Debug -Message "Removing PSDrive on root $($psDrive.Root)"
                    Remove-PSDrive -Name $psDrive -Force
                }
            }
        };

        TestScript = {
            # Generating credential object if password and username are specified
            $Credential = $null

            if (($using:password) -and ($using:username))
            {
                # Validate that certificate thumbprint is specified
                if (-not $using:CertificateThumbprint)
                {
                    $errorMessage = 'Certificate thumbprint has to be specified if credentials are present.'
                    Invoke-Command `
                        -ScriptBlock $using:throwTerminatingError `
                        -ArgumentList 'CertificateThumbprintIsRequired', $errorMessage, 'InvalidData'
                }

                Write-Debug -Message 'Username and password specified. Generating credential'

                # Decrypt password
                $decryptedPassword = Invoke-Command `
                    -ScriptBlock $using:getDecryptedPassword `
                    -ArgumentList $using:password, $using:CertificateThumbprint

                # Generate credential
                $securePassword = ConvertTo-SecureString -String $decryptedPassword -AsPlainText -Force
                $Credential = New-Object `
                    -TypeName System.Management.Automation.PSCredential `
                    -ArgumentList ($using:username, $securePassword)
            }
            else
            {
                Write-Debug -Message 'No credentials specified.'
            }

            # Validate DestinationPath is UNC path
            if (-not ($using:DestinationPath -as [System.Uri]).isUnc)
            {
                $errorMessage = "Destination path $using:DestinationPath is not a valid UNC path."
                Invoke-Command `
                    -ScriptBlock $using:throwTerminatingError `
                    -ArgumentList 'DestinationPathIsNotUNCFailure', $errorMessage, 'InvalidData'

            }

            # Check whether source path is existing file or directory (needed for expectedDestinationPath)
            $sourcePathType = $null
            if (-not (Test-Path -Path $using:SourcePath))
            {
                $errorMessage = "Source path $using:SourcePath does not exist."
                Invoke-Command `
                    -ScriptBlock $using:throwTerminatingError `
                    -ArgumentList 'SourcePathDoesNotExistFailure', $errorMessage, 'InvalidData'
            }
            else
            {
                $item = Get-Item -Path $using:SourcePath

                switch ($item.GetType().Name)
                {
                    'FileInfo'
                    {
                        $sourcePathType = 'File'
                    }

                    'DirectoryInfo'
                    {
                        $sourcePathType = 'Directory'
                    }
                }
            }

            Write-Debug -Message "SourcePath $using:SourcePath is of type: $sourcePathType"

            $psDrive = $null

            # Mount the drive only if credentials are specified and it's currently not accessible
            if ($Credential)
            {
                if (Test-Path -Path $using:DestinationPath -ErrorAction Ignore)
                {
                    Write-Debug -Message "Destination path $using:DestinationPath is already accessible. No mount needed."
                }
                else
                {
                    $psDriveArgs = @{
                        Name       = ([System.Guid]::NewGuid())
                        PSProvider = 'FileSystem'
                        Root       = $using:DestinationPath
                        Scope      = 'Private'
                        Credential = $Credential

                    }
                    try
                    {
                        Write-Debug -Message "Create psdrive with destination path $using:DestinationPath..."
                        $psDrive = New-PSDrive @psDriveArgs -ErrorAction Stop
                    }
                    catch
                    {
                        $errorMessage = "Cannot access destination path $using:DestinationPath with given Credential"
                        Invoke-Command `
                            -ScriptBlock $using:throwTerminatingError `
                            -ArgumentList 'DestinationPathNotAccessibleFailure', $errorMessage, 'InvalidData'
                    }
                }
            }

            try
            {
                # Get expected destination path
                $expectedDestinationPath = $null

                if (-not (Test-Path -Path $using:DestinationPath))
                {
                    # DestinationPath has to exist
                    $errorMessage = 'Invalid parameter values: DestinationPath does not exist or is not accessible. DestinationPath has to be existing directory.'
                    Invoke-Command `
                        -ScriptBlock $using:throwTerminatingError `
                        -ArgumentList 'DestinationPathDoesNotExistFailure', $errorMessage, 'InvalidData'
                }
                else
                {
                    $item = Get-Item -Path $using:DestinationPath

                    switch ($item.GetType().Name)
                    {
                        'FileInfo'
                        {
                            # DestinationPath cannot be file
                            $errorMessage = 'Invalid parameter values: DestinationPath is file, but has to be existing directory.'
                            Invoke-Command `
                                -ScriptBlock $using:throwTerminatingError `
                                -ArgumentList 'DestinationPathCannotBeFileFailure', $errorMessage, 'InvalidData'
                        }

                        'DirectoryInfo'
                        {
                            $expectedDestinationPath = Join-Path `
                                -Path $using:DestinationPath `
                                -ChildPath (Split-Path -Path $using:SourcePath -Leaf)
                        }
                    }

                    Write-Debug -Message "ExpectedDestinationPath is $expectedDestinationPath"
                }

                # Check whether ExpectedDestinationPath exists and has expected type
                $itemExists = $false

                if (-not (Test-Path $expectedDestinationPath))
                {
                    Write-Debug -Message 'Expected destination path does not exist or is not accessible.'
                }
                # If expectedDestinationPath exists
                else
                {
                    $expectedItem = Get-Item -Path $expectedDestinationPath
                    $expectedItemType = $expectedItem.GetType().Name

                    # If expectedDestinationPath has same type as sourcePathType, we need to verify cache to determine whether no upload is needed
                    if ((($expectedItemType -eq 'FileInfo') -and ($sourcePathType -eq 'File')) -or `
                        (($expectedItemType -eq 'DirectoryInfo') -and ($sourcePathType -eq 'Directory')))
                    {
                        # Get cache
                        Write-Debug -Message "Getting cache for $expectedDestinationPath"
                        $cacheContent = $null
                        $key = [System.String]::Join('', @($using:DestinationPath, $using:SourcePath, $expectedDestinationPath)).GetHashCode().ToString()
                        $path = Join-Path -Path $using:cacheLocation -ChildPath $key
                        Write-Debug -Message "Looking for cache under $path"

                        if (-not (Test-Path -Path $path))
                        {
                            Write-Debug -Message "No cache found for DestinationPath = $using:DestinationPath and SourcePath = $using:SourcePath. CacheKey = $key"
                        }
                        else
                        {
                            $cacheContent = Import-CliXml -Path $path
                            Write-Debug -Message "Found cache for DestinationPath = $using:DestinationPath and SourcePath = $using:SourcePath. CacheKey = $key"
                        }

                        # Verify whether cache reflects current state or upload is needed
                        if ($cacheContent -ne $null -and ($cacheContent.LastWriteTimeUtc -eq $expectedItem.LastWriteTimeUtc))
                        {
                            # No upload needed
                            Write-Debug -Message 'Cache reflects current state. No need for upload.'
                            $itemExists = $true
                        }
                        else
                        {
                            Write-Debug -Message 'Cache is empty or it does not reflect current state. Upload will be performed.'
                        }
                    }
                    else
                    {
                        Write-Debug -Message "Expected destination path: $expectedDestinationPath is of type $expectedItemType, although source path is $sourcePathType"
                    }
                }
            }
            finally
            {
                # Remove PSDrive
                if ($psDrive)
                {
                    Write-Debug -Message "Removing PSDrive on root $($psDrive.Root)"
                    Remove-PSDrive -Name $psDrive -Force
                }
            }

            return $itemExists
        };
    }
}

# Encrypts password using the defined public key
[System.Management.Automation.ScriptBlock] $getEncryptedPassword = {
    param
    (
        [Parameter(Mandatory = $true)]
        [PSCredential]
        $Credential,

        [Parameter(Mandatory = $true)]
        [System.String]
        $CertificateThumbprint
    )

    $value = $Credential.GetNetworkCredential().Password

    $cert = Invoke-Command `
        -ScriptBlock $getCertificate `
        -ArgumentList $CertificateThumbprint

    $encryptedPassword = $null

    if ($cert)
    {
        # Cast the public key correctly
        $rsaProvider = [System.Security.Cryptography.RSACryptoServiceProvider] $cert.PublicKey.Key

        if ($rsaProvider -eq $null)
        {
            $errorMessage = "Could not get public key from certificate with thumbprint: $CertificateThumbprint . Please verify certificate is valid for encryption."
            Invoke-Command `
                -ScriptBlock $throwTerminatingError `
                -ArgumentList "DecryptionCertificateNotFound", $errorMessage, "InvalidOperation"
        }

        # Convert to a byte array
        $keybytes = [System.Text.Encoding]::UNICODE.GetBytes($value)

        # Add a null terminator to the byte array
        $keybytes += 0
        $keybytes += 0

        # Encrypt using the public key
        $encbytes = $rsaProvider.Encrypt($keybytes, $false)

        # Return a string
        $encryptedPassword = [Convert]::ToBase64String($encbytes)
    }
    else
    {
        $errorMessage = "Could not find certificate which matches thumbprint: $CertificateThumbprint . Could not encrypt password"
        Invoke-Command `
            -ScriptBlock $throwTerminatingError `
            -ArgumentList "EncryptionCertificateNot", $errorMessage, "InvalidOperation"
    }

    return $encryptedPassword
}

# Retrieves certificate by thumbprint
[System.Management.Automation.ScriptBlock] $getCertificate = {
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $CertificateThumbprint
    )

    $cert = $null

    foreach ($certIndex in (Get-Childitem -Path Cert:\LocalMachine\My))
    {
        if ($certIndex.Thumbprint -match $CertificateThumbprint)
        {
            $cert = $certIndex
            break
        }
    }

    if (-not $cert)
    {
        $errorMessage = "Error Reading certificate store for {0}. Please verify thumbprint is correct and certificate belongs to cert:\LocalMachine\My store." -f ${CertificateThumbprint};
        Invoke-Command `
            -ScriptBlock $throwTerminatingError `
            -ArgumentList "InvalidPathSpecified", $errorMessage, "InvalidOperation"
    }
    else
    {
        $cert
    }
}

# Throws terminating error specified errorCategory, errorId and errorMessage
[System.Management.Automation.ScriptBlock] $throwTerminatingError = {
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $ErrorId,

        [Parameter(Mandatory = $true)]
        [System.String]
        $ErrorMessage,

        [Parameter(Mandatory = $true)]
        $ErrorCategory
    )

    $exception = New-Object -TypeName System.InvalidOperationException -ArgumentList $ErrorMessage
    $errorRecord = New-Object -TypeName System.Management.Automation.ErrorRecord -ArgumentList ($exception, $ErrorId, $ErrorCategory, $null)
    throw $errorRecord
}

# Decrypts password using the defined private key
[System.Management.Automation.ScriptBlock] $getDecryptedPassword = {
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $Value,

        [Parameter(Mandatory = $true)]
        [System.String]
        $CertificateThumbprint
    )

    $cert = $null

    foreach ($certIndex in (Get-Childitem -Path Cert:\LocalMachine\My))
    {
        if ($certIndex.Thumbprint -match $CertificateThumbprint)
        {
            $cert = $certIndex
            break
        }
    }

    if (-not $cert)
    {
        $errorMessage = "Error Reading certificate store for {0}. Please verify thumbprint is correct and certificate belongs to cert:\LocalMachine\My store." -f ${CertificateThumbprint};
        $exception = New-Object -TypeName System.InvalidOperationException -ArgumentList $errorMessage
        $errorRecord = New-Object -TypeName System.Management.Automation.ErrorRecord -ArgumentList ($exception, "InvalidPathSpecified", "InvalidOperation", $null)
        throw $errorRecord
    }

    $decryptedPassword = $null

    # Get RSA provider
    $rsaProvider = [System.Security.Cryptography.RSACryptoServiceProvider] $cert.PrivateKey

    if ($rsaProvider -eq $null)
    {
        $errorMessage = "Could not get private key from certificate with thumbprint: $CertificateThumbprint . Please verify certificate is valid for decryption."
        $exception = New-Object -TypeName System.InvalidOperationException -ArgumentList $errorMessage
        $errorRecord = New-Object -TypeName System.Management.Automation.ErrorRecord -ArgumentList ($exception, "DecryptionCertificateNotFound", "InvalidOperation", $null)
        throw $errorRecord
    }

    # Convert to bytes array
    $encBytes = [Convert]::FromBase64String($value)

    # Decrypt bytes
    $decryptedBytes = $rsaProvider.Decrypt($encBytes, $false)

    # Convert to string
    $decryptedPassword = [System.Text.Encoding]::Unicode.GetString($decryptedBytes)

    return $decryptedPassword
}

# SIG # Begin signature block
# MIIjYAYJKoZIhvcNAQcCoIIjUTCCI00CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCj1O1YOFaDaKAJ
# /yj0opLJ4kwZ1QbosUiWie8LY27tJqCCHVkwggUaMIIEAqADAgECAhADBbuGIbCh
# Y1+/3q4SBOdtMA0GCSqGSIb3DQEBCwUAMHIxCzAJBgNVBAYTAlVTMRUwEwYDVQQK
# EwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xMTAvBgNV
# BAMTKERpZ2lDZXJ0IFNIQTIgQXNzdXJlZCBJRCBDb2RlIFNpZ25pbmcgQ0EwHhcN
# MjAwNTEyMDAwMDAwWhcNMjMwNjA4MTIwMDAwWjBXMQswCQYDVQQGEwJVUzERMA8G
# A1UECBMIVmlyZ2luaWExDzANBgNVBAcTBlZpZW5uYTERMA8GA1UEChMIZGJhdG9v
# bHMxETAPBgNVBAMTCGRiYXRvb2xzMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
# CgKCAQEAvL9je6vjv74IAbaY5rXqHxaNeNJO9yV0ObDg+kC844Io2vrHKGD8U5hU
# iJp6rY32RVprnAFrA4jFVa6P+sho7F5iSVAO6A+QZTHQCn7oquOefGATo43NAadz
# W2OWRro3QprMPZah0QFYpej9WaQL9w/08lVaugIw7CWPsa0S/YjHPGKQ+bYgI/kr
# EUrk+asD7lvNwckR6pGieWAyf0fNmSoevQBTV6Cd8QiUfj+/qWvLW3UoEX9ucOGX
# 2D8vSJxL7JyEVWTHg447hr6q9PzGq+91CO/c9DWFvNMjf+1c5a71fEZ54h1mNom/
# XoWZYoKeWhKnVdv1xVT1eEimibPEfQIDAQABo4IBxTCCAcEwHwYDVR0jBBgwFoAU
# WsS5eyoKo6XqcQPAYPkt9mV1DlgwHQYDVR0OBBYEFPDAoPu2A4BDTvsJ193ferHL
# 454iMA4GA1UdDwEB/wQEAwIHgDATBgNVHSUEDDAKBggrBgEFBQcDAzB3BgNVHR8E
# cDBuMDWgM6Axhi9odHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc2hhMi1hc3N1cmVk
# LWNzLWcxLmNybDA1oDOgMYYvaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NoYTIt
# YXNzdXJlZC1jcy1nMS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAwEwKjAoBggr
# BgEFBQcCARYcaHR0cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBBAEw
# gYQGCCsGAQUFBwEBBHgwdjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNl
# cnQuY29tME4GCCsGAQUFBzAChkJodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20v
# RGlnaUNlcnRTSEEyQXNzdXJlZElEQ29kZVNpZ25pbmdDQS5jcnQwDAYDVR0TAQH/
# BAIwADANBgkqhkiG9w0BAQsFAAOCAQEAj835cJUMH9Y2pBKspjznNJwcYmOxeBcH
# Ji+yK0y4bm+j44OGWH4gu/QJM+WjZajvkydJKoJZH5zrHI3ykM8w8HGbYS1WZfN4
# oMwi51jKPGZPw9neGS2PXrBcKjzb7rlQ6x74Iex+gyf8z1ZuRDitLJY09FEOh0BM
# LaLh+UvJ66ghmfIyjP/g3iZZvqwgBhn+01fObqrAJ+SagxJ/21xNQJchtUOWIlxR
# kuUn9KkuDYrMO70a2ekHODcAbcuHAGI8wzw4saK1iPPhVTlFijHS+7VfIt/d/18p
# MLHHArLQQqe1Z0mTfuL4M4xCUKpebkH8rI3Fva62/6osaXLD0ymERzCCBTAwggQY
# oAMCAQICEAQJGBtf1btmdVNDtW+VUAgwDQYJKoZIhvcNAQELBQAwZTELMAkGA1UE
# BhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2lj
# ZXJ0LmNvbTEkMCIGA1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4X
# DTEzMTAyMjEyMDAwMFoXDTI4MTAyMjEyMDAwMFowcjELMAkGA1UEBhMCVVMxFTAT
# BgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEx
# MC8GA1UEAxMoRGlnaUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBD
# QTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPjTsxx/DhGvZ3cH0wsx
# SRnP0PtFmbE620T1f+Wondsy13Hqdp0FLreP+pJDwKX5idQ3Gde2qvCchqXYJawO
# eSg6funRZ9PG+yknx9N7I5TkkSOWkHeC+aGEI2YSVDNQdLEoJrskacLCUvIUZ4qJ
# RdQtoaPpiCwgla4cSocI3wz14k1gGL6qxLKucDFmM3E+rHCiq85/6XzLkqHlOzEc
# z+ryCuRXu0q16XTmK/5sy350OTYNkO/ktU6kqepqCquE86xnTrXE94zRICUj6whk
# PlKWwfIPEvTFjg/BougsUfdzvL2FsWKDc0GCB+Q4i2pzINAPZHM8np+mM6n9Gd8l
# k9ECAwEAAaOCAc0wggHJMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYDVR0PAQH/BAQD
# AgGGMBMGA1UdJQQMMAoGCCsGAQUFBwMDMHkGCCsGAQUFBwEBBG0wazAkBggrBgEF
# BQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAChjdodHRw
# Oi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0Eu
# Y3J0MIGBBgNVHR8EejB4MDqgOKA2hjRodHRwOi8vY3JsNC5kaWdpY2VydC5jb20v
# RGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsMDqgOKA2hjRodHRwOi8vY3JsMy5k
# aWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsME8GA1UdIARI
# MEYwOAYKYIZIAYb9bAACBDAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdp
# Y2VydC5jb20vQ1BTMAoGCGCGSAGG/WwDMB0GA1UdDgQWBBRaxLl7KgqjpepxA8Bg
# +S32ZXUOWDAfBgNVHSMEGDAWgBRF66Kv9JLLgjEtUYunpyGd823IDzANBgkqhkiG
# 9w0BAQsFAAOCAQEAPuwNWiSz8yLRFcgsfCUpdqgdXRwtOhrE7zBh134LYP3DPQ/E
# r4v97yrfIFU3sOH20ZJ1D1G0bqWOWuJeJIFOEKTuP3GOYw4TS63XX0R58zYUBor3
# nEZOXP+QsRsHDpEV+7qvtVHCjSSuJMbHJyqhKSgaOnEoAjwukaPAJRHinBRHoXpo
# aK+bp1wgXNlxsQyPu6j4xRJon89Ay0BEpRPw5mQMJQhCMrI2iiQC/i9yfhzXSUWW
# 6Fkd6fp0ZGuy62ZD2rOwjNXpDd32ASDOmTFjPQgaGLOBm0/GkxAG/AeB+ova+YJJ
# 92JuoVP6EpQYhS6SkepobEQysmah5xikmmRR7zCCBY0wggR1oAMCAQICEA6bGI75
# 0C3n79tQ4ghAGFowDQYJKoZIhvcNAQEMBQAwZTELMAkGA1UEBhMCVVMxFTATBgNV
# BAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEkMCIG
# A1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4XDTIyMDgwMTAwMDAw
# MFoXDTMxMTEwOTIzNTk1OVowYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGln
# aUNlcnQgVHJ1c3RlZCBSb290IEc0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAv+aQc2jeu+RdSjwwIjBpM+zCpyUuySE98orYWcLhKac9WKt2ms2uexuE
# DcQwH/MbpDgW61bGl20dq7J58soR0uRf1gU8Ug9SH8aeFaV+vp+pVxZZVXKvaJNw
# wrK6dZlqczKU0RBEEC7fgvMHhOZ0O21x4i0MG+4g1ckgHWMpLc7sXk7Ik/ghYZs0
# 6wXGXuxbGrzryc/NrDRAX7F6Zu53yEioZldXn1RYjgwrt0+nMNlW7sp7XeOtyU9e
# 5TXnMcvak17cjo+A2raRmECQecN4x7axxLVqGDgDEI3Y1DekLgV9iPWCPhCRcKtV
# gkEy19sEcypukQF8IUzUvK4bA3VdeGbZOjFEmjNAvwjXWkmkwuapoGfdpCe8oU85
# tRFYF/ckXEaPZPfBaYh2mHY9WV1CdoeJl2l6SPDgohIbZpp0yt5LHucOY67m1O+S
# kjqePdwA5EUlibaaRBkrfsCUtNJhbesz2cXfSwQAzH0clcOP9yGyshG3u3/y1Yxw
# LEFgqrFjGESVGnZifvaAsPvoZKYz0YkH4b235kOkGLimdwHhD5QMIR2yVCkliWzl
# DlJRR3S+Jqy2QXXeeqxfjT/JvNNBERJb5RBQ6zHFynIWIgnffEx1P2PsIV/EIFFr
# b7GrhotPwtZFX50g/KEexcCPorF+CiaZ9eRpL5gdLfXZqbId5RsCAwEAAaOCATow
# ggE2MA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFOzX44LScV1kTN8uZz/nupiu
# HA9PMB8GA1UdIwQYMBaAFEXroq/0ksuCMS1Ri6enIZ3zbcgPMA4GA1UdDwEB/wQE
# AwIBhjB5BggrBgEFBQcBAQRtMGswJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRp
# Z2ljZXJ0LmNvbTBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQu
# Y29tL0RpZ2lDZXJ0QXNzdXJlZElEUm9vdENBLmNydDBFBgNVHR8EPjA8MDqgOKA2
# hjRodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290
# Q0EuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQwFAAOCAQEAcKC/
# Q1xV5zhfoKN0Gz22Ftf3v1cHvZqsoYcs7IVeqRq7IviHGmlUIu2kiHdtvRoU9BNK
# ei8ttzjv9P+Aufih9/Jy3iS8UgPITtAq3votVs/59PesMHqai7Je1M/RQ0SbQyHr
# lnKhSLSZy51PpwYDE3cnRNTnf+hZqPC/Lwum6fI0POz3A8eHqNJMQBk1RmppVLC4
# oVaO7KTVPeix3P0c2PR3WlxUjG/voVA9/HYJaISfb8rbII01YBwCA8sgsKxYoA5A
# Y8WYIsGyWfVVa88nq2x2zm8jLfR+cWojayL/ErhULSd+2DrZ8LaHlv1b0VysGMNN
# n3O3AamfV6peKOK5lDCCBq4wggSWoAMCAQICEAc2N7ckVHzYR6z9KGYqXlswDQYJ
# KoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IElu
# YzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGlnaUNlcnQg
# VHJ1c3RlZCBSb290IEc0MB4XDTIyMDMyMzAwMDAwMFoXDTM3MDMyMjIzNTk1OVow
# YzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQD
# EzJEaWdpQ2VydCBUcnVzdGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGlu
# ZyBDQTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMaGNQZJs8E9cklR
# VcclA8TykTepl1Gh1tKD0Z5Mom2gsMyD+Vr2EaFEFUJfpIjzaPp985yJC3+dH54P
# Mx9QEwsmc5Zt+FeoAn39Q7SE2hHxc7Gz7iuAhIoiGN/r2j3EF3+rGSs+QtxnjupR
# PfDWVtTnKC3r07G1decfBmWNlCnT2exp39mQh0YAe9tEQYncfGpXevA3eZ9drMvo
# hGS0UvJ2R/dhgxndX7RUCyFobjchu0CsX7LeSn3O9TkSZ+8OpWNs5KbFHc02DVzV
# 5huowWR0QKfAcsW6Th+xtVhNef7Xj3OTrCw54qVI1vCwMROpVymWJy71h6aPTnYV
# VSZwmCZ/oBpHIEPjQ2OAe3VuJyWQmDo4EbP29p7mO1vsgd4iFNmCKseSv6De4z6i
# c/rnH1pslPJSlRErWHRAKKtzQ87fSqEcazjFKfPKqpZzQmiftkaznTqj1QPgv/Ci
# PMpC3BhIfxQ0z9JMq++bPf4OuGQq+nUoJEHtQr8FnGZJUlD0UfM2SU2LINIsVzV5
# K6jzRWC8I41Y99xh3pP+OcD5sjClTNfpmEpYPtMDiP6zj9NeS3YSUZPJjAw7W4oi
# qMEmCPkUEBIDfV8ju2TjY+Cm4T72wnSyPx4JduyrXUZ14mCjWAkBKAAOhFTuzuld
# yF4wEr1GnrXTdrnSDmuZDNIztM2xAgMBAAGjggFdMIIBWTASBgNVHRMBAf8ECDAG
# AQH/AgEAMB0GA1UdDgQWBBS6FtltTYUvcyl2mi91jGogj57IbzAfBgNVHSMEGDAW
# gBTs1+OC0nFdZEzfLmc/57qYrhwPTzAOBgNVHQ8BAf8EBAMCAYYwEwYDVR0lBAww
# CgYIKwYBBQUHAwgwdwYIKwYBBQUHAQEEazBpMCQGCCsGAQUFBzABhhhodHRwOi8v
# b2NzcC5kaWdpY2VydC5jb20wQQYIKwYBBQUHMAKGNWh0dHA6Ly9jYWNlcnRzLmRp
# Z2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRSb290RzQuY3J0MEMGA1UdHwQ8MDow
# OKA2oDSGMmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRS
# b290RzQuY3JsMCAGA1UdIAQZMBcwCAYGZ4EMAQQCMAsGCWCGSAGG/WwHATANBgkq
# hkiG9w0BAQsFAAOCAgEAfVmOwJO2b5ipRCIBfmbW2CFC4bAYLhBNE88wU86/GPvH
# UF3iSyn7cIoNqilp/GnBzx0H6T5gyNgL5Vxb122H+oQgJTQxZ822EpZvxFBMYh0M
# CIKoFr2pVs8Vc40BIiXOlWk/R3f7cnQU1/+rT4osequFzUNf7WC2qk+RZp4snuCK
# rOX9jLxkJodskr2dfNBwCnzvqLx1T7pa96kQsl3p/yhUifDVinF2ZdrM8HKjI/rA
# J4JErpknG6skHibBt94q6/aesXmZgaNWhqsKRcnfxI2g55j7+6adcq/Ex8HBanHZ
# xhOACcS2n82HhyS7T6NJuXdmkfFynOlLAlKnN36TU6w7HQhJD5TNOXrd/yVjmScs
# PT9rp/Fmw0HNT7ZAmyEhQNC3EyTN3B14OuSereU0cZLXJmvkOHOrpgFPvT87eK1M
# rfvElXvtCl8zOYdBeHo46Zzh3SP9HSjTx/no8Zhf+yvYfvJGnXUsHicsJttvFXse
# GYs2uJPU5vIXmVnKcPA3v5gA3yAWTyf7YGcWoWa63VXAOimGsJigK+2VQbc61RWY
# MbRiCQ8KvYHZE/6/pNHzV9m8BPqC3jLfBInwAM1dwvnQI38AC+R2AibZ8GV2QqYp
# hwlHK+Z/GqSFD/yYlvZVVCsfgPrA8g4r5db7qS9EFUrnEw4d2zc4GqEr9u3WfPww
# ggbAMIIEqKADAgECAhAMTWlyS5T6PCpKPSkHgD1aMA0GCSqGSIb3DQEBCwUAMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwHhcNMjIwOTIxMDAwMDAwWhcNMzMxMTIxMjM1OTU5WjBGMQswCQYDVQQGEwJV
# UzERMA8GA1UEChMIRGlnaUNlcnQxJDAiBgNVBAMTG0RpZ2lDZXJ0IFRpbWVzdGFt
# cCAyMDIyIC0gMjCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAM/spSY6
# xqnya7uNwQ2a26HoFIV0MxomrNAcVR4eNm28klUMYfSdCXc9FZYIL2tkpP0GgxbX
# kZI4HDEClvtysZc6Va8z7GGK6aYo25BjXL2JU+A6LYyHQq4mpOS7eHi5ehbhVsbA
# umRTuyoW51BIu4hpDIjG8b7gL307scpTjUCDHufLckkoHkyAHoVW54Xt8mG8qjoH
# ffarbuVm3eJc9S/tjdRNlYRo44DLannR0hCRRinrPibytIzNTLlmyLuqUDgN5YyU
# XRlav/V7QG5vFqianJVHhoV5PgxeZowaCiS+nKrSnLb3T254xCg/oxwPUAY3ugjZ
# Naa1Htp4WB056PhMkRCWfk3h3cKtpX74LRsf7CtGGKMZ9jn39cFPcS6JAxGiS7uY
# v/pP5Hs27wZE5FX/NurlfDHn88JSxOYWe1p+pSVz28BqmSEtY+VZ9U0vkB8nt9Kr
# FOU4ZodRCGv7U0M50GT6Vs/g9ArmFG1keLuY/ZTDcyHzL8IuINeBrNPxB9Thvdld
# S24xlCmL5kGkZZTAWOXlLimQprdhZPrZIGwYUWC6poEPCSVT8b876asHDmoHOWIZ
# ydaFfxPZjXnPYsXs4Xu5zGcTB5rBeO3GiMiwbjJ5xwtZg43G7vUsfHuOy2SJ8bHE
# uOdTXl9V0n0ZKVkDTvpd6kVzHIR+187i1Dp3AgMBAAGjggGLMIIBhzAOBgNVHQ8B
# Af8EBAMCB4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAg
# BgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZ
# bU2FL3MpdpovdYxqII+eyG8wHQYDVR0OBBYEFGKK3tBh/I8xFO2XC809KpQU31Kc
# MFoGA1UdHwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdp
# Q2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAG
# CCsGAQUFBwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2Vy
# dC5jb20wWAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9E
# aWdpQ2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQw
# DQYJKoZIhvcNAQELBQADggIBAFWqKhrzRvN4Vzcw/HXjT9aFI/H8+ZU5myXm93KK
# mMN31GT8Ffs2wklRLHiIY1UJRjkA/GnUypsp+6M/wMkAmxMdsJiJ3HjyzXyFzVOd
# r2LiYWajFCpFh0qYQitQ/Bu1nggwCfrkLdcJiXn5CeaIzn0buGqim8FTYAnoo7id
# 160fHLjsmEHw9g6A++T/350Qp+sAul9Kjxo6UrTqvwlJFTU2WZoPVNKyG39+Xgmt
# dlSKdG3K0gVnK3br/5iyJpU4GYhEFOUKWaJr5yI+RCHSPxzAm+18SLLYkgyRTzxm
# lK9dAlPrnuKe5NMfhgFknADC6Vp0dQ094XmIvxwBl8kZI4DXNlpflhaxYwzGRkA7
# zl011Fk+Q5oYrsPJy8P7mxNfarXH4PMFw1nfJ2Ir3kHJU7n/NBBn9iYymHv+XEKU
# gZSCnawKi8ZLFUrTmJBFYDOA4CPe+AOk9kVH5c64A0JH6EE2cXet/aLol3ROLtoe
# HYxayB6a1cLwxiKoT5u92ByaUcQvmvZfpyeXupYuhVfAYOd4Vn9q78KVmksRAsiC
# nMkaBXy6cbVOepls9Oie1FqYyJ+/jbsYXEP10Cro4mLueATbvdH7WwqocH7wl4R4
# 4wgDXUcsY6glOJcB0j862uXl9uab3H4szP8XTE0AotjWAQ64i+7m4HJViSwnGWH2
# dwGMMYIFXTCCBVkCAQEwgYYwcjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTExMC8GA1UEAxMoRGln
# aUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBDQQIQAwW7hiGwoWNf
# v96uEgTnbTANBglghkgBZQMEAgEFAKCBhDAYBgorBgEEAYI3AgEMMQowCKACgACh
# AoAAMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAM
# BgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCDk4FseKQf0Y7BkITfSlVvVClLu
# 6kzH/VfRh1mDNR6EhjANBgkqhkiG9w0BAQEFAASCAQBsWUtNqwdRIKEPfJLQQCU6
# hdNc8H6XdIdUHUqdTlCfk6igVRK4X3CfJAt902Ldx2dWdkmBduNOEuh/74CDpmjs
# z6JfAypLrZSg0f8lSyhbnAXY8c+Tbs458EI66ODBX65Ap80Jmp9hcSTxsmc2BrTf
# MA9ZI1gjHSsATtgB3GVFEGc33rqi22qIpyohbQiIo0wKeQy6/KKIcarxIKAlR6vs
# VbfeTsSL++qMmndI7BD83osHzvbN7pVnPMZ+D1jEIP5oeFQg5UKvzLTv4nNhlNJ2
# c8AnHuLQQtT+ppaJdow9xCY5kGv0MwWfF68/bSWMnt9wgoySTpHO0clXhgRgaPpw
# oYIDIDCCAxwGCSqGSIb3DQEJBjGCAw0wggMJAgEBMHcwYzELMAkGA1UEBhMCVVMx
# FzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQDEzJEaWdpQ2VydCBUcnVz
# dGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGluZyBDQQIQDE1pckuU+jwq
# Sj0pB4A9WjANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0B
# BwEwHAYJKoZIhvcNAQkFMQ8XDTIzMDUxMjAwMjUyOFowLwYJKoZIhvcNAQkEMSIE
# IJe88Wk1enHSa58S9GsrRDWj57hrKh+lYiksNUU/PXjBMA0GCSqGSIb3DQEBAQUA
# BIICAFLrR1yooBptnvK52yBSeYlWm53vwiw+fitiTlZBS2nWYCSsEyenfJBdiJG0
# OMKNugWLuff5utxWzU11BTPRHpPIkjtq1QVAEaHP9BTkRLk7l7wXTnHJO/Y81zcY
# 0ExlFzsLLmqMEBGRQXHkgdCYxrSRq/LkgS2O+QOnWxJSdy7A2fY/80RrmVxz80w6
# GzJA5jaUZg47krZrpVtoqc8PDmIXZKOGk33SQZ8fJ0R6vpGziUvGvAR9C/PNNfan
# GtTPpOgYPfsDO5LSfEtANIEAn5QV+m5OL8tRaz5N+9PmfTuVO4KfcyvEConfEQTL
# laKZZBgZNBQzeOc0lnBg394mYQn/kgT8dxB8q66fx2JGaOW+CwWD3KL9IweC9XYc
# Kyef8tPtZeWqozImb36M95l4WXJ9cR4yntsTErttiW7J0IlCFbxeXCw9qu7U0lFt
# gEuvnha4BeH6PGOgfbemSp+xHxfxa6HUVvxyx3MmA6/CRfmide9leG7JmvlGB4+T
# r1YFO0NrKN2CzKt/UyB66gMcWQa29y5Q27Hw8YYnBYgPW5w9mxft1+mytHxSW0Nq
# m2XYt6PlW/13keu2ifLVvDQP0/I8lr04A+vpRTfARMAz/WNPxx/ZlYm4vb8RhjCr
# TJKnEDLCHLr8icVAhkBVo8DYvoy6OAaUuTMFKBY0fLSzT6GQ
# SIG # End signature block
