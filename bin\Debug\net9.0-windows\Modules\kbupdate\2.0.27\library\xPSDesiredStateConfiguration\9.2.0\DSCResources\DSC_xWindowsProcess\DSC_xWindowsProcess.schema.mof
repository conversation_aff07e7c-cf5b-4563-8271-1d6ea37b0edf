[ClassVersion("1.0.0"), FriendlyName("xWindowsProcess")]
class DSC_xWindowsProcess : OMI_BaseResource
{
  [Key, Description("The full path or file name to the process executable to start or stop.")] String Path;
  [Key, Description("A string of arguments to pass to the process executable. Pass in an empty string if no arguments are needed.")] String Arguments;
  [Write, EmbeddedInstance("MSFT_Credential"), Description("The credential to run the process under.")] String Credential;
  [Write, ValueMap{"Present", "Absent"}, Values{"Present", "Absent"}, Description("Indicates whether the process is present (running) or absent (not running).")] String Ensure;
  [Write, Description("The path to write the standard output stream to.")] String StandardOutputPath;
  [Write, Description("The path to write the standard error stream to.")] String StandardErrorPath;
  [Write, Description("The path to receive standard input from.")] String StandardInputPath;
  [Write, Description("The directory to run the processes under.")] String WorkingDirectory;
  [Read, Description("The amount of paged memory, in bytes, allocated for the process.")] UInt64 PagedMemorySize;
  [Read, Description("The amount of nonpaged memory, in bytes, allocated for the process.")] UInt64 NonPagedMemorySize;
  [Read, Description("The amount of virtual memory, in bytes, allocated for the process.")] UInt64 VirtualMemorySize;
  [Read, Description("The number of handles opened by the process.")] SInt32 HandleCount;
  [Read, Description("The unique identifier of the process.")] SInt32 ProcessId;
  [Read, Description("The number of instances of the given process that are currently running.")] SInt32 ProcessCount;
};
