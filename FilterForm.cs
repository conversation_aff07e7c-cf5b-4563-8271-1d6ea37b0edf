using System.ComponentModel;

namespace WindowsUpdateTool
{
    public partial class FilterForm : Form
    {
        public UpdateFilter Filter { get; private set; } = new();

        public FilterForm()
        {
            InitializeComponent();
            LoadArchitectureOptions();
        }

        private void InitializeComponent()
        {
            this.Text = "Add/Edit Filter";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Filter Name
            var lblName = new Label
            {
                Text = "Filter Name:",
                Location = new Point(20, 20),
                AutoSize = true
            };

            var txtName = new TextBox
            {
                Name = "txtName",
                Location = new Point(150, 20),
                Size = new Size(300, 25)
            };

            // Title Contains
            var lblTitleContains = new Label
            {
                Text = "Title Contains:",
                Location = new Point(20, 60),
                AutoSize = true
            };

            var txtTitleContains = new TextBox
            {
                Name = "txtTitleContains",
                Location = new Point(150, 60),
                Size = new Size(300, 25)
            };

            // Title Excludes
            var lblTitleExcludes = new Label
            {
                Text = "Title Excludes:",
                Location = new Point(20, 100),
                AutoSize = true
            };

            var txtTitleExcludes = new TextBox
            {
                Name = "txtTitleExcludes",
                Location = new Point(150, 100),
                Size = new Size(300, 25)
            };

            // Build Contains
            var lblBuildContains = new Label
            {
                Text = "Build Contains:",
                Location = new Point(20, 140),
                AutoSize = true
            };

            var txtBuildContains = new TextBox
            {
                Name = "txtBuildContains",
                Location = new Point(150, 140),
                Size = new Size(300, 25)
            };

            // Build Excludes
            var lblBuildExcludes = new Label
            {
                Text = "Build Excludes:",
                Location = new Point(20, 180),
                AutoSize = true
            };

            var txtBuildExcludes = new TextBox
            {
                Name = "txtBuildExcludes",
                Location = new Point(150, 180),
                Size = new Size(300, 25)
            };

            // Architecture
            var lblArchitecture = new Label
            {
                Text = "Architecture:",
                Location = new Point(20, 220),
                AutoSize = true
            };

            var cmbArchitecture = new ComboBox
            {
                Name = "cmbArchitecture",
                Location = new Point(150, 220),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Buttons
            var btnOK = new Button
            {
                Text = "OK",
                DialogResult = DialogResult.OK,
                Location = new Point(300, 320),
                Size = new Size(75, 25)
            };
            btnOK.Click += BtnOK_Click;

            var btnCancel = new Button
            {
                Text = "Cancel",
                DialogResult = DialogResult.Cancel,
                Location = new Point(385, 320),
                Size = new Size(75, 25)
            };

            // Help text
            var lblHelp = new Label
            {
                Text = "Leave fields empty to ignore that filter. Multiple filters work as AND conditions.",
                Location = new Point(20, 270),
                Size = new Size(440, 40),
                Font = new Font(this.Font.FontFamily, 8)
            };

            // Add controls
            this.Controls.AddRange(new Control[]
            {
                lblName, txtName,
                lblTitleContains, txtTitleContains,
                lblTitleExcludes, txtTitleExcludes,
                lblBuildContains, txtBuildContains,
                lblBuildExcludes, txtBuildExcludes,
                lblArchitecture, cmbArchitecture,
                btnOK, btnCancel, lblHelp
            });

            // Store references for later use
            this.Controls.Add(new Label { Name = "txtName", Visible = false });
            this.Controls.Add(new Label { Name = "txtTitleContains", Visible = false });
            this.Controls.Add(new Label { Name = "txtTitleExcludes", Visible = false });
            this.Controls.Add(new Label { Name = "txtBuildContains", Visible = false });
            this.Controls.Add(new Label { Name = "txtBuildExcludes", Visible = false });
            this.Controls.Add(new Label { Name = "cmbArchitecture", Visible = false });

            // Replace with actual controls
            this.Controls.Remove(this.Controls["txtName"]);
            this.Controls.Remove(this.Controls["txtTitleContains"]);
            this.Controls.Remove(this.Controls["txtTitleExcludes"]);
            this.Controls.Remove(this.Controls["txtBuildContains"]);
            this.Controls.Remove(this.Controls["txtBuildExcludes"]);
            this.Controls.Remove(this.Controls["cmbArchitecture"]);

            this.Controls.Add(txtName);
            this.Controls.Add(txtTitleContains);
            this.Controls.Add(txtTitleExcludes);
            this.Controls.Add(txtBuildContains);
            this.Controls.Add(txtBuildExcludes);
            this.Controls.Add(cmbArchitecture);
        }

        private void LoadArchitectureOptions()
        {
            var cmbArchitecture = this.Controls.OfType<ComboBox>().FirstOrDefault(c => c.Name == "cmbArchitecture");
            if (cmbArchitecture != null)
            {
                cmbArchitecture.Items.AddRange(new object[]
                {
                    "",
                    "amd64",
                    "arm64",
                    "x86"
                });
                cmbArchitecture.SelectedIndex = 0;
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // Validate and collect filter data
            var txtName = this.Controls.OfType<TextBox>().FirstOrDefault(t => t.Name == "txtName");
            var txtTitleContains = this.Controls.OfType<TextBox>().FirstOrDefault(t => t.Name == "txtTitleContains");
            var txtTitleExcludes = this.Controls.OfType<TextBox>().FirstOrDefault(t => t.Name == "txtTitleExcludes");
            var txtBuildContains = this.Controls.OfType<TextBox>().FirstOrDefault(t => t.Name == "txtBuildContains");
            var txtBuildExcludes = this.Controls.OfType<TextBox>().FirstOrDefault(t => t.Name == "txtBuildExcludes");
            var cmbArchitecture = this.Controls.OfType<ComboBox>().FirstOrDefault(c => c.Name == "cmbArchitecture");

            if (txtName?.Text.Trim().Length == 0)
            {
                MessageBox.Show("Please enter a filter name.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            Filter = new UpdateFilter
            {
                Name = txtName?.Text.Trim() ?? "",
                TitleContains = txtTitleContains?.Text.Trim() ?? "",
                TitleExcludes = txtTitleExcludes?.Text.Trim() ?? "",
                BuildContains = txtBuildContains?.Text.Trim() ?? "",
                BuildExcludes = txtBuildExcludes?.Text.Trim() ?? "",
                Architecture = cmbArchitecture?.Text.Trim() ?? ""
            };
        }
    }
} 