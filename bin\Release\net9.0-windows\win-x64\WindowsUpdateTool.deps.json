{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"WindowsUpdateTool/1.0.0": {"dependencies": {"Microsoft.NET.ILLink.Tasks": "9.0.7", "Newtonsoft.Json": "13.0.3", "System.IO.Compression": "4.3.0", "System.Management": "8.0.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.7", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "9.0.7"}, "runtime": {"WindowsUpdateTool.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.7": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.725.31616"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "9.0.725.31616"}, "clretwrc.dll": {"fileVersion": "9.0.725.31616"}, "clrgc.dll": {"fileVersion": "9.0.725.31616"}, "clrgcexp.dll": {"fileVersion": "9.0.725.31616"}, "clrjit.dll": {"fileVersion": "9.0.725.31616"}, "coreclr.dll": {"fileVersion": "9.0.725.31616"}, "createdump.exe": {"fileVersion": "9.0.725.31616"}, "hostfxr.dll": {"fileVersion": "9.0.725.31616"}, "hostpolicy.dll": {"fileVersion": "9.0.725.31616"}, "mscordaccore.dll": {"fileVersion": "9.0.725.31616"}, "mscordaccore_amd64_amd64_9.0.725.31616.dll": {"fileVersion": "9.0.725.31616"}, "mscordbi.dll": {"fileVersion": "9.0.725.31616"}, "mscorrc.dll": {"fileVersion": "9.0.725.31616"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.7": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "9.0.725.31604"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Formats.Nrbf.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31604"}}}, "Microsoft.NET.ILLink.Tasks/9.0.7": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Runtime.Handles/4.3.0": {}, "runtime.any.System.Runtime.InteropServices/4.3.0": {}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.CodeDom/8.0.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tracing": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.Management/8.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.any.System.Runtime.InteropServices": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}}}, "libraries": {"WindowsUpdateTool/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.7": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.7": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.NET.ILLink.Tasks/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-SZ1brSGoLnhLbE8QUZrtN6YwzN2gDT1wbx9qDBEfFFJcstiDTjJ6ygNuTPBV/K7SjGfx2YNbcJi5+ygbPOZpDg==", "path": "microsoft.net.illink.tasks/9.0.7", "hashPath": "microsoft.net.illink.tasks.9.0.7.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1lpifymjGDzoYIaam6/Hyqf8GhBI3xXYLK2TgEvTtuZMorG3Kb9QnMTIKhLjJYXIiu1JvxjngHvtVFQQlpQ3HQ==", "path": "runtime.any.system.diagnostics.tracing/4.3.0", "hashPath": "runtime.any.system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GG84X6vufoEzqx8PbeBKheE4srOhimv+yLtGb/JkR3Y2FmoqmueLNFU4Xx8Y67plFpltQSdK74x0qlEhIpv/CQ==", "path": "runtime.any.system.runtime.handles/4.3.0", "hashPath": "runtime.any.system.runtime.handles.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lBoFeQfxe/4eqjPi46E0LU/YaCMdNkQ8B4MZu/mkzdIAZh8RQ1NYZSj0egrQKdgdvlPFtP4STtob40r4o2DBAw==", "path": "runtime.any.system.runtime.interopservices/4.3.0", "hashPath": "runtime.any.system.runtime.interopservices.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "path": "runtime.win.system.diagnostics.debug/4.3.0", "hashPath": "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "path": "runtime.win.system.runtime.extensions/4.3.0", "hashPath": "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.Management/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "path": "system.management/8.0.0", "hashPath": "system.management.8.0.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}