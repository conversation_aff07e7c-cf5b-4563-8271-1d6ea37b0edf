$errorActionPreference = 'Stop'
Set-StrictMode -Version 'Latest'

$modulePath = Join-Path -Path (Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent) -ChildPath 'Modules'

# Import the shared modules
Import-Module -Name (Join-Path -Path $modulePath `
    -ChildPath (Join-Path -Path 'xPSDesiredStateConfiguration.Common' `
        -ChildPath 'xPSDesiredStateConfiguration.Common.psm1'))

Import-Module -Name (Join-Path -Path $modulePath -ChildPath 'DscResource.Common')

# Import Localization Strings
$script:localizedData = Get-LocalizedData -DefaultUICulture 'en-US'

$script:envVarRegPathMachine = 'HKLM:\System\CurrentControlSet\Control\Session Manager\Environment'
$script:envVarRegPathUser = 'HKCU:\Environment'

$script:maxSystemEnvVariableLength = 1024
$script:maxUserEnvVariableLength = 255

<#
    .SYNOPSIS
        Retrieves the state of the environment variable. If both Machine and Process Target are
        specified, only the machine value will be returned.

    .PARAMETER Name
        he name of the environment variable for which you want to ensure a specific state.

    .PARAMETER Target
        Indicates the target where the environment variable should be set.
#>
function Get-TargetResource
{
    [CmdletBinding()]
    [OutputType([System.Collections.Hashtable])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [ValidateSet('Process', 'Machine')]
        [ValidateNotNullOrEmpty()]
        [System.String[]]
        $Target = ('Process', 'Machine')
    )

    $valueToReturn = $null

    if ($Target -contains 'Machine')
    {
        $environmentVariable = Get-EnvironmentVariableWithoutExpanding -Name $Name -ErrorAction 'SilentlyContinue'

        if ($null -ne $environmentVariable)
        {
            $valueToReturn = $environmentVariable.$Name
        }
    }
    else
    {
        $valueToReturn = Get-ProcessEnvironmentVariable -Name $Name
    }

    $environmentResource = @{
        Name = $Name
        Value = $null
        Ensure = 'Absent'
    }

    if ($null -eq $valueToReturn)
    {
        Write-Verbose -Message ($script:localizedData.EnvVarNotFound -f $Name)
    }
    else
    {
        Write-Verbose -Message ($script:localizedData.EnvVarFound -f $Name, $valueToReturn)
        $environmentResource.Ensure = 'Present'
        $environmentResource.Value = $valueToReturn
    }

    return $environmentResource
}

<#
    .SYNOPSIS
        Creates, modifies, or removes an environment variable.

    .PARAMETER Name
        he name of the environment variable for which you want to ensure a specific state.

    .PARAMETER Value
        The desired value for the environment variable. The default value is an empty string
        which either indicates that the variable should be removed entirely or that the value
        does not matter when testing its existence. Multiple entries can be entered and
        separated by semicolons.

    .PARAMETER Ensure
        Specifies whether the variable should exist or not.

    .PARAMETER Path
        Indicates whether or not the environment variable is a path variable. If the variable
        being configured is a path variable, the value provided will be appended to or removed
        from the existing value, otherwise the existing value will be replaced by the new value.
        When configured as a Path variable, multiple entries separated by semicolons are ensured
        to be either present or absent without affecting other Path entries.

    .PARAMETER Target
        Indicates the target where the environment variable should be set.
#>
function Set-TargetResource
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [ValidateNotNull()]
        [System.String]
        $Value = [System.String]::Empty,

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present',

        [Parameter()]
        [System.Boolean]
        $Path = $false,

        [Parameter()]
        [ValidateSet('Process', 'Machine')]
        [ValidateNotNullOrEmpty()]
        [System.String[]]
        $Target = ('Process', 'Machine')
    )

    $valueSpecified = ($Value -ne [System.String]::Empty)
    $currentValueFromMachine = $null
    $currentValueFromProcess = $null
    $currentPropertiesFromMachine = $null

    $setMachineVariable = ($Target -contains 'Machine')
    $setProcessVariable = ($Target -contains 'Process')

    if ($setMachineVariable)
    {
        if ($Path)
        {
            $currentPropertiesFromMachine = Get-EnvironmentVariableWithoutExpanding -Name $Name -ErrorAction 'SilentlyContinue'

            if ($null -ne $currentPropertiesFromMachine)
            {
                $currentValueFromMachine = $currentPropertiesFromMachine.$Name
            }
        }
        else
        {
            $currentPropertiesFromMachine = Get-ItemProperty -Path $script:envVarRegPathMachine -Name $Name -ErrorAction 'SilentlyContinue'
            $currentValueFromMachine = Get-EnvironmentVariable -Name $Name -Target 'Machine'
        }
    }

    if ($setProcessVariable)
    {
        $currentValueFromProcess = Get-EnvironmentVariable -Name $Name -Target 'Process'
    }

    # A different value of the environment variable needs to be displayed depending on the Target
    $currentValueToDisplay = ''
    if ($setMachineVariable -and $setProcessVariable)
    {
        $currentValueToDisplay = "Machine: $currentValueFromMachine, Process: $currentValueFromProcess"
    }
    elseif ($setMachineVariable)
    {
        $currentValueToDisplay = $currentValueFromMachine
    }
    else
    {
        $currentValueToDisplay = $currentValueFromProcess
    }

    if ($Ensure -eq 'Present')
    {
        $createMachineVariable = ((-not $setMachineVariable) -or ($null -eq $currentPropertiesFromMachine) -or ($currentValueFromMachine -eq [System.String]::Empty))
        $createProcessVariable = ((-not $setProcessVariable) -or ($null -eq $currentValueFromProcess) -or ($currentValueFromProcess -eq [System.String]::Empty))

        if ($createMachineVariable -and $createProcessVariable)
        {
            if (-not $valueSpecified)
            {
                <#
                    If the environment variable doesn't exist and no value is passed in
                    then there is nothing to set - so throw an error.
                #>

                New-InvalidOperationException -Message ($script:localizedData.CannotSetValueToEmpty -f $Name)
            }

            <#
                Given the specified $Name environment variable hasn't been created or set
                simply create one with the specified value and return.
                Both path and non-path cases are covered by this.
            #>

            Set-EnvironmentVariable -Name $Name -Value $Value -Target $Target

            Write-Verbose -Message ($script:localizedData.EnvVarCreated -f $Name, $Value)
            return
        }

        if (-not $valueSpecified)
        {
            <#
                Given no $Value was specified to be set and the variable exists,
                we'll leave the existing variable as is.
                This covers both path and non-path variables.
            #>

            Write-Verbose -Message ($script:localizedData.EnvVarUnchanged -f $Name, $currentValueToDisplay)
            return
        }

        # Check if an empty, whitespace or semi-colon only string has been specified. If yes, return unchanged.
        $trimmedValue = $Value.Trim(';').Trim()

        if ([System.String]::IsNullOrEmpty($trimmedValue))
        {
            Write-Verbose -Message ($script:localizedData.EnvVarPathUnchanged -f $Name, $currentValueToDisplay)
            return
        }

        if (-not $Path)
        {
            # For non-path variables, simply set the specified $Value as the new value of the specified
            # variable $Name for the given $Target

            if (($setMachineVariable -and ($Value -cne $currentValueFromMachine)) -or `
                ($setProcessVariable -and ($Value -cne $currentValueFromProcess)))
            {
                Set-EnvironmentVariable -Name $Name -Value $Value -Target $Target
                Write-Verbose -Message ($script:localizedData.EnvVarUpdated -f $Name, $currentValueToDisplay, $Value)
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.EnvVarUnchanged -f $Name, $currentValueToDisplay)
            }

            return
        }

        # If the control reaches here, the specified variable exists, it is a path variable, and a value has been specified to be set.

        if ($setMachineVariable)
        {
            $valueUnchanged = Test-PathsInValue -ExistingPaths $currentValueFromMachine -QueryPaths $trimmedValue -FindCriteria 'All'

            if ($currentValueFromMachine -and -not $valueUnchanged)
            {
                $updatedValue = Add-PathsToValue -CurrentValue $currentValueFromMachine -NewValue $trimmedValue
                Set-EnvironmentVariable -Name $Name -Value $updatedValue -Target @('Machine')
                Write-Verbose -Message ($script:localizedData.EnvVarPathUpdated -f $Name, $currentValueFromMachine, $updatedValue)
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.EnvVarPathUnchanged -f $Name, $currentValueFromMachine)
            }
        }

        if ($setProcessVariable)
        {
            $valueUnchanged = Test-PathsInValue -ExistingPaths $currentValueFromProcess -QueryPaths $trimmedValue -FindCriteria 'All'

            if ($currentValueFromProcess -and -not $valueUnchanged)
            {
                $updatedValue = Add-PathsToValue -CurrentValue $currentValueFromProcess -NewValue $trimmedValue
                Set-EnvironmentVariable -Name $Name -Value $updatedValue -Target @('Process')
                Write-Verbose -Message ($script:localizedData.EnvVarPathUpdated -f $Name, $currentValueFromProcess, $updatedValue)
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.EnvVarPathUnchanged -f $Name, $currentValueFromProcess)
            }
        }
    }

    # Ensure = 'Absent'
    else
    {
        $machineVariableRemoved = ((-not $setMachineVariable) -or ($null -eq $currentPropertiesFromMachine))
        $processVariableRemoved = ((-not $setProcessVariable) -or ($null -eq $currentValueFromProcess))

        if ($machineVariableRemoved -and $processVariableRemoved)
        {
            # Variable not found, condition is satisfied and there is nothing to set/remove, return
            Write-Verbose -Message ($script:localizedData.EnvVarNotFound -f $Name)
            return
        }

        if ((-not $ValueSpecified) -or (-not $Path))
        {
            <#
                If $Value is not specified or if $Value is a non-path variable,
                simply remove the environment variable.
            #>

            Remove-EnvironmentVariable -Name $Name -Target $Target

            Write-Verbose -Message ($script:localizedData.EnvVarRemoved -f $Name)
            return
        }

        # Check if an empty string or semi-colon only string has been specified as $Value. If yes, return unchanged as we don't need to remove anything.
        $trimmedValue = $Value.Trim(';').Trim()

        if ([System.String]::IsNullOrEmpty($trimmedValue))
        {
            Write-Verbose -Message ($script:localizedData.EnvVarPathUnchanged -f $Name, $currentValueToDisplay)
            return
        }

        # If the control reaches here: target variable is an existing environment path-variable and a specified $Value needs be removed from it

        if ($setMachineVariable)
        {
            $finalPath = $null

            if ($currentValueFromMachine)
            {
                <#
                    If this value returns $null or an empty string, than the entire path should be removed.
                    If it returns the same value as the path that was passed in, than nothing needs to be
                    updated, otherwise, only the specified paths were removed but there are still others
                    that need to be left in, so the path variable is updated to remove only the specified paths.
                #>
                $finalPath = Remove-PathsFromValue -CurrentValue $currentValueFromMachine -PathsToRemove $trimmedValue
            }

            if ([System.String]::IsNullOrEmpty($finalPath))
            {
                Remove-EnvironmentVariable -Name $Name -Target @('Machine')
                Write-Verbose -Message ($script:localizedData.EnvVarRemoved -f $Name)
            }
            elseif ($finalPath -ceq $currentValueFromMachine)
            {
                Write-Verbose -Message ($script:localizedData.EnvVarPathUnchanged -f $Name, $currentValueFromMachine)
            }
            else
            {
                Set-EnvironmentVariable -Name $Name -Value $finalPath -Target @('Machine')
                Write-Verbose -Message ($script:localizedData.EnvVarPathUpdated -f $Name, $currentValueFromMachine, $finalPath)
            }
        }

        if ($setProcessVariable)
        {
            $finalPath = $null

            if ($currentValueFromProcess)
            {
                <#
                    If this value returns $null or an empty string, than the entire path should be removed.
                    If it returns the same value as the path that was passed in, than nothing needs to be
                    updated, otherwise, only the specified paths were removed but there are still others
                    that need to be left in, so the path variable is updated to remove only the specified paths.
                #>
                $finalPath = Remove-PathsFromValue -CurrentValue $currentValueFromProcess -PathsToRemove $trimmedValue
            }

            if ([System.String]::IsNullOrEmpty($finalPath))
            {
                Remove-EnvironmentVariable -Name $Name -Target @('Process')
                Write-Verbose -Message ($script:localizedData.EnvVarRemoved -f $Name)
            }
            elseif ($finalPath -ceq $currentValueFromProcess)
            {
                Write-Verbose -Message ($script:localizedData.EnvVarPathUnchanged -f $Name, $currentValueFromProcess)
            }
            else
            {
                Set-EnvironmentVariable -Name $Name -Value $finalPath -Target @('Process')
                Write-Verbose -Message ($script:localizedData.EnvVarPathUpdated -f $Name, $currentValueFromProcess, $finalPath)
            }
        }
    }
}

<#
    .SYNOPSIS
        Tests if the environment variable is in the desired state.

    .PARAMETER Name
        he name of the environment variable for which you want to ensure a specific state.

    .PARAMETER Value
        The desired value for the environment variable. The default value is an empty string
        which either indicates that the variable should be removed entirely or that the value
        does not matter when testing its existence. Multiple entries can be entered and
        separated by semicolons.

    .PARAMETER Ensure
        Specifies whether the variable should exist or not.

    .PARAMETER Path
        Indicates whether or not the environment variable is a path variable. If the variable
        being configured is a path variable, the value provided will be appended to or removed
        from the existing value, otherwise the existing value will be replaced by the new value.
        When configured as a Path variable, multiple entries separated by semicolons are ensured
        to be either present or absent without affecting other Path entries.

    .PARAMETER Target
        Indicates the target where the environment variable should be set.
#>
function Test-TargetResource
{
    [CmdletBinding()]
    [OutputType([System.Boolean])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [ValidateNotNull()]
        [System.String]
        $Value,

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present',

        [Parameter()]
        [System.Boolean]
        $Path = $false,

        [Parameter()]
        [ValidateSet('Process', 'Machine')]
        [ValidateNotNullOrEmpty()]
        [System.String[]]
        $Target = ('Process', 'Machine')
    )

    $valueSpecified = $PSBoundParameters.ContainsKey('Value') -and ($Value -ne [System.String]::Empty)
    $currentValueFromMachine = $null
    $currentValueFromProcess = $null
    $currentPropertiesFromMachine = $null

    $checkMachineTarget = ($Target -contains 'Machine')
    $checkProcessTarget = ($Target -contains 'Process')

    if ($checkMachineTarget)
    {
        if ($Path)
        {
            $currentPropertiesFromMachine = Get-EnvironmentVariableWithoutExpanding -Name $Name -ErrorAction 'SilentlyContinue'

            if ($null -ne $currentPropertiesFromMachine)
            {
                $currentValueFromMachine = $currentPropertiesFromMachine.$Name
            }
        }
        else
        {
            $currentPropertiesFromMachine = Get-ItemProperty -Path $script:envVarRegPathMachine -Name $Name -ErrorAction 'SilentlyContinue'
            $currentValueFromMachine = Get-EnvironmentVariable -Name $Name -Target 'Machine'
        }
    }

    if ($checkProcessTarget)
    {
        $currentValueFromProcess = Get-EnvironmentVariable -Name $Name -Target 'Process'
    }

    # A different value of the environment variable needs to be displayed depending on the Target
    $currentValueToDisplay = ''
    if ($checkMachineTarget -and $checkProcessTarget)
    {
        $currentValueToDisplay = "Machine: $currentValueFromMachine, Process: $currentValueFromProcess"
    }
    elseif ($checkMachineTarget)
    {
        $currentValueToDisplay = $currentValueFromMachine
    }
    else
    {
        $currentValueToDisplay = $currentValueFromProcess
    }

    if (($checkMachineTarget -and ($null -eq $currentPropertiesFromMachine)) -or ($checkProcessTarget -and ($null -eq $currentValueFromProcess)))
    {
        # Variable not found
        Write-Verbose ($script:localizedData.EnvVarNotFound -f $Name)
        return ($Ensure -eq 'Absent')
    }

    if (-not $valueSpecified)
    {
        Write-Verbose ($script:localizedData.EnvVarFound -f $Name, $currentValueToDisplay)
        return ($Ensure -eq 'Present')
    }

    if (-not $Path)
    {
        # For this non-path variable, make sure that the specified $Value matches the current value.

        if (($checkMachineTarget -and ($Value -cne $currentValueFromMachine)) -or `
           ($checkProcessTarget -and ($Value -cne $currentValueFromProcess)))
        {
            Write-Verbose ($script:localizedData.EnvVarFoundWithMisMatchingValue -f $Name, $currentValueToDisplay, $Value)
            return ($Ensure -eq 'Absent')
        }
        else
        {
            Write-Verbose ($script:localizedData.EnvVarFound -f $Name, $currentValueToDisplay)
            return ($Ensure -eq 'Present')
        }
    }

    # If the control reaches here, the expected environment variable exists, it is a path variable and a $Value is specified to test against
    if ($Ensure -eq 'Present')
    {
        if ($checkMachineTarget)
        {
            if (-not (Test-PathsInValue -ExistingPaths $currentValueFromMachine -QueryPaths $Value -FindCriteria 'All'))
            {
                # If the control reached here some part of the specified path ($Value) was not found in the existing variable, return failure
                Write-Verbose ($script:localizedData.EnvVarFoundWithMisMatchingValue -f $Name, $currentValueToDisplay, $Value)
                return $false
            }
        }

        if ($checkProcessTarget)
        {
            if (-not (Test-PathsInValue -ExistingPaths $currentValueFromProcess -QueryPaths $Value -FindCriteria 'All'))
            {
                # If the control reached here some part of the specified path ($Value) was not found in the existing variable, return failure
                Write-Verbose ($script:localizedData.EnvVarFoundWithMisMatchingValue -f $Name, $currentValueToDisplay, $Value)
                return $false
            }
        }

        # The specified path was completely present in the existing environment variable, return success
        Write-Verbose ($script:localizedData.EnvVarFound -f $Name, $currentValueToDisplay)
        return $true
    }
    # Ensure = 'Absent'
    else
    {
        if ($checkMachineTarget)
        {
            if (Test-PathsInValue -ExistingPaths $currentValueFromMachine -QueryPaths $Value -FindCriteria 'Any')
            {
                # One of the specified paths in $Value exists in the environment variable path, thus the test fails
                Write-Verbose ($script:localizedData.EnvVarFound -f $Name, $currentValueFromMachine)
                return $false
            }
        }

        if ($checkProcessTarget)
        {
            if (Test-PathsInValue -ExistingPaths $currentValueFromProcess -QueryPaths $Value -FindCriteria 'Any')
            {
                # One of the specified paths in $Value exists in the environment variable path, thus the test fails
                Write-Verbose ($script:localizedData.EnvVarFound -f $Name, $currentValueFromProcess)
                return $false
            }
        }

        # If the control reached here, none of the specified paths were found in the existing path-variable, return success
        Write-Verbose ($script:localizedData.EnvVarFoundWithMisMatchingValue -f $Name, $currentValueToDisplay, $Value)
        return $true
    }
}

<#
    .SYNOPSIS
        Retrieves the value of the environment variable from the given Target.

    .PARAMETER Name
        The name of the environment variable to retrieve the value from.

    .PARAMETER Target
        Indicates where to retrieve the environment variable from. Currently, only
        Process and Machine are being used, but User is included for future extension
        of this resource.
#>
function Get-EnvironmentVariable
{
    [CmdletBinding()]
    [OutputType([System.String])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter(Mandatory = $true)]
        [ValidateSet('Process', 'Machine')]
        [System.String]
        $Target
    )

    $valueToReturn = $null

    if ($Target -eq 'Process')
    {
        $valueToReturn = Get-ProcessEnvironmentVariable -Name $Name
    }
    elseif ($Target -eq 'Machine')
    {
        $retrievedProperty = Get-ItemProperty -Path $script:envVarRegPathMachine -Name $Name -ErrorAction 'SilentlyContinue'

        if ($null -ne $retrievedProperty)
        {
            $valueToReturn = $retrievedProperty.$Name
        }
    }
    elseif ($Target -eq 'User')
    {
        $retrievedProperty = Get-ItemProperty -Path $script:envVarRegPathUser -Name $Name -ErrorAction 'SilentlyContinue'

        if ($null -ne $retrievedProperty)
        {
            $valueToReturn = $retrievedProperty.$Name
        }
    }

    return $valueToReturn
}

<#
    .SYNOPSIS
        Wrapper function to retrieve an environment variable from the current process.

    .PARAMETER Name
        The name of the variable to retrieve
#>
function Get-ProcessEnvironmentVariable
{
    [CmdletBinding()]
    [OutputType([System.String])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name
    )

    return [System.Environment]::GetEnvironmentVariable($Name)
}

<#
    .SYNOPSIS
        If there are any paths in NewPaths that aren't in CurrentValue they will be added
        to the current paths value and a String will be returned containing all old paths
        and new paths. Otherwise the original value will be returned unchanged.

    .PARAMETER CurrentValue
        A semicolon-separated String containing the current path values.

    .PARAMETER NewPaths
        A semicolon-separated String containing any paths that should be added to
        the current value. If CurrentValue already contains a path, it will not be added.
#>
function Add-PathsToValue
{
    [CmdletBinding()]
    [OutputType([System.String])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $CurrentValue,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $NewValue
    )

    $finalValue = $CurrentValue + ';'
    $currentPaths = $CurrentValue -split ';'
    $newPaths = $NewValue -split ';'

    foreach ($path in $newPaths)
    {
        if ($currentPaths -notcontains $path)
        {
            <#
                If the control reached here, we didn't find this $specifiedPath in the $currentPaths,
                so add it.
            #>

            $finalValue += ($path + ';')
        }
    }

    # Remove any extraneous ';' at the end (and potentially start - as a side-effect) of the value to be set
    return $finalValue.Trim(';')
}

<#
    .SYNOPSIS
        If there are any paths in PathsToRemove that aren't in CurrentValue they will be removed
        from the current paths value and either the new value will be returned if there are still
        paths that remain, or an empty string will be returned if all paths were removed.
        If none of the paths in PathsToRemove are in CurrentValue then this function will
        return CurrentValue since nothing needs to be changed.

    .PARAMETER CurrentValue
        A semicolon-separated String containing the current path values.

    .PARAMETER PathsToRemove
        A semicolon-separated String containing any paths that should be removed from
        the current value.
#>
function Remove-PathsFromValue
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $CurrentValue,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $PathsToRemove
    )

    $finalPath = ''
    $specifiedPaths = $PathsToRemove -split ';'
    $currentPaths = $CurrentValue -split ';'
    $varAltered = $false

    foreach ($subpath in $currentPaths)
    {
        if ($specifiedPaths -contains $subpath)
        {
            <#
                Found this $subpath as one of the $specifiedPaths, skip adding this to the final
                value/path of this variable and mark the variable as altered.
            #>
            $varAltered = $true
        }
        else
        {
            # the current $subpath was not part of the $specifiedPaths (to be removed) so keep this $subpath in the finalPath
            $finalPath += $subpath + ';'
        }
    }

    # Remove any extraneous ';' at the end (and potentially start - as a side-effect) of the $finalPath
    $finalPath = $finalPath.Trim(';')

    if ($varAltered)
    {
        return $finalPath
    }
    else
    {
        return $CurrentValue
    }
}

<#
    .SYNOPSIS
        Sets the value of the environment variable with the given name if a value is specified.
        If no value is specified, then the environment variable will be removed.

    .PARAMETER Name
        The name of the environment variable to set or remove.

    .PARAMETER Value
        The value to set the environment variable to. If not provided, then the variable will
        be removed.

    .PARAMETER Target
        Indicates where to set or remove the environment variable: The machine, the process, or both.
        The logic for User is also included here for future expansion of this resource.
#>
function Set-EnvironmentVariable
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [System.String]
        $Value,

        [Parameter(Mandatory = $true)]
        [ValidateSet('Process', 'Machine')]
        [System.String[]]
        $Target
    )

    $valueSpecified = $PSBoundParameters.ContainsKey('Value')

    try
    {
        # If the Value is set to [System.String]::Empty then nothing should be updated for the process
        if (($Target -contains 'Process') -and (-not $valueSpecified -or ($Value -ne [System.String]::Empty)))
        {
            if (-not $valueSpecified)
            {
                Set-ProcessEnvironmentVariable -Name $Name -Value $null
            }
            else
            {
                Set-ProcessEnvironmentVariable -Name $Name -Value $Value
            }
        }

        if ($Target -contains 'Machine')
        {
            if ($Name.Length -ge $script:maxSystemEnvVariableLength)
            {
                New-InvalidArgumentException -Message $script:localizedData.ArgumentTooLong -ArgumentName $Name
            }

            $path = $script:envVarRegPathMachine

            if (-not $valueSpecified)
            {
                $environmentKey = Get-ItemProperty -Path $path -Name $Name -ErrorAction 'SilentlyContinue'

                if ($environmentKey)
                {
                    Remove-ItemProperty -Path $path -Name $Name
                }
                else
                {
                    $message = ($script:localizedData.RemoveNonExistentVarError -f $Name)
                    New-InvalidArgumentException -Message $message -ArgumentName $Name
                }
            }
            else
            {
                Set-ItemProperty -Path $path -Name $Name -Value $Value
                $environmentKey = Get-ItemProperty -Path $path -Name $Name -ErrorAction 'SilentlyContinue'

                if ($null -eq $environmentKey)
                {
                    $message = ($script:localizedData.GetItemPropertyFailure -f $Name, $path)
                    New-InvalidArgumentException -Message $message -ArgumentName $Name
                }
            }
        }

        # The User feature of this resource is not yet implemented.
        if ($Target -contains 'User')
        {
            if ($Name.Length -ge $script:maxUserEnvVariableLength)
            {
                New-InvalidArgumentException -Message $script:localizedData.ArgumentTooLong -ArgumentName $Name
            }

            $path = $script:envVarRegPathUser

            if (-not $valueSpecified)
            {
                $environmentKey = Get-ItemProperty -Path $path -Name $Name -ErrorAction 'SilentlyContinue'

                if ($environmentKey)
                {
                    Remove-ItemProperty -Path $path -Name $Name
                }
                else
                {
                    $message = ($script:localizedData.RemoveNonExistentVarError -f $Name)
                    New-InvalidArgumentException -Message $message -ArgumentName $Name
                }
            }
            else
            {
                Set-ItemProperty -Path $path -Name $Name -Value $Value
                $environmentKey = Get-ItemProperty -Path $path -Name $Name -ErrorAction 'SilentlyContinue'

                if ($null -eq $environmentKey)
                {
                    $message = ($script:localizedData.GetItemPropertyFailure -f $Name, $path)
                    New-InvalidArgumentException -Message $message -ArgumentName $Name
                }
            }
        }
    }
    catch
    {
        New-InvalidOperationException -Message ($script:localizedData.EnvVarSetError -f $Name, $Value) `
                                      -ErrorRecord $_
    }

}

<#
    .SYNOPSIS
        Wrapper function to set an environment variable for the current process.

    .PARAMETER Name
        The name of the environment variable to set.

    .PARAMETER Value
        The value to set the environment variable to.
#>
function Set-ProcessEnvironmentVariable
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [System.String]
        $Value = [System.String]::Empty
    )

    [System.Environment]::SetEnvironmentVariable($Name, $Value)
}

<#
    .SYNOPSIS
        Removes an environment variable from the given target(s) by calling Set-EnvironmentVariable
        with no Value specified.

    .PARAMETER Name
        The name of the environment variable to remove.

    .PARAMETER Target
        Indicates where to remove the environment variable from: The machine, the process, or both.
#>
function Remove-EnvironmentVariable
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter(Mandatory = $true)]
        [ValidateSet('Process', 'Machine')]
        [System.String[]]
        $Target
    )

    try
    {
        Set-EnvironmentVariable -Name $Name -Target $Target
    }
    catch
    {
        New-InvalidOperationException -Message ($script:localizedData.EnvVarRemoveError -f $Name) `
                                      -ErrorRecord $_
    }
}

<#
    .SYNOPSIS
        Tests all of the paths in QueryPaths against those in ExistingPaths.
        If FindCriteria is set to 'All' then it will only return True if all of the
        paths in QueryPaths are in ExistingPaths, otherwise it will return False.
        If FindCriteria is set to 'Any' then it will return True if any of the paths
        in QueryPaths are in ExistingPaths, otherwise it will return False.

    .PARAMETER ExistingPaths
        A semicolon-separated String containing the path values to test against.

    .PARAMETER QueryPaths
        A semicolon-separated String containing the path values to ensure are either
        included or not included in ExistingPaths.

    .PARAMETER FindCriteria
        Set to either 'All' or 'Any' to indicate whether all of the paths in QueryPaths
        should be included in ExistingPaths or any of them.
#>
function Test-PathsInValue
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $ExistingPaths,

        [Parameter(Mandatory = $true)]
        [System.String]
        $QueryPaths,

        [Parameter(Mandatory = $true)]
        [ValidateSet('Any', 'All')]
        [System.String]
        $FindCriteria
    )

    $existingPathList = $ExistingPaths -split ';'
    $queryPathList = $QueryPaths -split ';'

    switch ($FindCriteria)
    {
        'Any'
        {
            foreach ($queryPath in $queryPathList)
            {
                if ($existingPathList -contains $queryPath)
                {
                    # Found this $queryPath in the existing paths, return $true
                    return $true
                }
            }

            # If the control reached here, none of the QueryPaths were found in ExistingPaths
            return $false
        }

        'All'
        {
            foreach ($queryPath in $queryPathList)
            {
                if ($queryPath)
                {
                    if ($existingPathList -notcontains $queryPath)
                    {
                        # The current $queryPath wasn't found in any of the $existingPathList, return false
                        return $false
                    }
                }
            }

            # If the control reached here, all of the QueryPaths were found in ExistingPaths
            return $true
        }
    }
}

<#
    .SYNOPSIS
        Retrieves the Environment variable with the given name from the registry on the machine.
        It returns the result as an object containing a Hashtable with the environment variable
        name and its current value on the machine. This is to most closely represent what the
        actual API call returns. If an environment variable with the given name is not found, then
        $null will be returned.

    .PARAMETER Name
        The name of the environment variable to retrieve the value of.
#>
function Get-EnvironmentVariableWithoutExpanding
{
    [OutputType([System.Management.Automation.PSObject])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [System.String]
        $Name
    )

    $path = $script:envVarRegPathMachine
    $pathTokens = $path.Split('\',[System.StringSplitOptions]::RemoveEmptyEntries)
    $entry = $pathTokens[1..($pathTokens.Count - 1)] -join '\'

    # Since the target registry path coming to this function is hardcoded for local machine
    $hive = [Microsoft.Win32.Registry]::LocalMachine

    $noteProperties = @{}

    try
    {
        $key = $hive.OpenSubKey($entry)

        $valueNames = $key.GetValueNames()
        if ($valueNames -inotcontains $Name)
        {
            return $null
        }

        [System.String] $value = Get-KeyValue -Name $Name -Key $key
        $noteProperties.Add($Name, $value)
    }
    finally
    {
        if ($key)
        {
            $key.Close()
        }
    }

    [System.Management.Automation.PSObject] $propertyResults = New-Object -TypeName System.Management.Automation.PSObject -Property $noteProperties

    return $propertyResults
}

<#
    .SYNOPSIS
        Wrapper function to get the value of the environment variable with the given name
        from the specified registry key.

    .PARAMETER Name
        The name of the environment variable to retrieve the value of.

    .PARAMETER Key
        The key to retrieve the environment variable from.
#>
function Get-KeyValue
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [System.String]
        $Name,

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [Microsoft.Win32.RegistryKey]
        $Key
    )

    return $Key.GetValue($Name, $null, [Microsoft.Win32.RegistryValueOptions]::DoNotExpandEnvironmentNames)
}

# SIG # Begin signature block
# MIIjYAYJKoZIhvcNAQcCoIIjUTCCI00CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBteqIjPr9cL6Ok
# Ywr1rl2Gv1v5e93HTWmJBScB7KKFhKCCHVkwggUaMIIEAqADAgECAhADBbuGIbCh
# Y1+/3q4SBOdtMA0GCSqGSIb3DQEBCwUAMHIxCzAJBgNVBAYTAlVTMRUwEwYDVQQK
# EwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xMTAvBgNV
# BAMTKERpZ2lDZXJ0IFNIQTIgQXNzdXJlZCBJRCBDb2RlIFNpZ25pbmcgQ0EwHhcN
# MjAwNTEyMDAwMDAwWhcNMjMwNjA4MTIwMDAwWjBXMQswCQYDVQQGEwJVUzERMA8G
# A1UECBMIVmlyZ2luaWExDzANBgNVBAcTBlZpZW5uYTERMA8GA1UEChMIZGJhdG9v
# bHMxETAPBgNVBAMTCGRiYXRvb2xzMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
# CgKCAQEAvL9je6vjv74IAbaY5rXqHxaNeNJO9yV0ObDg+kC844Io2vrHKGD8U5hU
# iJp6rY32RVprnAFrA4jFVa6P+sho7F5iSVAO6A+QZTHQCn7oquOefGATo43NAadz
# W2OWRro3QprMPZah0QFYpej9WaQL9w/08lVaugIw7CWPsa0S/YjHPGKQ+bYgI/kr
# EUrk+asD7lvNwckR6pGieWAyf0fNmSoevQBTV6Cd8QiUfj+/qWvLW3UoEX9ucOGX
# 2D8vSJxL7JyEVWTHg447hr6q9PzGq+91CO/c9DWFvNMjf+1c5a71fEZ54h1mNom/
# XoWZYoKeWhKnVdv1xVT1eEimibPEfQIDAQABo4IBxTCCAcEwHwYDVR0jBBgwFoAU
# WsS5eyoKo6XqcQPAYPkt9mV1DlgwHQYDVR0OBBYEFPDAoPu2A4BDTvsJ193ferHL
# 454iMA4GA1UdDwEB/wQEAwIHgDATBgNVHSUEDDAKBggrBgEFBQcDAzB3BgNVHR8E
# cDBuMDWgM6Axhi9odHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc2hhMi1hc3N1cmVk
# LWNzLWcxLmNybDA1oDOgMYYvaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NoYTIt
# YXNzdXJlZC1jcy1nMS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAwEwKjAoBggr
# BgEFBQcCARYcaHR0cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBBAEw
# gYQGCCsGAQUFBwEBBHgwdjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNl
# cnQuY29tME4GCCsGAQUFBzAChkJodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20v
# RGlnaUNlcnRTSEEyQXNzdXJlZElEQ29kZVNpZ25pbmdDQS5jcnQwDAYDVR0TAQH/
# BAIwADANBgkqhkiG9w0BAQsFAAOCAQEAj835cJUMH9Y2pBKspjznNJwcYmOxeBcH
# Ji+yK0y4bm+j44OGWH4gu/QJM+WjZajvkydJKoJZH5zrHI3ykM8w8HGbYS1WZfN4
# oMwi51jKPGZPw9neGS2PXrBcKjzb7rlQ6x74Iex+gyf8z1ZuRDitLJY09FEOh0BM
# LaLh+UvJ66ghmfIyjP/g3iZZvqwgBhn+01fObqrAJ+SagxJ/21xNQJchtUOWIlxR
# kuUn9KkuDYrMO70a2ekHODcAbcuHAGI8wzw4saK1iPPhVTlFijHS+7VfIt/d/18p
# MLHHArLQQqe1Z0mTfuL4M4xCUKpebkH8rI3Fva62/6osaXLD0ymERzCCBTAwggQY
# oAMCAQICEAQJGBtf1btmdVNDtW+VUAgwDQYJKoZIhvcNAQELBQAwZTELMAkGA1UE
# BhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2lj
# ZXJ0LmNvbTEkMCIGA1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4X
# DTEzMTAyMjEyMDAwMFoXDTI4MTAyMjEyMDAwMFowcjELMAkGA1UEBhMCVVMxFTAT
# BgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEx
# MC8GA1UEAxMoRGlnaUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBD
# QTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPjTsxx/DhGvZ3cH0wsx
# SRnP0PtFmbE620T1f+Wondsy13Hqdp0FLreP+pJDwKX5idQ3Gde2qvCchqXYJawO
# eSg6funRZ9PG+yknx9N7I5TkkSOWkHeC+aGEI2YSVDNQdLEoJrskacLCUvIUZ4qJ
# RdQtoaPpiCwgla4cSocI3wz14k1gGL6qxLKucDFmM3E+rHCiq85/6XzLkqHlOzEc
# z+ryCuRXu0q16XTmK/5sy350OTYNkO/ktU6kqepqCquE86xnTrXE94zRICUj6whk
# PlKWwfIPEvTFjg/BougsUfdzvL2FsWKDc0GCB+Q4i2pzINAPZHM8np+mM6n9Gd8l
# k9ECAwEAAaOCAc0wggHJMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYDVR0PAQH/BAQD
# AgGGMBMGA1UdJQQMMAoGCCsGAQUFBwMDMHkGCCsGAQUFBwEBBG0wazAkBggrBgEF
# BQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAChjdodHRw
# Oi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0Eu
# Y3J0MIGBBgNVHR8EejB4MDqgOKA2hjRodHRwOi8vY3JsNC5kaWdpY2VydC5jb20v
# RGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsMDqgOKA2hjRodHRwOi8vY3JsMy5k
# aWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsME8GA1UdIARI
# MEYwOAYKYIZIAYb9bAACBDAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdp
# Y2VydC5jb20vQ1BTMAoGCGCGSAGG/WwDMB0GA1UdDgQWBBRaxLl7KgqjpepxA8Bg
# +S32ZXUOWDAfBgNVHSMEGDAWgBRF66Kv9JLLgjEtUYunpyGd823IDzANBgkqhkiG
# 9w0BAQsFAAOCAQEAPuwNWiSz8yLRFcgsfCUpdqgdXRwtOhrE7zBh134LYP3DPQ/E
# r4v97yrfIFU3sOH20ZJ1D1G0bqWOWuJeJIFOEKTuP3GOYw4TS63XX0R58zYUBor3
# nEZOXP+QsRsHDpEV+7qvtVHCjSSuJMbHJyqhKSgaOnEoAjwukaPAJRHinBRHoXpo
# aK+bp1wgXNlxsQyPu6j4xRJon89Ay0BEpRPw5mQMJQhCMrI2iiQC/i9yfhzXSUWW
# 6Fkd6fp0ZGuy62ZD2rOwjNXpDd32ASDOmTFjPQgaGLOBm0/GkxAG/AeB+ova+YJJ
# 92JuoVP6EpQYhS6SkepobEQysmah5xikmmRR7zCCBY0wggR1oAMCAQICEA6bGI75
# 0C3n79tQ4ghAGFowDQYJKoZIhvcNAQEMBQAwZTELMAkGA1UEBhMCVVMxFTATBgNV
# BAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEkMCIG
# A1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4XDTIyMDgwMTAwMDAw
# MFoXDTMxMTEwOTIzNTk1OVowYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGln
# aUNlcnQgVHJ1c3RlZCBSb290IEc0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAv+aQc2jeu+RdSjwwIjBpM+zCpyUuySE98orYWcLhKac9WKt2ms2uexuE
# DcQwH/MbpDgW61bGl20dq7J58soR0uRf1gU8Ug9SH8aeFaV+vp+pVxZZVXKvaJNw
# wrK6dZlqczKU0RBEEC7fgvMHhOZ0O21x4i0MG+4g1ckgHWMpLc7sXk7Ik/ghYZs0
# 6wXGXuxbGrzryc/NrDRAX7F6Zu53yEioZldXn1RYjgwrt0+nMNlW7sp7XeOtyU9e
# 5TXnMcvak17cjo+A2raRmECQecN4x7axxLVqGDgDEI3Y1DekLgV9iPWCPhCRcKtV
# gkEy19sEcypukQF8IUzUvK4bA3VdeGbZOjFEmjNAvwjXWkmkwuapoGfdpCe8oU85
# tRFYF/ckXEaPZPfBaYh2mHY9WV1CdoeJl2l6SPDgohIbZpp0yt5LHucOY67m1O+S
# kjqePdwA5EUlibaaRBkrfsCUtNJhbesz2cXfSwQAzH0clcOP9yGyshG3u3/y1Yxw
# LEFgqrFjGESVGnZifvaAsPvoZKYz0YkH4b235kOkGLimdwHhD5QMIR2yVCkliWzl
# DlJRR3S+Jqy2QXXeeqxfjT/JvNNBERJb5RBQ6zHFynIWIgnffEx1P2PsIV/EIFFr
# b7GrhotPwtZFX50g/KEexcCPorF+CiaZ9eRpL5gdLfXZqbId5RsCAwEAAaOCATow
# ggE2MA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFOzX44LScV1kTN8uZz/nupiu
# HA9PMB8GA1UdIwQYMBaAFEXroq/0ksuCMS1Ri6enIZ3zbcgPMA4GA1UdDwEB/wQE
# AwIBhjB5BggrBgEFBQcBAQRtMGswJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRp
# Z2ljZXJ0LmNvbTBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQu
# Y29tL0RpZ2lDZXJ0QXNzdXJlZElEUm9vdENBLmNydDBFBgNVHR8EPjA8MDqgOKA2
# hjRodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290
# Q0EuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQwFAAOCAQEAcKC/
# Q1xV5zhfoKN0Gz22Ftf3v1cHvZqsoYcs7IVeqRq7IviHGmlUIu2kiHdtvRoU9BNK
# ei8ttzjv9P+Aufih9/Jy3iS8UgPITtAq3votVs/59PesMHqai7Je1M/RQ0SbQyHr
# lnKhSLSZy51PpwYDE3cnRNTnf+hZqPC/Lwum6fI0POz3A8eHqNJMQBk1RmppVLC4
# oVaO7KTVPeix3P0c2PR3WlxUjG/voVA9/HYJaISfb8rbII01YBwCA8sgsKxYoA5A
# Y8WYIsGyWfVVa88nq2x2zm8jLfR+cWojayL/ErhULSd+2DrZ8LaHlv1b0VysGMNN
# n3O3AamfV6peKOK5lDCCBq4wggSWoAMCAQICEAc2N7ckVHzYR6z9KGYqXlswDQYJ
# KoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IElu
# YzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGlnaUNlcnQg
# VHJ1c3RlZCBSb290IEc0MB4XDTIyMDMyMzAwMDAwMFoXDTM3MDMyMjIzNTk1OVow
# YzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQD
# EzJEaWdpQ2VydCBUcnVzdGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGlu
# ZyBDQTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMaGNQZJs8E9cklR
# VcclA8TykTepl1Gh1tKD0Z5Mom2gsMyD+Vr2EaFEFUJfpIjzaPp985yJC3+dH54P
# Mx9QEwsmc5Zt+FeoAn39Q7SE2hHxc7Gz7iuAhIoiGN/r2j3EF3+rGSs+QtxnjupR
# PfDWVtTnKC3r07G1decfBmWNlCnT2exp39mQh0YAe9tEQYncfGpXevA3eZ9drMvo
# hGS0UvJ2R/dhgxndX7RUCyFobjchu0CsX7LeSn3O9TkSZ+8OpWNs5KbFHc02DVzV
# 5huowWR0QKfAcsW6Th+xtVhNef7Xj3OTrCw54qVI1vCwMROpVymWJy71h6aPTnYV
# VSZwmCZ/oBpHIEPjQ2OAe3VuJyWQmDo4EbP29p7mO1vsgd4iFNmCKseSv6De4z6i
# c/rnH1pslPJSlRErWHRAKKtzQ87fSqEcazjFKfPKqpZzQmiftkaznTqj1QPgv/Ci
# PMpC3BhIfxQ0z9JMq++bPf4OuGQq+nUoJEHtQr8FnGZJUlD0UfM2SU2LINIsVzV5
# K6jzRWC8I41Y99xh3pP+OcD5sjClTNfpmEpYPtMDiP6zj9NeS3YSUZPJjAw7W4oi
# qMEmCPkUEBIDfV8ju2TjY+Cm4T72wnSyPx4JduyrXUZ14mCjWAkBKAAOhFTuzuld
# yF4wEr1GnrXTdrnSDmuZDNIztM2xAgMBAAGjggFdMIIBWTASBgNVHRMBAf8ECDAG
# AQH/AgEAMB0GA1UdDgQWBBS6FtltTYUvcyl2mi91jGogj57IbzAfBgNVHSMEGDAW
# gBTs1+OC0nFdZEzfLmc/57qYrhwPTzAOBgNVHQ8BAf8EBAMCAYYwEwYDVR0lBAww
# CgYIKwYBBQUHAwgwdwYIKwYBBQUHAQEEazBpMCQGCCsGAQUFBzABhhhodHRwOi8v
# b2NzcC5kaWdpY2VydC5jb20wQQYIKwYBBQUHMAKGNWh0dHA6Ly9jYWNlcnRzLmRp
# Z2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRSb290RzQuY3J0MEMGA1UdHwQ8MDow
# OKA2oDSGMmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRS
# b290RzQuY3JsMCAGA1UdIAQZMBcwCAYGZ4EMAQQCMAsGCWCGSAGG/WwHATANBgkq
# hkiG9w0BAQsFAAOCAgEAfVmOwJO2b5ipRCIBfmbW2CFC4bAYLhBNE88wU86/GPvH
# UF3iSyn7cIoNqilp/GnBzx0H6T5gyNgL5Vxb122H+oQgJTQxZ822EpZvxFBMYh0M
# CIKoFr2pVs8Vc40BIiXOlWk/R3f7cnQU1/+rT4osequFzUNf7WC2qk+RZp4snuCK
# rOX9jLxkJodskr2dfNBwCnzvqLx1T7pa96kQsl3p/yhUifDVinF2ZdrM8HKjI/rA
# J4JErpknG6skHibBt94q6/aesXmZgaNWhqsKRcnfxI2g55j7+6adcq/Ex8HBanHZ
# xhOACcS2n82HhyS7T6NJuXdmkfFynOlLAlKnN36TU6w7HQhJD5TNOXrd/yVjmScs
# PT9rp/Fmw0HNT7ZAmyEhQNC3EyTN3B14OuSereU0cZLXJmvkOHOrpgFPvT87eK1M
# rfvElXvtCl8zOYdBeHo46Zzh3SP9HSjTx/no8Zhf+yvYfvJGnXUsHicsJttvFXse
# GYs2uJPU5vIXmVnKcPA3v5gA3yAWTyf7YGcWoWa63VXAOimGsJigK+2VQbc61RWY
# MbRiCQ8KvYHZE/6/pNHzV9m8BPqC3jLfBInwAM1dwvnQI38AC+R2AibZ8GV2QqYp
# hwlHK+Z/GqSFD/yYlvZVVCsfgPrA8g4r5db7qS9EFUrnEw4d2zc4GqEr9u3WfPww
# ggbAMIIEqKADAgECAhAMTWlyS5T6PCpKPSkHgD1aMA0GCSqGSIb3DQEBCwUAMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwHhcNMjIwOTIxMDAwMDAwWhcNMzMxMTIxMjM1OTU5WjBGMQswCQYDVQQGEwJV
# UzERMA8GA1UEChMIRGlnaUNlcnQxJDAiBgNVBAMTG0RpZ2lDZXJ0IFRpbWVzdGFt
# cCAyMDIyIC0gMjCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAM/spSY6
# xqnya7uNwQ2a26HoFIV0MxomrNAcVR4eNm28klUMYfSdCXc9FZYIL2tkpP0GgxbX
# kZI4HDEClvtysZc6Va8z7GGK6aYo25BjXL2JU+A6LYyHQq4mpOS7eHi5ehbhVsbA
# umRTuyoW51BIu4hpDIjG8b7gL307scpTjUCDHufLckkoHkyAHoVW54Xt8mG8qjoH
# ffarbuVm3eJc9S/tjdRNlYRo44DLannR0hCRRinrPibytIzNTLlmyLuqUDgN5YyU
# XRlav/V7QG5vFqianJVHhoV5PgxeZowaCiS+nKrSnLb3T254xCg/oxwPUAY3ugjZ
# Naa1Htp4WB056PhMkRCWfk3h3cKtpX74LRsf7CtGGKMZ9jn39cFPcS6JAxGiS7uY
# v/pP5Hs27wZE5FX/NurlfDHn88JSxOYWe1p+pSVz28BqmSEtY+VZ9U0vkB8nt9Kr
# FOU4ZodRCGv7U0M50GT6Vs/g9ArmFG1keLuY/ZTDcyHzL8IuINeBrNPxB9Thvdld
# S24xlCmL5kGkZZTAWOXlLimQprdhZPrZIGwYUWC6poEPCSVT8b876asHDmoHOWIZ
# ydaFfxPZjXnPYsXs4Xu5zGcTB5rBeO3GiMiwbjJ5xwtZg43G7vUsfHuOy2SJ8bHE
# uOdTXl9V0n0ZKVkDTvpd6kVzHIR+187i1Dp3AgMBAAGjggGLMIIBhzAOBgNVHQ8B
# Af8EBAMCB4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAg
# BgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZ
# bU2FL3MpdpovdYxqII+eyG8wHQYDVR0OBBYEFGKK3tBh/I8xFO2XC809KpQU31Kc
# MFoGA1UdHwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdp
# Q2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAG
# CCsGAQUFBwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2Vy
# dC5jb20wWAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9E
# aWdpQ2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQw
# DQYJKoZIhvcNAQELBQADggIBAFWqKhrzRvN4Vzcw/HXjT9aFI/H8+ZU5myXm93KK
# mMN31GT8Ffs2wklRLHiIY1UJRjkA/GnUypsp+6M/wMkAmxMdsJiJ3HjyzXyFzVOd
# r2LiYWajFCpFh0qYQitQ/Bu1nggwCfrkLdcJiXn5CeaIzn0buGqim8FTYAnoo7id
# 160fHLjsmEHw9g6A++T/350Qp+sAul9Kjxo6UrTqvwlJFTU2WZoPVNKyG39+Xgmt
# dlSKdG3K0gVnK3br/5iyJpU4GYhEFOUKWaJr5yI+RCHSPxzAm+18SLLYkgyRTzxm
# lK9dAlPrnuKe5NMfhgFknADC6Vp0dQ094XmIvxwBl8kZI4DXNlpflhaxYwzGRkA7
# zl011Fk+Q5oYrsPJy8P7mxNfarXH4PMFw1nfJ2Ir3kHJU7n/NBBn9iYymHv+XEKU
# gZSCnawKi8ZLFUrTmJBFYDOA4CPe+AOk9kVH5c64A0JH6EE2cXet/aLol3ROLtoe
# HYxayB6a1cLwxiKoT5u92ByaUcQvmvZfpyeXupYuhVfAYOd4Vn9q78KVmksRAsiC
# nMkaBXy6cbVOepls9Oie1FqYyJ+/jbsYXEP10Cro4mLueATbvdH7WwqocH7wl4R4
# 4wgDXUcsY6glOJcB0j862uXl9uab3H4szP8XTE0AotjWAQ64i+7m4HJViSwnGWH2
# dwGMMYIFXTCCBVkCAQEwgYYwcjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTExMC8GA1UEAxMoRGln
# aUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBDQQIQAwW7hiGwoWNf
# v96uEgTnbTANBglghkgBZQMEAgEFAKCBhDAYBgorBgEEAYI3AgEMMQowCKACgACh
# AoAAMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAM
# BgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCAztB0SBDspMZvnScGvQbGMhXUe
# 8sm8CYz7DdMTc6k5GzANBgkqhkiG9w0BAQEFAASCAQBiadOe0oMH2RKfVcuCf+cq
# 2MNZ59fTLjXu4Te1KhAlf86NGVxvbACY2keOTFlvoZdGxyGVIJlzSkTKrXX9b4C7
# /obbcV1fglHN7NlY/DbDs38wvGb0K0QWlkixg+asMUn2VTadCQViOaLOIogDfUOm
# Ep8HW/yp8LnGQ26RMENESnvVcZOXOVNxkFZEryaaD4VneGOVX6romqkAj5Cgx+Es
# Cqkhl3gL7H85KVUKsKthF87d/TkscqNTnpZCA2S3FtFsfqvBwVIaDFGB5WRy5joD
# x24BZ23mFoibeXcdiPUpJ2bVOxWj49GpGexCD+dM7FBJuOoRfV+XzydrUFT4t+Vj
# oYIDIDCCAxwGCSqGSIb3DQEJBjGCAw0wggMJAgEBMHcwYzELMAkGA1UEBhMCVVMx
# FzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQDEzJEaWdpQ2VydCBUcnVz
# dGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGluZyBDQQIQDE1pckuU+jwq
# Sj0pB4A9WjANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0B
# BwEwHAYJKoZIhvcNAQkFMQ8XDTIzMDUxMjAwMjUyNVowLwYJKoZIhvcNAQkEMSIE
# IEreCs50nFZjlnQr29o5uSmKKQATBnIUq+uA8kItlwzdMA0GCSqGSIb3DQEBAQUA
# BIICAFgRv5hKVL0NjjEbcesKScjfUr9eYEM/hx/Je7JdTjk+w6yUSSbiQ2mpRSGr
# Rd3EJeKyr8DuDBlSoSF2ZS77p+jWf2R9ijLdZ92I0cLz0Yriv0ixwrba0rvZ2DVD
# UawlP9aWhr6EdkUnj+nPOOQRLTuuWZ79WCK9tVXF0g2NkjWUiPV6o79xpmPDfNPs
# UswlqLzA/SJ0RMqzWEDkU8oeqtbmVunrF1LBu/BcVz3DfEFef3zYsXvgcxi1ESFo
# udebX+d57hVBmLouK34k9xjSaDGtMYrVYLxGxJkUKzDYDaMf0lYetrINQFcw+LQc
# xltDWqgM+0RY75ykAK8EUrwCTP+1TECx7iEDs0rcbxwDxBi0EhE2izM/7QOHK69O
# /2ZyTqGNEN03iwzx12/PSJE8f2YIUkV6Gk1L4moStmW/cvD5sp7VwaiY4wsGrloz
# MA0mOMaViB64qHpOKUc2vAmYxJfCQMUHfDegFjOxnS5p6jVA7og/TCcA5vzsN+o/
# u0Zyf4EKQ6i1Ym28EYMc4O+Waplz3OYcSxiHydbjoOwRxW/U2gfzCtQahoF12rNJ
# v3o5yARTXsF7zSYAhjkhxGHUbbsUvlfJHshhZxxoohz/HRr1eYQu0UHZmjYiPA7i
# s85sYSx7z0IhNBztrEW7I7yu29bpfuA3OjbWJrNMlezapxpE
# SIG # End signature block
