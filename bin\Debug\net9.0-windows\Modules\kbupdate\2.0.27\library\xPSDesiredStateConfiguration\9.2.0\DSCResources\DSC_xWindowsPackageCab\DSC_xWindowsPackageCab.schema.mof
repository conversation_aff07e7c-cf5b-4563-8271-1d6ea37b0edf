
[ClassVersion("*******"), FriendlyName("xWindowsPackageCab")]
class DSC_xWindowsPackageCab : OMI_BaseResource
{
    [Key, Description("The name of the package to install or uninstall.")] String Name;
    [Required, Description("Specifies whether the package should be installed or uninstalled. To install the package, set this property to Present. To uninstall the package, set the property to Absent."), ValueMap{"Present", "Absent"}, Values{"Present", "Absent"}] String Ensure;
    [Required, Description("The path to the cab file to install or uninstall the package from.")] String SourcePath;
    [Write, Description("The path to a file to log the operation to.")] String LogPath;
};
