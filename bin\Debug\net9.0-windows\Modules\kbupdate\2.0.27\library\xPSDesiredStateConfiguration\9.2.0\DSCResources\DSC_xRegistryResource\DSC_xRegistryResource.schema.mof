[ClassVersion("1.0.0"), FriendlyName("xRegistry")]
class DSC_xRegistryResource : OMI_BaseResource
{
  [Key, Description("The path of the registry key to add, modify, or remove. This path must include the registry hive/drive.")] String Key;
  [Key, Description("The name of the registry value. To add or remove a registry key, specify this property as an empty string without specifying ValueType or ValueData. To modify or remove the default value of a registry key, specify this property as an empty string while also specifying ValueType or ValueData.")] String ValueName;
  [Write, Description("The data the specified registry key value should have as a string or an array of strings (MultiString only).")] String ValueData[];
  [Write, Description("The type the specified registry key value should have."), ValueMap{"String", "Binary", "DWord", "QWord", "MultiString", "ExpandString"},Values{"String", "Binary", "DWord", "QWord", "MultiString", "ExpandString"}] String ValueType;
  [Write, Description("Specifies whether or not the registry key or value should exist. To add or modify a registry key or value, set this property to Present. To remove a registry key or value, set the property to Absent."), ValueMap{"Present", "Absent"},Values{"Present", "Absent"}] String Ensure;
  [Write, Description("Specifies whether or not the specified DWord or QWord registry key data is provided in a hexadecimal format. Not valid for types other than DWord and QWord. The default value is $false.")] Boolean Hex;
  [Write, Description("Specifies whether or not to overwrite the specified registry key value if it already has a value or whether or not to delete a registry key that has subkeys. The default value is $false.")] Boolean Force;
};
