<?xml version="1.0" encoding="utf-8"?>
<Types>
  <!-- PSFramework.Message.CallStack -->
  <Type>
    <Name>Deserialized.PSFramework.Message.CallStack</Name>
    <Members>
      <MemberSet>
        <Name>PSStandardMembers</Name>
        <Members>
          <NoteProperty>
            <Name>
              TargetTypeForDeserialization
            </Name>
            <Value>
              PSFramework.Message.CallStack
            </Value>
          </NoteProperty>
        </Members>
      </MemberSet>
    </Members>
  </Type>
  <Type>
    <Name>PSFramework.Message.CallStack</Name>
    <Members>
      <CodeProperty IsHidden="true">
        <Name>SerializationData</Name>
        <GetCodeReference>
          <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
          <MethodName>GetSerializationData</MethodName>
        </GetCodeReference>
      </CodeProperty>
    </Members>
    <TypeConverter>
      <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
    </TypeConverter>
  </Type>

  <!-- PSFramework.Message.CallStackEntry -->
  <Type>
    <Name>Deserialized.PSFramework.Message.CallStackEntry</Name>
    <Members>
      <MemberSet>
        <Name>PSStandardMembers</Name>
        <Members>
          <NoteProperty>
            <Name>
              TargetTypeForDeserialization
            </Name>
            <Value>
              PSFramework.Message.CallStackEntry
            </Value>
          </NoteProperty>
        </Members>
      </MemberSet>
    </Members>
  </Type>
  <Type>
    <Name>PSFramework.Message.CallStackEntry</Name>
    <Members>
      <CodeProperty IsHidden="true">
        <Name>SerializationData</Name>
        <GetCodeReference>
          <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
          <MethodName>GetSerializationData</MethodName>
        </GetCodeReference>
      </CodeProperty>
    </Members>
    <TypeConverter>
      <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
    </TypeConverter>
  </Type>

  <!-- PSFramework.Message.LogEntry -->
  <Type>
    <Name>Deserialized.PSFramework.Message.LogEntry</Name>
    <Members>
      <MemberSet>
        <Name>PSStandardMembers</Name>
        <Members>
          <NoteProperty>
            <Name>
              TargetTypeForDeserialization
            </Name>
            <Value>
              PSFramework.Message.LogEntry
            </Value>
          </NoteProperty>
        </Members>
      </MemberSet>
    </Members>
  </Type>
  <Type>
    <Name>PSFramework.Message.LogEntry</Name>
    <Members>
      <CodeProperty IsHidden="true">
        <Name>SerializationData</Name>
        <GetCodeReference>
          <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
          <MethodName>GetSerializationData</MethodName>
        </GetCodeReference>
      </CodeProperty>
    </Members>
    <TypeConverter>
      <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
    </TypeConverter>
  </Type>
	
  <!-- PSFramework.Message.PsfException -->
  <Type>
    <Name>Deserialized.PSFramework.Message.PsfException</Name>
    <Members>
      <MemberSet>
        <Name>PSStandardMembers</Name>
        <Members>
          <NoteProperty>
            <Name>
              TargetTypeForDeserialization
            </Name>
            <Value>
              PSFramework.Message.PsfException
            </Value>
          </NoteProperty>
        </Members>
      </MemberSet>
    </Members>
  </Type>
  <Type>
    <Name>PSFramework.Message.PsfException</Name>
    <Members>
      <CodeProperty IsHidden="true">
        <Name>SerializationData</Name>
        <GetCodeReference>
          <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
          <MethodName>GetSerializationData</MethodName>
        </GetCodeReference>
      </CodeProperty>
    </Members>
    <TypeConverter>
      <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
    </TypeConverter>
  </Type>
	
  <!-- PSFramework.Message.PsfExceptionRecord -->
  <Type>
    <Name>Deserialized.PSFramework.Message.PsfExceptionRecord</Name>
    <Members>
      <MemberSet>
        <Name>PSStandardMembers</Name>
        <Members>
          <NoteProperty>
            <Name>
              TargetTypeForDeserialization
            </Name>
            <Value>
              PSFramework.Message.PsfExceptionRecord
            </Value>
          </NoteProperty>
        </Members>
      </MemberSet>
    </Members>
  </Type>
  <Type>
    <Name>PSFramework.Message.PsfExceptionRecord</Name>
    <Members>
      <CodeProperty IsHidden="true">
        <Name>SerializationData</Name>
        <GetCodeReference>
          <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
          <MethodName>GetSerializationData</MethodName>
        </GetCodeReference>
      </CodeProperty>
    </Members>
    <TypeConverter>
      <TypeName>PSFramework.Serialization.SerializationTypeConverter</TypeName>
    </TypeConverter>
  </Type>
</Types>