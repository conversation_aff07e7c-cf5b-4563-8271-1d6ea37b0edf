$errorActionPreference = 'Stop'
Set-StrictMode -Version 'Latest'

$modulePath = Join-Path -Path (Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent) -ChildPath 'Modules'

# Import the shared modules
Import-Module -Name (Join-Path -Path $modulePath `
    -ChildPath (Join-Path -Path 'xPSDesiredStateConfiguration.Common' `
        -ChildPath 'xPSDesiredStateConfiguration.Common.psm1'))

Import-Module -Name (Join-Path -Path $modulePath -ChildPath 'DscResource.Common')

# Import Localization Strings
$script:localizedData = Get-LocalizedData -DefaultUICulture 'en-US'

Add-Type -AssemblyName 'System.IO.Compression'

# This resource has not yet been tested on a Nano server.
if (-not (Test-IsNanoServer))
{
    Add-Type -AssemblyName 'System.IO.Compression.FileSystem'
}

<#
    .SYNOPSIS
        Retrieves the current state of the archive resource with the specified path and
        destination.

        The returned object provides the following properties:
            Path: The specified path.
            Destination: The specified destination.
            Ensure: Present if the archive at the specified path is expanded at the specified
                destination. Absent if the archive at the specified path is not expanded at the
                specified destination.

    .PARAMETER Path
        The path to the archive file that should or should not be expanded at the specified
        destination.

    .PARAMETER Destination
        The path where the archive file should or should not be expanded.

    .PARAMETER Validate
        Specifies whether or not to validate that a file at the destination with the same name as a
        file in the archive actually matches that corresponding file in the archive by the
        specified checksum method.

        If a file does not match it will be considered not present.

        The default value is false.

    .PARAMETER Checksum
        The Checksum method to use to validate whether or not a file at the destination with the
        same name as a file in the archive actually matches that corresponding file in the archive.

        An invalid argument exception will be thrown if Checksum is specified while Validate is
        specified as false.

        ModifiedDate will check that the LastWriteTime property of the file at the destination
        matches the LastWriteTime property of the file in the archive.
        CreatedDate will check that the CreationTime property of the file at the destination
        matches the CreationTime property of the file in the archive.
        SHA-1, SHA-256, and SHA-512 will check that the hash of the file at the destination by the
        specified SHA method matches the hash of the file in the archive by the specified SHA
        method.

        The default value is ModifiedDate.

    .PARAMETER Credential
        The credential of a user account with permissions to access the specified archive path and
        destination if needed.
#>
function Get-TargetResource
{
    [OutputType([System.Collections.Hashtable])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination,

        [Parameter()]
        [System.Boolean]
        $Validate = $false,

        [Parameter()]
        [ValidateSet('SHA-1', 'SHA-256', 'SHA-512', 'CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum = 'ModifiedDate',

        [Parameter()]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $Credential
    )

    if ($PSBoundParameters.ContainsKey('Checksum') -and -not $Validate)
    {
        $errorMessage = $script:localizedData.ChecksumSpecifiedAndValidateFalse -f $Checksum, $Path, $Destination
        New-InvalidArgumentException -ArgumentName 'Checksum or Validate' -Message $errorMessage
    }

    $archiveState = @{
        Path = $Path
        Destination = $Destination
    }

    # In case an error occurs, we assume that the archive is not expanded at the destination
    $archiveExpandedAtDestination = $false

    $psDrive = $null

    if ($PSBoundParameters.ContainsKey('Credential'))
    {
        $psDrive = Mount-PSDriveWithCredential -Path $Path -Credential $Credential
    }

    try
    {
        Assert-PathExistsAsLeaf -Path $Path
        Assert-DestinationDoesNotExistAsFile -Destination $Destination

        Write-Verbose -Message ($script:localizedData.RetrievingArchiveState -f $Path, $Destination)

        $testArchiveExistsAtDestinationParameters = @{
            ArchiveSourcePath = $Path
            Destination = $Destination
        }

        if ($Validate)
        {
            $testArchiveExistsAtDestinationParameters['Checksum'] = $Checksum
        }

        if (Test-Path -LiteralPath $Destination)
        {
            Write-Verbose -Message ($script:localizedData.DestinationExists -f $Destination)

            $archiveExpandedAtDestination = Test-ArchiveExistsAtDestination @testArchiveExistsAtDestinationParameters
        }
        else
        {
            Write-Verbose -Message ($script:localizedData.DestinationDoesNotExist -f $Destination)
        }
    }
    finally
    {
        if ($null -ne $psDrive)
        {
            Write-Verbose -Message ($script:localizedData.RemovingPSDrive -f $psDrive.Root)

            $null = Remove-PSDrive -Name $psDrive -Force -ErrorAction 'SilentlyContinue'
        }
    }

    if ($archiveExpandedAtDestination)
    {
        $archiveState['Ensure'] = 'Present'
    }
    else
    {
        $archiveState['Ensure'] = 'Absent'
    }

    return $archiveState
}

<#
    .SYNOPSIS
        Expands the archive (.zip) file at the specified path to the specified destination or
        removes the expanded archive (.zip) file at the specified path from the specified
        destination.

    .PARAMETER Path
        The path to the archive file that should be expanded to or removed from the specified
        destination.

    .PARAMETER Destination
        The path where the specified archive file should be expanded to or removed from.

    .PARAMETER Ensure
        Specifies whether or not the expanded content of the archive file at the specified path
        should exist at the specified destination.

        To update the specified destination to have the expanded content of the archive file at the
        specified path, specify this property as Present.
        To remove the expanded content of the archive file at the specified path from the specified
        destination, specify this property as Absent.

        The default value is Present.

    .PARAMETER Validate
        Specifies whether or not to validate that a file at the destination with the same name as a
        file in the archive actually matches that corresponding file in the archive by the
        specified checksum method.

        If the file does not match and Ensure is specified as Present and Force is not specified,
        the resource will throw an error that the file at the destination cannot be overwritten.
        If the file does not match and Ensure is specified as Present and Force is specified, the
        file at the destination will be overwritten.
        If the file does not match and Ensure is specified as Absent, the file at the destination
        will not be removed.

        The default value is false.

    .PARAMETER Checksum
        The Checksum method to use to validate whether or not a file at the destination with the
        same name as a file in the archive actually matches that corresponding file in the archive.

        An invalid argument exception will be thrown if Checksum is specified while Validate is
        specified as false.

        ModifiedDate will check that the LastWriteTime property of the file at the destination
        matches the LastWriteTime property of the file in the archive.
        CreatedDate will check that the CreationTime property of the file at the destination
        matches the CreationTime property of the file in the archive.
        SHA-1, SHA-256, and SHA-512 will check that the hash of the file at the destination by the
        specified SHA method matches the hash of the file in the archive by the specified SHA
        method.

        The default value is ModifiedDate.

    .PARAMETER Credential
        The credential of a user account with permissions to access the specified archive path and
        destination if needed.

    .PARAMETER Force
        Specifies whether or not any existing files or directories at the destination with the same
        name as a file or directory in the archive should be overwritten to match the file or
        directory in the archive.

        When this property is false, an error will be thrown if an item at the destination needs to
        be overwritten.

        The default value is false.
#>
function Set-TargetResource
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination,

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present',

        [Parameter()]
        [System.Boolean]
        $Validate = $false,

        [Parameter()]
        [ValidateSet('SHA-1', 'SHA-256', 'SHA-512', 'CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum = 'ModifiedDate',

        [Parameter()]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $Credential,

        [Parameter()]
        [System.Boolean]
        $Force = $false
    )

    if ($PSBoundParameters.ContainsKey('Checksum') -and -not $Validate)
    {
        $errorMessage = $script:localizedData.ChecksumSpecifiedAndValidateFalse -f $Checksum, $Path, $Destination
        New-InvalidArgumentException -ArgumentName 'Checksum or Validate' -Message $errorMessage
    }

    $psDrive = $null

    if ($PSBoundParameters.ContainsKey('Credential'))
    {
        $psDrive = Mount-PSDriveWithCredential -Path $Path -Credential $Credential
    }

    try
    {
        Assert-PathExistsAsLeaf -Path $Path
        Assert-DestinationDoesNotExistAsFile -Destination $Destination

        Write-Verbose -Message ($script:localizedData.SettingArchiveState -f $Path, $Destination)

        $expandArchiveToDestinationParameters = @{
            ArchiveSourcePath = $Path
            Destination = $Destination
            Force = $Force
        }

        $removeArchiveFromDestinationParameters = @{
            ArchiveSourcePath = $Path
            Destination = $Destination
        }

        if ($Validate)
        {
            $expandArchiveToDestinationParameters['Checksum'] = $Checksum
            $removeArchiveFromDestinationParameters['Checksum'] = $Checksum
        }

        if (Test-Path -LiteralPath $Destination)
        {
            Write-Verbose -Message ($script:localizedData.DestinationExists -f $Destination)

            if ($Ensure -eq 'Present')
            {
                Expand-ArchiveToDestination @expandArchiveToDestinationParameters
            }
            else
            {
                Remove-ArchiveFromDestination @removeArchiveFromDestinationParameters
            }
        }
        else
        {
            Write-Verbose -Message ($script:localizedData.DestinationDoesNotExist -f $Destination)

            if ($Ensure -eq 'Present')
            {
                Write-Verbose -Message ($script:localizedData.CreatingDirectoryAtDestination -f $Destination)

                $null = New-Item -Path $Destination -ItemType 'Directory'
                Expand-ArchiveToDestination @expandArchiveToDestinationParameters
            }
        }

        Write-Verbose -Message ($script:localizedData.ArchiveStateSet -f $Path, $Destination)
    }
    finally
    {
        if ($null -ne $psDrive)
        {
            Write-Verbose -Message ($script:localizedData.RemovingPSDrive -f $psDrive.Root)

            $null = Remove-PSDrive -Name $psDrive -Force -ErrorAction 'SilentlyContinue'
        }
    }
}

<#
    .SYNOPSIS
        Tests whether or not the archive (.zip) file at the specified path is expanded at the
        specified destination.

    .PARAMETER Path
        The path to the archive file that should or should not be expanded at the specified
        destination.

    .PARAMETER Destination
        The path where the archive file should or should not be expanded.

    .PARAMETER Ensure
        Specifies whether or not the archive file should be expanded to the specified destination.

        To test whether the archive file is expanded at the specified destination, specify this
        property as Present.
        To test whether the archive file is not expanded at the specified destination, specify this
        property as Absent.

        The default value is Present.

    .PARAMETER Validate
        Specifies whether or not to validate that a file at the destination with the same name as a
        file in the archive actually matches that corresponding file in the archive by the
        specified checksum method.

        If a file does not match it will be considered not present.

        The default value is false.

    .PARAMETER Checksum
        The Checksum method to use to validate whether or not a file at the destination with the
        same name as a file in the archive actually matches that corresponding file in the archive.

        An invalid argument exception will be thrown if Checksum is specified while Validate is
        specified as false.

        ModifiedDate will check that the LastWriteTime property of the file at the destination
        matches the LastWriteTime property of the file in the archive.
        CreatedDate will check that the CreationTime property of the file at the destination
        matches the CreationTime property of the file in the archive.
        SHA-1, SHA-256, and SHA-512 will check that the hash of the file at the destination by the
        specified SHA method matches the hash of the file in the archive by the specified SHA
        method.

        The default value is ModifiedDate.

    .PARAMETER Credential
        The credential of a user account with permissions to access the specified archive path and
        destination if needed.

    .PARAMETER Force
        Not used in Test-TargetResource.
#>
function Test-TargetResource
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination,

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present',

        [Parameter()]
        [System.Boolean]
        $Validate = $false,

        [Parameter()]
        [ValidateSet('SHA-1', 'SHA-256', 'SHA-512', 'CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum = 'ModifiedDate',

        [Parameter()]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $Credential,

        [Parameter()]
        [System.Boolean]
        $Force = $false
    )

    $getTargetResourceParameters = @{
        Path = $Path
        Destination = $Destination
    }

    $optionalGetTargetResourceParameters = @( 'Validate', 'Checksum', 'Credential' )

    foreach ($optionalGetTargetResourceParameter in $optionalGetTargetResourceParameters)
    {
        if ($PSBoundParameters.ContainsKey($optionalGetTargetResourceParameter))
        {
            $getTargetResourceParameters[$optionalGetTargetResourceParameter] = $PSBoundParameters[$optionalGetTargetResourceParameter]
        }
    }

    $archiveResourceState = Get-TargetResource @getTargetResourceParameters

    Write-Verbose -Message ($script:localizedData.TestingArchiveState -f $Path, $Destination)

    $archiveInDesiredState = $archiveResourceState.Ensure -ieq $Ensure

    return $archiveInDesiredState
}

<#
    .SYNOPSIS
        Creates a new GUID.
        This is a wrapper function for unit testing.
#>
function New-Guid
{
    [OutputType([System.Guid])]
    [CmdletBinding()]
    param ()

    return [System.Guid]::NewGuid()
}

<#
    .SYNOPSIS
        Mounts a PSDrive to access the specified path with the permissions granted by the specified
        credential.

    .PARAMETER Path
        The path to which to mount a PSDrive.

    .PARAMETER Credential
        The credential of the user account with permissions to access the specified path.
#>
function Mount-PSDriveWithCredential
{
    [OutputType([System.Management.Automation.PSDriveInfo])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $Credential
    )

    $newPSDrive = $null

    if (Test-Path -LiteralPath $Path -ErrorAction 'SilentlyContinue')
    {
        Write-Verbose -Message ($script:localizedData.PathAccessiblePSDriveNotNeeded -f $Path)
    }
    else
    {
        $pathIsADirectory = $Path.EndsWith('\')

        if ($pathIsADirectory)
        {
            $pathToPSDriveRoot = $Path
        }
        else
        {
            $lastIndexOfBackslash = $Path.LastIndexOf('\')
            $pathDoesNotContainADirectory = $lastIndexOfBackslash -eq -1

            if ($pathDoesNotContainADirectory)
            {
                $errorMessage = $script:localizedData.PathDoesNotContainValidPSDriveRoot -f $Path
                New-InvalidArgumentException -ArgumentName 'Path' -Message $errorMessage
            }
            else
            {
                $pathToPSDriveRoot = $Path.Substring(0, $lastIndexOfBackslash)
            }
        }

        $newPSDriveParameters = @{
            Name = New-Guid
            PSProvider = 'FileSystem'
            Root = $pathToPSDriveRoot
            Scope = 'Script'
            Credential = $Credential
        }

        try
        {
            Write-Verbose -Message ($script:localizedData.CreatingPSDrive -f $pathToPSDriveRoot, $Credential.UserName)
            $newPSDrive = New-PSDrive @newPSDriveParameters
        }
        catch
        {
            $errorMessage = $script:localizedData.ErrorCreatingPSDrive -f $pathToPSDriveRoot, $Credential.UserName
            New-InvalidOperationException -Message $errorMessage -ErrorRecord $_
        }
    }

    return $newPSDrive
}

<#
    .SYNOPSIS
        Throws an invalid argument exception if the specified path does not exist or is not a path
        leaf.

    .PARAMETER Path
        The path to assert.
#>
function Assert-PathExistsAsLeaf
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path
    )

    $pathExistsAsLeaf = Test-Path -LiteralPath $Path -PathType 'Leaf' -ErrorAction 'SilentlyContinue'

    if (-not $pathExistsAsLeaf)
    {
        $errorMessage = $script:localizedData.PathDoesNotExistAsLeaf -f $Path
        New-InvalidArgumentException -ArgumentName 'Path' -Message $errorMessage
    }
}

<#
    .SYNOPSIS
        Throws an invalid argument exception if the specified destination path already exists as a
        file.

    .PARAMETER Destination
        The destination path to assert.
#>
function Assert-DestinationDoesNotExistAsFile
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination
    )

    $itemAtDestination = Get-Item -LiteralPath $Destination -ErrorAction 'SilentlyContinue'

    $itemAtDestinationExists = $null -ne $itemAtDestination
    $itemAtDestinationIsFile = $itemAtDestination -is [System.IO.FileInfo]

    if ($itemAtDestinationExists -and $itemAtDestinationIsFile)
    {
        $errorMessage = $script:localizedData.DestinationExistsAsFile -f $Destination
        New-InvalidArgumentException -ArgumentName 'Destination' -Message $errorMessage
    }
}

<#
    .SYNOPSIS
        Opens the archive at the given path.
        This is a wrapper function for unit testing.

    .PARAMETER Path
        The path to the archive to open.
#>
function Open-Archive
{
    [OutputType([System.IO.Compression.ZipArchive])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Path
    )

    Write-Verbose -Message ($script:localizedData.OpeningArchive -f $Path)

    try
    {
        $archive = [System.IO.Compression.ZipFile]::OpenRead($Path)
    }
    catch
    {
        $errorMessage = $script:localizedData.ErrorOpeningArchive -f $Path
        New-InvalidOperationException -Message $errorMessage -ErrorRecord $_
    }

    return $archive
}

<#
    .SYNOPSIS
        Closes the specified archive.
        This is a wrapper function for unit testing.

    .PARAMETER Archive
        The archive to close.
#>
function Close-Archive
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchive]
        $Archive
    )

    Write-Verbose -Message ($script:localizedData.ClosingArchive -f $Path)
    $null = $Archive.Dispose()
}

<#
    .SYNOPSIS
        Retrieves the archive entries from the specified archive.
        This is a wrapper function for unit testing.

    .PARAMETER Archive
        The archive of which to retrieve the archive entries.
#>
function Get-ArchiveEntries
{
    [OutputType([System.IO.Compression.ZipArchiveEntry[]])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchive]
        $Archive
    )

    return $Archive.Entries
}

<#
    .SYNOPSIS
        Retrieves the full name of the specified archive entry.
        This is a wrapper function for unit testing.

    .PARAMETER ArchiveEntry
        The archive entry to retrieve the full name of.
#>
function Get-ArchiveEntryFullName
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchiveEntry]
        $ArchiveEntry
    )

    return $ArchiveEntry.FullName
}

<#
    .SYNOPSIS
        Opens the specified archive entry.
        This is a wrapper function for unit testing.

    .PARAMETER ArchiveEntry
        The archive entry to open.
#>
function Open-ArchiveEntry
{
    [OutputType([System.IO.Stream])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchiveEntry]
        $ArchiveEntry
    )

    Write-Verbose -Message ($script:localizedData.OpeningArchiveEntry -f $ArchiveEntry.FullName)
    return $ArchiveEntry.Open()
}

<#
    .SYNOPSIS
        Closes the specified stream.
        This is a wrapper function for unit testing.

    .PARAMETER Stream
        The stream to close.
#>
function Close-Stream
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Stream]
        $Stream
    )

    $null = $Stream.Dispose()
}

<#
    .SYNOPSIS
        Tests if the given checksum method name is the name of a SHA checksum method.

    .PARAMETER Checksum
        The name of the checksum method to test.
#>
function Test-ChecksumIsSha
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Checksum
    )

    return ($Checksum.Length -ge 'SHA'.Length) -and ($Checksum.Substring(0, 3) -ieq 'SHA')
}

<#
    .SYNOPSIS
        Converts the specified DSC hash algorithm name (with a hyphen) to a PowerShell hash
        algorithm name (without a hyphen). The in-box PowerShell Get-FileHash cmdlet will only hash
        algorithm names without hypens.

    .PARAMETER DscHashAlgorithmName
        The DSC hash algorithm name to convert.
#>
function ConvertTo-PowerShellHashAlgorithmName
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $DscHashAlgorithmName
    )

    return $DscHashAlgorithmName.Replace('-', '')
}

<#
    .SYNOPSIS
        Tests if the hash of the specified file matches the hash of the specified archive entry
        using the specified hash algorithm.

    .PARAMETER FilePath
        The path to the file to test the hash of.

    .PARAMETER CacheEntry
        The cache entry to test the hash of.

    .PARAMETER HashAlgorithmName
        The name of the hash algorithm to use to retrieve the hashes of the file and archive entry.
#>
function Test-FileHashMatchesArchiveEntryHash
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $FilePath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchiveEntry]
        $ArchiveEntry,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $HashAlgorithmName
    )

    $archiveEntryFullName = Get-ArchiveEntryFullName -ArchiveEntry $ArchiveEntry

    Write-Verbose -Message ($script:localizedData.ComparingHashes -f $FilePath, $archiveEntryFullName, $HashAlgorithmName)

    $fileHashMatchesArchiveEntryHash = $false

    $powerShellHashAlgorithmName = ConvertTo-PowerShellHashAlgorithmName -DscHashAlgorithmName $HashAlgorithmName

    $openStreams = @()

    try
    {
        $archiveEntryStream = Open-ArchiveEntry -ArchiveEntry $ArchiveEntry
        $openStreams += $archiveEntryStream

        # The Open mode will open the file for reading without modifying the file
        $fileStreamMode = [System.IO.FileMode]::Open

        $fileStream = New-Object -TypeName 'System.IO.FileStream' -ArgumentList @( $FilePath, $fileStreamMode )
        $openStreams += $fileStream

        $fileHash = Get-FileHash -InputStream $fileStream -Algorithm $powerShellHashAlgorithmName
        $archiveEntryHash = Get-FileHash -InputStream $archiveEntryStream -Algorithm $powerShellHashAlgorithmName

        $hashAlgorithmsMatch = $fileHash.Algorithm -eq $archiveEntryHash.Algorithm
        $hashesMatch = $fileHash.Hash -eq $archiveEntryHash.Hash

        $fileHashMatchesArchiveEntryHash = $hashAlgorithmsMatch -and $hashesMatch
    }
    catch
    {
        $errorMessage = $script:localizedData.ErrorComparingHashes -f $FilePath, $archiveEntryFullName, $HashAlgorithmName
        New-InvalidOperationException -Message $errorMessage -ErrorRecord $_
    }
    finally
    {
        foreach ($openStream in $openStreams)
        {
            Close-Stream -Stream $openStream
        }
    }

    return $fileHashMatchesArchiveEntryHash
}

<#
    .SYNOPSIS
        Retrieves the timestamp of the specified file for the specified checksum method
        and returns it as a checksum.

    .PARAMETER File
        The file to retrieve the timestamp of.

    .PARAMETER Checksum
        The checksum method to retrieve the timestamp checksum for.

    .NOTES
        The returned string is file timestamp normalized to the format specified in
        ConvertTo-CheckSumFromDateTime.
#>
function Get-ChecksumFromFileTimestamp
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.FileInfo]
        $File,

        [Parameter(Mandatory = $true)]
        [ValidateSet('CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum
    )

    $timestamp = Get-TimestampForChecksum @PSBoundParameters

    return ConvertTo-ChecksumFromDateTime -Date $timestamp
}

<#
    .SYNOPSIS
        Retrieves the timestamp of the specified file for the specified checksum method.

    .PARAMETER File
        The file to retrieve the timestamp of.

    .PARAMETER Checksum
        The checksum method to retrieve the timestamp for.
#>
function Get-TimestampForChecksum
{
    [OutputType([System.DateTime])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.FileInfo]
        $File,

        [Parameter(Mandatory = $true)]
        [ValidateSet('CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum
    )

    if ($Checksum -ieq 'CreatedDate')
    {
        $relevantTimestamp = 'CreationTime'
    }
    elseif ($Checksum -ieq 'ModifiedDate')
    {
        $relevantTimestamp = 'LastWriteTime'
    }

    return Get-TimestampFromFile -File $File -Timestamp $relevantTimestamp
}

<#
    .SYNOPSIS
        Retrieves a timestamp of the specified file.

    .PARAMETER File
        The file to retrieve the timestamp from.

    .PARAMETER Timestamp
        The timestamp attribute to retrieve.
#>
function Get-TimestampFromFile
{
    [OutputType([System.Datetime])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.FileInfo]
        $File,

        [Parameter(Mandatory = $true)]
        [ValidateSet('CreationTime', 'LastWriteTime')]
        [System.String]
        $Timestamp
    )

    return $File.$Timestamp
}

<#
    .SYNOPSIS
        Converts a datetime object into the format used for a
        checksum.

    .PARAMETER Date
        The date to use to generate the checksum.

    .NOTES
        The returned date is normalized to the General (G) date format.
        https://technet.microsoft.com/en-us/library/ee692801.aspx

        Because the General (G) is localization specific a non-localization
        specific format such as ISO9660 could be used in future.
#>
function ConvertTo-ChecksumFromDateTime
{
    [OutputType([System.String])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.DateTime]
        $Date
    )

    return Get-Date -Date $Date -Format 'G'
}

<#
    .SYNOPSIS
        Retrieves the last write time of the specified archive entry.
        This is a wrapper function for unit testing.

    .PARAMETER ArchiveEntry
        The archive entry to retrieve the last write time of.
#>
function Get-ArchiveEntryLastWriteTime
{
    [OutputType([System.DateTime])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchiveEntry]
        $ArchiveEntry
    )

    return $ArchiveEntry.LastWriteTime.DateTime
}

<#
    .SYNOPSIS
        Tests if the specified file matches the specified archive entry based on the specified
        checksum method.

    .PARAMETER File
        The file to test against the specified archive entry.

    .PARAMETER ArchiveEntry
        The archive entry to test against the specified file.

    .PARAMETER Checksum
        The checksum method to use to determine whether or not the specified file matches the
        specified archive entry.
#>
function Test-FileMatchesArchiveEntryByChecksum
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.FileInfo]
        $File,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchiveEntry]
        $ArchiveEntry,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Checksum
    )

    $archiveEntryFullName = Get-ArchiveEntryFullName -ArchiveEntry $ArchiveEntry

    Write-Verbose -Message ($script:localizedData.TestingIfFileMatchesArchiveEntryByChecksum -f $File.FullName, $archiveEntryFullName, $Checksum)

    $fileMatchesArchiveEntry = $false

    if (Test-ChecksumIsSha -Checksum $Checksum)
    {
        $fileHashMatchesArchiveEntryHash = Test-FileHashMatchesArchiveEntryHash -FilePath $File.FullName -ArchiveEntry $ArchiveEntry -HashAlgorithmName $Checksum

        if ($fileHashMatchesArchiveEntryHash)
        {
            Write-Verbose -Message ($script:localizedData.FileMatchesArchiveEntryByChecksum -f $File.FullName, $archiveEntryFullName, $Checksum)

            $fileMatchesArchiveEntry = $true
        }
        else
        {
            Write-Verbose -Message ($script:localizedData.FileDoesNotMatchArchiveEntryByChecksum -f $File.FullName, $archiveEntryFullName, $Checksum)
        }
    }
    else
    {
        $fileTimestampForChecksum = Get-ChecksumFromFileTimestamp -File $File -Checksum $Checksum

        $archiveEntryLastWriteTime = Get-ArchiveEntryLastWriteTime -ArchiveEntry $ArchiveEntry
        $archiveEntryLastWriteTimeChecksum = ConvertTo-CheckSumFromDateTime -Date $archiveEntryLastWriteTime

        if ($fileTimestampForChecksum.Equals($archiveEntryLastWriteTimeChecksum))
        {
            Write-Verbose -Message ($script:localizedData.FileMatchesArchiveEntryByChecksum -f $File.FullName, $archiveEntryFullName, $Checksum)

            $fileMatchesArchiveEntry = $true
        }
        else
        {
            Write-Verbose -Message ($script:localizedData.FileDoesNotMatchArchiveEntryByChecksum -f $File.FullName, $archiveEntryFullName, $Checksum)
        }
    }

    return $fileMatchesArchiveEntry
}

<#
    .SYNOPSIS
        Tests if the given archive entry name represents a directory.

    .PARAMETER ArchiveEntryName
        The archive entry name to test.
#>
function Test-ArchiveEntryIsDirectory
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $ArchiveEntryName
    )

    return $ArchiveEntryName.EndsWith('\') -or $ArchiveEntryName.EndsWith('/')
}

<#
    .SYNOPSIS
        Tests if the specified archive exists in its expanded form at the destination.

    .PARAMETER Archive
        The archive to test for existence at the specified destination.

    .PARAMETER Destination
        The path to the destination to check for the presence of the expanded form of the specified
        archive.

    .PARAMETER Checksum
        The checksum method to use to determine whether a file in the archive matches a file at the
        destination.

        If not provided, only the existence of the items in the archive will be checked.
#>
function Test-ArchiveExistsAtDestination
{
    [OutputType([System.Boolean])]
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $ArchiveSourcePath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination,

        [Parameter()]
        [ValidateSet('SHA-1', 'SHA-256', 'SHA-512', 'CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum
    )

    Write-Verbose -Message ($script:localizedData.TestingIfArchiveExistsAtDestination -f $Destination)

    $archiveExistsAtDestination = $true

    $archive = Open-Archive -Path $ArchiveSourcePath

    try
    {
        $archiveEntries = Get-ArchiveEntries -Archive $archive

        foreach ($archiveEntry in $archiveEntries)
        {
            $archiveEntryFullName = Get-ArchiveEntryFullName -ArchiveEntry $archiveEntry
            $archiveEntryPathAtDestination = Join-Path -Path $Destination -ChildPath $archiveEntryFullName

            $archiveEntryItemAtDestination = Get-Item -LiteralPath $archiveEntryPathAtDestination -ErrorAction 'SilentlyContinue'

            if ($null -eq $archiveEntryItemAtDestination)
            {
                Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameDoesNotExist -f $archiveEntryPathAtDestination)

                $archiveExistsAtDestination = $false
                break
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameExists -f $archiveEntryPathAtDestination)

                if (Test-ArchiveEntryIsDirectory -ArchiveEntryName $archiveEntryFullName)
                {
                    if (-not ($archiveEntryItemAtDestination -is [System.IO.DirectoryInfo]))
                    {
                        Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameIsNotDirectory -f $archiveEntryPathAtDestination)

                        $archiveExistsAtDestination = $false
                        break
                    }
                }
                else
                {
                    if ($archiveEntryItemAtDestination -is [System.IO.FileInfo])
                    {
                        if ($PSBoundParameters.ContainsKey('Checksum'))
                        {
                            if (-not (Test-FileMatchesArchiveEntryByChecksum -File $archiveEntryItemAtDestination -ArchiveEntry $archiveEntry -Checksum $Checksum))
                            {
                                $archiveExistsAtDestination = $false
                                break
                            }
                        }
                    }
                    else
                    {
                        Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameIsNotFile -f $archiveEntryPathAtDestination)

                        $archiveExistsAtDestination = $false
                        break
                    }
                }
            }
        }
    }
    finally
    {
        Close-Archive -Archive $archive
    }

    if ($archiveExistsAtDestination)
    {
        Write-Verbose -Message ($script:localizedData.ArchiveExistsAtDestination -f $ArchiveSourcePath, $Destination)
    }
    else
    {
        Write-Verbose -Message ($script:localizedData.ArchiveDoesNotExistAtDestination -f $ArchiveSourcePath, $Destination)
    }

    return $archiveExistsAtDestination
}

<#
    .SYNOPSIS
        Copies the contents of the specified source stream to the specified destination stream.
        This is a wrapper function for unit testing.

    .PARAMETER SourceStream
        The stream to copy from.

    .PARAMETER DestinationStream
        The stream to copy to.
#>
function Copy-FromStreamToStream
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Stream]
        $SourceStream,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Stream]
        $DestinationStream
    )

    $null = $SourceStream.CopyTo($DestinationStream)
}

<#
    .SYNOPSIS
        Copies the specified archive entry to the specified destination path.

    .PARAMETER ArchiveEntry
        The archive entry to copy to the destination.

    .PARAMETER DestinationPath
        The destination file path to copy the archive entry to.
#>
function Copy-ArchiveEntryToDestination
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.IO.Compression.ZipArchiveEntry]
        $ArchiveEntry,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $DestinationPath
    )

    $archiveEntryFullName = Get-ArchiveEntryFullName -ArchiveEntry $ArchiveEntry

    Write-Verbose -Message ($script:localizedData.CopyingArchiveEntryToDestination -f $archiveEntryFullName, $DestinationPath)

    if (Test-ArchiveEntryIsDirectory -ArchiveEntryName $archiveEntryFullName)
    {
        Write-Verbose -Message ($script:localizedData.CreatingArchiveEntryDirectory -f $DestinationPath)

        $null = New-Item -Path $DestinationPath -ItemType 'Directory'
    }
    else
    {
        $openStreams = @()

        try
        {
            $archiveEntryStream = Open-ArchiveEntry -ArchiveEntry $ArchiveEntry
            $openStreams += $archiveEntryStream

            # The Create mode will create a new file if it does not exist or overwrite the file if it already exists
            $destinationStreamMode = [System.IO.FileMode]::Create

            $destinationStream = New-Object -TypeName 'System.IO.FileStream' -ArgumentList @( $DestinationPath, $destinationStreamMode )
            $openStreams += $destinationStream

            Copy-FromStreamToStream -SourceStream $archiveEntryStream -DestinationStream $destinationStream
        }
        catch
        {
            $errorMessage = $script:localizedData.ErrorCopyingFromArchiveToDestination -f $DestinationPath
            New-InvalidOperationException -Message $errorMessage -ErrorRecord $_
        }
        finally
        {
            foreach ($openStream in $openStreams)
            {
                Close-Stream -Stream $openStream
            }
        }

        $null = New-Object -TypeName 'System.IO.FileInfo' -ArgumentList @( $DestinationPath )

        $updatedTimestamp = Get-ArchiveEntryLastWriteTime -ArchiveEntry $ArchiveEntry

        $null = Set-ItemProperty -LiteralPath $DestinationPath -Name 'LastWriteTime' -Value $updatedTimestamp
        $null = Set-ItemProperty -LiteralPath $DestinationPath -Name 'LastAccessTime' -Value $updatedTimestamp
        $null = Set-ItemProperty -LiteralPath $DestinationPath -Name 'CreationTime' -Value $updatedTimestamp
    }
}

<#
    .SYNOPSIS
        Expands the archive at the specified source path to the specified destination path.

    .PARAMETER ArchiveSourcePath
        The source path of the archive to expand to the specified destination path.

    .PARAMETER Destination
        The destination path at which to expand the archive at the specified source path.

    .PARAMETER Checksum
        The checksum method to use to determine if a file at the destination already matches a file
        in the archive.

    .PARAMETER Force
        Specifies whether or not to overwrite files that exist at the destination but do not match
        the file of the same name in the archive based on the specified checksum method.
#>
function Expand-ArchiveToDestination
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $ArchiveSourcePath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination,

        [Parameter()]
        [ValidateSet('SHA-1', 'SHA-256', 'SHA-512', 'CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum,

        [Parameter()]
        [System.Boolean]
        $Force = $false
    )

    Write-Verbose -Message ($script:localizedData.ExpandingArchiveToDestination -f $ArchiveSourcePath, $Destination)

    $archive = Open-Archive -Path $ArchiveSourcePath

    try
    {
        $archiveEntries = Get-ArchiveEntries -Archive $archive

        foreach ($archiveEntry in $archiveEntries)
        {
            $archiveEntryFullName = Get-ArchiveEntryFullName -ArchiveEntry $archiveEntry
            $archiveEntryPathAtDestination = Join-Path -Path $Destination -ChildPath $archiveEntryFullName

            $archiveEntryIsDirectory = Test-ArchiveEntryIsDirectory -ArchiveEntryName $archiveEntryFullName

            $archiveEntryItemAtDestination = Get-Item -LiteralPath $archiveEntryPathAtDestination -ErrorAction 'SilentlyContinue'

            if ($null -eq $archiveEntryItemAtDestination)
            {
                Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameDoesNotExist -f $archiveEntryPathAtDestination)

                if (-not $archiveEntryIsDirectory)
                {
                    $parentDirectory = Split-Path -Path $archiveEntryPathAtDestination -Parent

                    if (-not (Test-Path -Path $parentDirectory))
                    {
                        Write-Verbose -Message ($script:localizedData.CreatingParentDirectory -f $parentDirectory)

                        $null = New-Item -Path $parentDirectory -ItemType 'Directory'
                    }
                }

                Copy-ArchiveEntryToDestination -ArchiveEntry $archiveEntry -DestinationPath $archiveEntryPathAtDestination
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameExists -f $archiveEntryPathAtDestination)

                $overwriteArchiveEntry = $true

                if ($archiveEntryIsDirectory)
                {
                    $overwriteArchiveEntry = -not ($archiveEntryItemAtDestination -is [System.IO.DirectoryInfo])
                }
                elseif ($archiveEntryItemAtDestination -is [System.IO.FileInfo])
                {
                    if ($PSBoundParameters.ContainsKey('Checksum'))
                    {
                        $overwriteArchiveEntry = -not (Test-FileMatchesArchiveEntryByChecksum -File $archiveEntryItemAtDestination -ArchiveEntry $archiveEntry -Checksum $Checksum)
                    }
                    else
                    {
                        $overwriteArchiveEntry = $false
                    }
                }

                if ($overwriteArchiveEntry)
                {
                    if ($Force)
                    {
                        Write-Verbose -Message ($script:localizedData.OverwritingItem -f $archiveEntryPathAtDestination)

                        $null = Remove-Item -LiteralPath $archiveEntryPathAtDestination
                        Copy-ArchiveEntryToDestination -ArchiveEntry $archiveEntry -DestinationPath $archiveEntryPathAtDestination
                    }
                    else
                    {
                        New-InvalidOperationException -Message ($script:localizedData.ForceNotSpecifiedToOverwriteItem -f $archiveEntryPathAtDestination, $archiveEntryFullName)
                    }
                }
            }
        }
    }
    finally
    {
        Close-Archive -Archive $archive
    }
}

<#
    .SYNOPSIS
        Removes the specified directory from the specified destination path.

    .PARAMETER Directory
        The partial path under the destination path of the directory to remove.

    .PARAMETER Destination
        The destination from which to remove the directory.
#>
function Remove-DirectoryFromDestination
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String[]]
        $Directory,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination
    )

    # Sort-Object requires the use of a pipe to function properly
    $Directory = $Directory | Sort-Object -Descending -Unique

    foreach ($directoryToRemove in $Directory)
    {
        $directoryPathAtDestination = Join-Path -Path $Destination -ChildPath $directoryToRemove
        $directoryExists = Test-Path -LiteralPath $directoryPathAtDestination -PathType 'Container'

        if ($directoryExists)
        {
            $directoryChildItems = Get-ChildItem -LiteralPath $directoryPathAtDestination -ErrorAction 'SilentlyContinue'
            $directoryIsEmpty = $null -eq $directoryChildItems

            if ($directoryIsEmpty)
            {
                Write-Verbose -Message ($script:localizedData.RemovingDirectory -f $directoryPathAtDestination)

                $null = Remove-Item -LiteralPath $directoryPathAtDestination
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.DirectoryIsNotEmpty -f $directoryPathAtDestination)
            }
        }
    }
}

<#
    .SYNOPSIS
        Removes the specified archive from the specified destination.

    .PARAMETER Archive
        The archive to remove from the specified destination.

    .PARAMETER Destination
        The path to the destination to remove the specified archive from.

    .PARAMETER Checksum
        The checksum method to use to determine whether a file in the archive matches a file at the
        destination.

        If not provided, only the existence of the items in the archive will be checked.
#>
function Remove-ArchiveFromDestination
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $ArchiveSourcePath,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Destination,

        [Parameter()]
        [ValidateSet('SHA-1', 'SHA-256', 'SHA-512', 'CreatedDate', 'ModifiedDate')]
        [System.String]
        $Checksum
    )

    Write-Verbose -Message ($script:localizedData.RemovingArchiveFromDestination -f $Destination)

    $archive = Open-Archive -Path $ArchiveSourcePath

    try
    {
        $directoriesToRemove = @()

        $archiveEntries = Get-ArchiveEntries -Archive $archive

        foreach ($archiveEntry in $archiveEntries)
        {
            $archiveEntryFullName = Get-ArchiveEntryFullName -ArchiveEntry $archiveEntry
            $archiveEntryPathAtDestination = Join-Path -Path $Destination -ChildPath $archiveEntryFullName

            $archiveEntryIsDirectory = Test-ArchiveEntryIsDirectory -ArchiveEntryName $archiveEntryFullName

            $itemAtDestination = Get-Item -LiteralPath $archiveEntryPathAtDestination -ErrorAction 'SilentlyContinue'

            if ($null -eq $itemAtDestination)
            {
                Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameDoesNotExist -f $archiveEntryPathAtDestination)
            }
            else
            {
                Write-Verbose -Message ($script:localizedData.ItemWithArchiveEntryNameExists -f $archiveEntryPathAtDestination)

                $itemAtDestinationIsDirectory = $itemAtDestination -is [System.IO.DirectoryInfo]
                $itemAtDestinationIsFile = $itemAtDestination -is [System.IO.FileInfo]

                $removeArchiveEntry = $false

                if ($archiveEntryIsDirectory -and $itemAtDestinationIsDirectory)
                {
                    $removeArchiveEntry = $true
                    $directoriesToRemove += $archiveEntryFullName

                }
                elseif ((-not $archiveEntryIsDirectory) -and $itemAtDestinationIsFile)
                {
                    $removeArchiveEntry = $true

                    if ($PSBoundParameters.ContainsKey('Checksum'))
                    {
                        $removeArchiveEntry = Test-FileMatchesArchiveEntryByChecksum -File $itemAtDestination -ArchiveEntry $archiveEntry -Checksum $Checksum
                    }

                    if ($removeArchiveEntry)
                    {
                        Write-Verbose -Message ($script:localizedData.RemovingFile -f $archiveEntryPathAtDestination)
                        $null = Remove-Item -LiteralPath $archiveEntryPathAtDestination
                    }
                }
                else
                {
                    Write-Verbose -Message ($script:localizedData.CouldNotRemoveItemOfIncorrectType -f $archiveEntryPathAtDestination, $archiveEntryFullName)
                }

                if ($removeArchiveEntry)
                {
                    $parentDirectory = Split-Path -Path $archiveEntryFullName -Parent

                    while (-not [System.String]::IsNullOrEmpty($parentDirectory))
                    {
                        $directoriesToRemove += $parentDirectory
                        $parentDirectory = Split-Path -Path $parentDirectory -Parent
                    }
                }
            }
        }

        if ($directoriesToRemove.Count -gt 0)
        {
            $null = Remove-DirectoryFromDestination -Directory $directoriesToRemove -Destination $Destination
        }

        Write-Verbose -Message ($script:localizedData.ArchiveRemovedFromDestination -f $Destination)

    }
    finally
    {
        Close-Archive -Archive $archive
    }
}

# SIG # Begin signature block
# MIIjYAYJKoZIhvcNAQcCoIIjUTCCI00CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCDw3CBgS5x9BXMm
# I11puLl6f30R3DZk8N/Ld768jdoL66CCHVkwggUaMIIEAqADAgECAhADBbuGIbCh
# Y1+/3q4SBOdtMA0GCSqGSIb3DQEBCwUAMHIxCzAJBgNVBAYTAlVTMRUwEwYDVQQK
# EwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xMTAvBgNV
# BAMTKERpZ2lDZXJ0IFNIQTIgQXNzdXJlZCBJRCBDb2RlIFNpZ25pbmcgQ0EwHhcN
# MjAwNTEyMDAwMDAwWhcNMjMwNjA4MTIwMDAwWjBXMQswCQYDVQQGEwJVUzERMA8G
# A1UECBMIVmlyZ2luaWExDzANBgNVBAcTBlZpZW5uYTERMA8GA1UEChMIZGJhdG9v
# bHMxETAPBgNVBAMTCGRiYXRvb2xzMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
# CgKCAQEAvL9je6vjv74IAbaY5rXqHxaNeNJO9yV0ObDg+kC844Io2vrHKGD8U5hU
# iJp6rY32RVprnAFrA4jFVa6P+sho7F5iSVAO6A+QZTHQCn7oquOefGATo43NAadz
# W2OWRro3QprMPZah0QFYpej9WaQL9w/08lVaugIw7CWPsa0S/YjHPGKQ+bYgI/kr
# EUrk+asD7lvNwckR6pGieWAyf0fNmSoevQBTV6Cd8QiUfj+/qWvLW3UoEX9ucOGX
# 2D8vSJxL7JyEVWTHg447hr6q9PzGq+91CO/c9DWFvNMjf+1c5a71fEZ54h1mNom/
# XoWZYoKeWhKnVdv1xVT1eEimibPEfQIDAQABo4IBxTCCAcEwHwYDVR0jBBgwFoAU
# WsS5eyoKo6XqcQPAYPkt9mV1DlgwHQYDVR0OBBYEFPDAoPu2A4BDTvsJ193ferHL
# 454iMA4GA1UdDwEB/wQEAwIHgDATBgNVHSUEDDAKBggrBgEFBQcDAzB3BgNVHR8E
# cDBuMDWgM6Axhi9odHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc2hhMi1hc3N1cmVk
# LWNzLWcxLmNybDA1oDOgMYYvaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NoYTIt
# YXNzdXJlZC1jcy1nMS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAwEwKjAoBggr
# BgEFBQcCARYcaHR0cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBBAEw
# gYQGCCsGAQUFBwEBBHgwdjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNl
# cnQuY29tME4GCCsGAQUFBzAChkJodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20v
# RGlnaUNlcnRTSEEyQXNzdXJlZElEQ29kZVNpZ25pbmdDQS5jcnQwDAYDVR0TAQH/
# BAIwADANBgkqhkiG9w0BAQsFAAOCAQEAj835cJUMH9Y2pBKspjznNJwcYmOxeBcH
# Ji+yK0y4bm+j44OGWH4gu/QJM+WjZajvkydJKoJZH5zrHI3ykM8w8HGbYS1WZfN4
# oMwi51jKPGZPw9neGS2PXrBcKjzb7rlQ6x74Iex+gyf8z1ZuRDitLJY09FEOh0BM
# LaLh+UvJ66ghmfIyjP/g3iZZvqwgBhn+01fObqrAJ+SagxJ/21xNQJchtUOWIlxR
# kuUn9KkuDYrMO70a2ekHODcAbcuHAGI8wzw4saK1iPPhVTlFijHS+7VfIt/d/18p
# MLHHArLQQqe1Z0mTfuL4M4xCUKpebkH8rI3Fva62/6osaXLD0ymERzCCBTAwggQY
# oAMCAQICEAQJGBtf1btmdVNDtW+VUAgwDQYJKoZIhvcNAQELBQAwZTELMAkGA1UE
# BhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2lj
# ZXJ0LmNvbTEkMCIGA1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4X
# DTEzMTAyMjEyMDAwMFoXDTI4MTAyMjEyMDAwMFowcjELMAkGA1UEBhMCVVMxFTAT
# BgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEx
# MC8GA1UEAxMoRGlnaUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBD
# QTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPjTsxx/DhGvZ3cH0wsx
# SRnP0PtFmbE620T1f+Wondsy13Hqdp0FLreP+pJDwKX5idQ3Gde2qvCchqXYJawO
# eSg6funRZ9PG+yknx9N7I5TkkSOWkHeC+aGEI2YSVDNQdLEoJrskacLCUvIUZ4qJ
# RdQtoaPpiCwgla4cSocI3wz14k1gGL6qxLKucDFmM3E+rHCiq85/6XzLkqHlOzEc
# z+ryCuRXu0q16XTmK/5sy350OTYNkO/ktU6kqepqCquE86xnTrXE94zRICUj6whk
# PlKWwfIPEvTFjg/BougsUfdzvL2FsWKDc0GCB+Q4i2pzINAPZHM8np+mM6n9Gd8l
# k9ECAwEAAaOCAc0wggHJMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYDVR0PAQH/BAQD
# AgGGMBMGA1UdJQQMMAoGCCsGAQUFBwMDMHkGCCsGAQUFBwEBBG0wazAkBggrBgEF
# BQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAChjdodHRw
# Oi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0Eu
# Y3J0MIGBBgNVHR8EejB4MDqgOKA2hjRodHRwOi8vY3JsNC5kaWdpY2VydC5jb20v
# RGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsMDqgOKA2hjRodHRwOi8vY3JsMy5k
# aWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsME8GA1UdIARI
# MEYwOAYKYIZIAYb9bAACBDAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdp
# Y2VydC5jb20vQ1BTMAoGCGCGSAGG/WwDMB0GA1UdDgQWBBRaxLl7KgqjpepxA8Bg
# +S32ZXUOWDAfBgNVHSMEGDAWgBRF66Kv9JLLgjEtUYunpyGd823IDzANBgkqhkiG
# 9w0BAQsFAAOCAQEAPuwNWiSz8yLRFcgsfCUpdqgdXRwtOhrE7zBh134LYP3DPQ/E
# r4v97yrfIFU3sOH20ZJ1D1G0bqWOWuJeJIFOEKTuP3GOYw4TS63XX0R58zYUBor3
# nEZOXP+QsRsHDpEV+7qvtVHCjSSuJMbHJyqhKSgaOnEoAjwukaPAJRHinBRHoXpo
# aK+bp1wgXNlxsQyPu6j4xRJon89Ay0BEpRPw5mQMJQhCMrI2iiQC/i9yfhzXSUWW
# 6Fkd6fp0ZGuy62ZD2rOwjNXpDd32ASDOmTFjPQgaGLOBm0/GkxAG/AeB+ova+YJJ
# 92JuoVP6EpQYhS6SkepobEQysmah5xikmmRR7zCCBY0wggR1oAMCAQICEA6bGI75
# 0C3n79tQ4ghAGFowDQYJKoZIhvcNAQEMBQAwZTELMAkGA1UEBhMCVVMxFTATBgNV
# BAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEkMCIG
# A1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4XDTIyMDgwMTAwMDAw
# MFoXDTMxMTEwOTIzNTk1OVowYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGln
# aUNlcnQgVHJ1c3RlZCBSb290IEc0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAv+aQc2jeu+RdSjwwIjBpM+zCpyUuySE98orYWcLhKac9WKt2ms2uexuE
# DcQwH/MbpDgW61bGl20dq7J58soR0uRf1gU8Ug9SH8aeFaV+vp+pVxZZVXKvaJNw
# wrK6dZlqczKU0RBEEC7fgvMHhOZ0O21x4i0MG+4g1ckgHWMpLc7sXk7Ik/ghYZs0
# 6wXGXuxbGrzryc/NrDRAX7F6Zu53yEioZldXn1RYjgwrt0+nMNlW7sp7XeOtyU9e
# 5TXnMcvak17cjo+A2raRmECQecN4x7axxLVqGDgDEI3Y1DekLgV9iPWCPhCRcKtV
# gkEy19sEcypukQF8IUzUvK4bA3VdeGbZOjFEmjNAvwjXWkmkwuapoGfdpCe8oU85
# tRFYF/ckXEaPZPfBaYh2mHY9WV1CdoeJl2l6SPDgohIbZpp0yt5LHucOY67m1O+S
# kjqePdwA5EUlibaaRBkrfsCUtNJhbesz2cXfSwQAzH0clcOP9yGyshG3u3/y1Yxw
# LEFgqrFjGESVGnZifvaAsPvoZKYz0YkH4b235kOkGLimdwHhD5QMIR2yVCkliWzl
# DlJRR3S+Jqy2QXXeeqxfjT/JvNNBERJb5RBQ6zHFynIWIgnffEx1P2PsIV/EIFFr
# b7GrhotPwtZFX50g/KEexcCPorF+CiaZ9eRpL5gdLfXZqbId5RsCAwEAAaOCATow
# ggE2MA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFOzX44LScV1kTN8uZz/nupiu
# HA9PMB8GA1UdIwQYMBaAFEXroq/0ksuCMS1Ri6enIZ3zbcgPMA4GA1UdDwEB/wQE
# AwIBhjB5BggrBgEFBQcBAQRtMGswJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRp
# Z2ljZXJ0LmNvbTBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQu
# Y29tL0RpZ2lDZXJ0QXNzdXJlZElEUm9vdENBLmNydDBFBgNVHR8EPjA8MDqgOKA2
# hjRodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290
# Q0EuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQwFAAOCAQEAcKC/
# Q1xV5zhfoKN0Gz22Ftf3v1cHvZqsoYcs7IVeqRq7IviHGmlUIu2kiHdtvRoU9BNK
# ei8ttzjv9P+Aufih9/Jy3iS8UgPITtAq3votVs/59PesMHqai7Je1M/RQ0SbQyHr
# lnKhSLSZy51PpwYDE3cnRNTnf+hZqPC/Lwum6fI0POz3A8eHqNJMQBk1RmppVLC4
# oVaO7KTVPeix3P0c2PR3WlxUjG/voVA9/HYJaISfb8rbII01YBwCA8sgsKxYoA5A
# Y8WYIsGyWfVVa88nq2x2zm8jLfR+cWojayL/ErhULSd+2DrZ8LaHlv1b0VysGMNN
# n3O3AamfV6peKOK5lDCCBq4wggSWoAMCAQICEAc2N7ckVHzYR6z9KGYqXlswDQYJ
# KoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IElu
# YzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGlnaUNlcnQg
# VHJ1c3RlZCBSb290IEc0MB4XDTIyMDMyMzAwMDAwMFoXDTM3MDMyMjIzNTk1OVow
# YzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQD
# EzJEaWdpQ2VydCBUcnVzdGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGlu
# ZyBDQTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMaGNQZJs8E9cklR
# VcclA8TykTepl1Gh1tKD0Z5Mom2gsMyD+Vr2EaFEFUJfpIjzaPp985yJC3+dH54P
# Mx9QEwsmc5Zt+FeoAn39Q7SE2hHxc7Gz7iuAhIoiGN/r2j3EF3+rGSs+QtxnjupR
# PfDWVtTnKC3r07G1decfBmWNlCnT2exp39mQh0YAe9tEQYncfGpXevA3eZ9drMvo
# hGS0UvJ2R/dhgxndX7RUCyFobjchu0CsX7LeSn3O9TkSZ+8OpWNs5KbFHc02DVzV
# 5huowWR0QKfAcsW6Th+xtVhNef7Xj3OTrCw54qVI1vCwMROpVymWJy71h6aPTnYV
# VSZwmCZ/oBpHIEPjQ2OAe3VuJyWQmDo4EbP29p7mO1vsgd4iFNmCKseSv6De4z6i
# c/rnH1pslPJSlRErWHRAKKtzQ87fSqEcazjFKfPKqpZzQmiftkaznTqj1QPgv/Ci
# PMpC3BhIfxQ0z9JMq++bPf4OuGQq+nUoJEHtQr8FnGZJUlD0UfM2SU2LINIsVzV5
# K6jzRWC8I41Y99xh3pP+OcD5sjClTNfpmEpYPtMDiP6zj9NeS3YSUZPJjAw7W4oi
# qMEmCPkUEBIDfV8ju2TjY+Cm4T72wnSyPx4JduyrXUZ14mCjWAkBKAAOhFTuzuld
# yF4wEr1GnrXTdrnSDmuZDNIztM2xAgMBAAGjggFdMIIBWTASBgNVHRMBAf8ECDAG
# AQH/AgEAMB0GA1UdDgQWBBS6FtltTYUvcyl2mi91jGogj57IbzAfBgNVHSMEGDAW
# gBTs1+OC0nFdZEzfLmc/57qYrhwPTzAOBgNVHQ8BAf8EBAMCAYYwEwYDVR0lBAww
# CgYIKwYBBQUHAwgwdwYIKwYBBQUHAQEEazBpMCQGCCsGAQUFBzABhhhodHRwOi8v
# b2NzcC5kaWdpY2VydC5jb20wQQYIKwYBBQUHMAKGNWh0dHA6Ly9jYWNlcnRzLmRp
# Z2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRSb290RzQuY3J0MEMGA1UdHwQ8MDow
# OKA2oDSGMmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRS
# b290RzQuY3JsMCAGA1UdIAQZMBcwCAYGZ4EMAQQCMAsGCWCGSAGG/WwHATANBgkq
# hkiG9w0BAQsFAAOCAgEAfVmOwJO2b5ipRCIBfmbW2CFC4bAYLhBNE88wU86/GPvH
# UF3iSyn7cIoNqilp/GnBzx0H6T5gyNgL5Vxb122H+oQgJTQxZ822EpZvxFBMYh0M
# CIKoFr2pVs8Vc40BIiXOlWk/R3f7cnQU1/+rT4osequFzUNf7WC2qk+RZp4snuCK
# rOX9jLxkJodskr2dfNBwCnzvqLx1T7pa96kQsl3p/yhUifDVinF2ZdrM8HKjI/rA
# J4JErpknG6skHibBt94q6/aesXmZgaNWhqsKRcnfxI2g55j7+6adcq/Ex8HBanHZ
# xhOACcS2n82HhyS7T6NJuXdmkfFynOlLAlKnN36TU6w7HQhJD5TNOXrd/yVjmScs
# PT9rp/Fmw0HNT7ZAmyEhQNC3EyTN3B14OuSereU0cZLXJmvkOHOrpgFPvT87eK1M
# rfvElXvtCl8zOYdBeHo46Zzh3SP9HSjTx/no8Zhf+yvYfvJGnXUsHicsJttvFXse
# GYs2uJPU5vIXmVnKcPA3v5gA3yAWTyf7YGcWoWa63VXAOimGsJigK+2VQbc61RWY
# MbRiCQ8KvYHZE/6/pNHzV9m8BPqC3jLfBInwAM1dwvnQI38AC+R2AibZ8GV2QqYp
# hwlHK+Z/GqSFD/yYlvZVVCsfgPrA8g4r5db7qS9EFUrnEw4d2zc4GqEr9u3WfPww
# ggbAMIIEqKADAgECAhAMTWlyS5T6PCpKPSkHgD1aMA0GCSqGSIb3DQEBCwUAMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwHhcNMjIwOTIxMDAwMDAwWhcNMzMxMTIxMjM1OTU5WjBGMQswCQYDVQQGEwJV
# UzERMA8GA1UEChMIRGlnaUNlcnQxJDAiBgNVBAMTG0RpZ2lDZXJ0IFRpbWVzdGFt
# cCAyMDIyIC0gMjCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAM/spSY6
# xqnya7uNwQ2a26HoFIV0MxomrNAcVR4eNm28klUMYfSdCXc9FZYIL2tkpP0GgxbX
# kZI4HDEClvtysZc6Va8z7GGK6aYo25BjXL2JU+A6LYyHQq4mpOS7eHi5ehbhVsbA
# umRTuyoW51BIu4hpDIjG8b7gL307scpTjUCDHufLckkoHkyAHoVW54Xt8mG8qjoH
# ffarbuVm3eJc9S/tjdRNlYRo44DLannR0hCRRinrPibytIzNTLlmyLuqUDgN5YyU
# XRlav/V7QG5vFqianJVHhoV5PgxeZowaCiS+nKrSnLb3T254xCg/oxwPUAY3ugjZ
# Naa1Htp4WB056PhMkRCWfk3h3cKtpX74LRsf7CtGGKMZ9jn39cFPcS6JAxGiS7uY
# v/pP5Hs27wZE5FX/NurlfDHn88JSxOYWe1p+pSVz28BqmSEtY+VZ9U0vkB8nt9Kr
# FOU4ZodRCGv7U0M50GT6Vs/g9ArmFG1keLuY/ZTDcyHzL8IuINeBrNPxB9Thvdld
# S24xlCmL5kGkZZTAWOXlLimQprdhZPrZIGwYUWC6poEPCSVT8b876asHDmoHOWIZ
# ydaFfxPZjXnPYsXs4Xu5zGcTB5rBeO3GiMiwbjJ5xwtZg43G7vUsfHuOy2SJ8bHE
# uOdTXl9V0n0ZKVkDTvpd6kVzHIR+187i1Dp3AgMBAAGjggGLMIIBhzAOBgNVHQ8B
# Af8EBAMCB4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAg
# BgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZ
# bU2FL3MpdpovdYxqII+eyG8wHQYDVR0OBBYEFGKK3tBh/I8xFO2XC809KpQU31Kc
# MFoGA1UdHwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdp
# Q2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAG
# CCsGAQUFBwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2Vy
# dC5jb20wWAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9E
# aWdpQ2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQw
# DQYJKoZIhvcNAQELBQADggIBAFWqKhrzRvN4Vzcw/HXjT9aFI/H8+ZU5myXm93KK
# mMN31GT8Ffs2wklRLHiIY1UJRjkA/GnUypsp+6M/wMkAmxMdsJiJ3HjyzXyFzVOd
# r2LiYWajFCpFh0qYQitQ/Bu1nggwCfrkLdcJiXn5CeaIzn0buGqim8FTYAnoo7id
# 160fHLjsmEHw9g6A++T/350Qp+sAul9Kjxo6UrTqvwlJFTU2WZoPVNKyG39+Xgmt
# dlSKdG3K0gVnK3br/5iyJpU4GYhEFOUKWaJr5yI+RCHSPxzAm+18SLLYkgyRTzxm
# lK9dAlPrnuKe5NMfhgFknADC6Vp0dQ094XmIvxwBl8kZI4DXNlpflhaxYwzGRkA7
# zl011Fk+Q5oYrsPJy8P7mxNfarXH4PMFw1nfJ2Ir3kHJU7n/NBBn9iYymHv+XEKU
# gZSCnawKi8ZLFUrTmJBFYDOA4CPe+AOk9kVH5c64A0JH6EE2cXet/aLol3ROLtoe
# HYxayB6a1cLwxiKoT5u92ByaUcQvmvZfpyeXupYuhVfAYOd4Vn9q78KVmksRAsiC
# nMkaBXy6cbVOepls9Oie1FqYyJ+/jbsYXEP10Cro4mLueATbvdH7WwqocH7wl4R4
# 4wgDXUcsY6glOJcB0j862uXl9uab3H4szP8XTE0AotjWAQ64i+7m4HJViSwnGWH2
# dwGMMYIFXTCCBVkCAQEwgYYwcjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTExMC8GA1UEAxMoRGln
# aUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBDQQIQAwW7hiGwoWNf
# v96uEgTnbTANBglghkgBZQMEAgEFAKCBhDAYBgorBgEEAYI3AgEMMQowCKACgACh
# AoAAMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAM
# BgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCDP4OzSI5A9xKDbbwWqBu4Uujrp
# K2ILmwQIaS64x80FCzANBgkqhkiG9w0BAQEFAASCAQCHkDH5s1kd5xlmHHYX//qx
# LdQ+gJUg3x78OigFIP27Q60aTARo2R/SiGXZT1SPggeceDVP3dx6veAhR+oJTSXm
# BrzqHwiKsladx85ShkPuePTR3eXiO+noS6bjQki5TyyE8p9FAGx4XDOcX7Dy8t4z
# 1guOusnHVNWFwPXIUMALhw8phcsrVr+Y8auOIBIAhZdMQGgabGVslbDWsgbfyDVU
# D88zGDyUpj0heeKrjGA+qXpJXRf5+M/XObBGpBxGZ204gtomQnNpT6SKZwWCYNIH
# CbPelzBfc6dXJ7jraYxoxyBYMRBOMUAO8lJEqSF4Dgh/HJ7cFlqGM2l1jn9dfvHQ
# oYIDIDCCAxwGCSqGSIb3DQEJBjGCAw0wggMJAgEBMHcwYzELMAkGA1UEBhMCVVMx
# FzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQDEzJEaWdpQ2VydCBUcnVz
# dGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGluZyBDQQIQDE1pckuU+jwq
# Sj0pB4A9WjANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0B
# BwEwHAYJKoZIhvcNAQkFMQ8XDTIzMDUxMjAwMjUyNVowLwYJKoZIhvcNAQkEMSIE
# IFZeXTNUpflpD5RGOTipErk+IhJt6q3O4Lsmr8bLf7JmMA0GCSqGSIb3DQEBAQUA
# BIICAA9b7zHRW//1cUwUJ7m7utQ4zH+Befr1dsqYxM+uT4mRqidkzpEE5D0HsrhT
# rpWnhI2i8GB6lxNH5OyGsuh2Qa+B+oE0v/YGPKeOJQ9m3sz/EHraeL85aLBNI63J
# nDZ22bd1GSxl227GwP3Ca/vj6h2HZQ9jl5kQQfA79fRnw5SQL0MirOK43DF0UIuz
# pMmkHobR+gGzEV9iaXBtM9Gx9ZoAYXsK7MPkfpw/80aI7oxyrxmVOQPdfPF6Z/PZ
# WSm6WEPpXcq9BzWpLQJAdwglqI9HSe4JVMpgn9ybVA++DupMZtuSM/HI84f42OHB
# yN7NN6ZkB77gyeh9gGZLHrEBQj746DvxQvpsJd5C6bzuVo02eqN/gaf+/mnKhxE4
# KJBJYMP8yfQ381s2xneU0FKvC6omNilRbyHPFj5kB0hazK38Vn/0tM0KNbgAi91O
# Tbm7OG83whSUU0dIAOgUDHPFO4lqTO5L16Hq+NVumsrCUA7+xgudHqZGpDbVibu/
# k3wkTsvtOpQ5oCY70ftXZo1eo6f2ylaV0ysrULL+n2CHEBLMfXEVJyRgYHAMfiEp
# 5U9g0hPlxTSO5MVqwsjW7KITwebQQtBpT7m4MM7jhs5dHrU+Zx0vMFw8TZuszQQ9
# oTAdM/wcTLoN9tD3cwWkii1llY4cKLahfU1FD/HERPltmsdr
# SIG # End signature block
