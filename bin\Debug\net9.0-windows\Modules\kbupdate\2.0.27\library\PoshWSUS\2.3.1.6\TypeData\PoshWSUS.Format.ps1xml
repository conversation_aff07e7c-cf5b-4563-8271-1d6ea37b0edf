﻿<Configuration>
  <ViewDefinitions>
    <View>
      <Name>Update</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.Update</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Title</Label>
            <Width>30</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>KnowledgebaseArticles</Label>
            <Width>22</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UpdateType</Label>
            <Width>10</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>CreationDate</Label>
            <Width>23</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UpdateID</Label>
            <Width>30</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Title</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>KnowledgebaseArticles</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UpdateType</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>CreationDate</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UpdateID</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Update</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.Update</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <PropertyName>Title</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>Description</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>KnowledgebaseArticles</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>UpdateID</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>CreationDate</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>ProductTitles</PropertyName>
              </ListItem>
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>
    <View>
      <Name>UpdateServer</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateServer</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Name</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Version</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>PortNumber</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ServerProtocolVersion</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Name</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Version</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>PortNumber</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ServerProtocolVersion</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.ComputerTarget</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>FullDomainName</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>IPAddress</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ClientVersion</Label>
            <Width>15</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastSyncTime</Label>
            <Width>25</Width>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>OSDescription</Label>
            <Width>40</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>FullDomainName</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>IPAddress</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ClientVersion</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastSyncTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>OSDescription</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Update</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.ComputerTarget</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <PropertyName>FullDomainName</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>IPAddress</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>OSDescription</PropertyName>
              </ListItem>              
              <ListItem>
                <PropertyName>ClientVersion</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>LastSyncTime</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>ID</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>ComputerGroup</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>LastReportedStatusTime</PropertyName>
              </ListItem>
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Administration.UpdateServerStatus</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>UpdateCount</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>DeclinedUpdateCount</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ApprovedUpdateCount</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>NotApprovedUpdateCount</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ComputerTargetCount</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>UpdateCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>DeclinedUpdateCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ApprovedUpdateCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>NotApprovedUpdateCount</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ComputerTargetCount</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>Update</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateServerStatus</TypeName>
      </ViewSelectedBy>
      <ListControl>
        <ListEntries>
          <ListEntry>
            <ListItems>
              <ListItem>
                <PropertyName>UpdateCount</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>DeclinedUpdateCount</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>ApprovedUpdateCount</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>NotApprovedUpdateCount</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>ComputerTargetCount</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>UpdatesNeedingFilesCount</PropertyName>
              </ListItem>
              <ListItem>
                <PropertyName>CriticalOrSecurityUpdatesNotApprovedForInstallCount</PropertyName>
              </ListItem>
            </ListItems>
          </ListEntry>
        </ListEntries>
      </ListControl>
    </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.SynchronizationInfo</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>StartTime</Label>
            <Width>30</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>EndTime</Label>
            <Width>30</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Result</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Error</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>StartTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>EndTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Result</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Error</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.Subscription</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>SynchronizeAutomatically</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>SynchronizeAutomaticallyTimeOfDay</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastSynchronizationTime</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastModifiedTime</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>LastModifiedBy</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>SynchronizeAutomatically</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>SynchronizeAutomaticallyTimeOfDay</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastSynchronizationTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastModifiedTime</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>LastModifiedBy</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateCategory</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Type</Label>
            <Width>15</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Title</Label>
            <Width>35</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UpdateSource</Label>
            <Width>18</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ArrivalDate</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Type</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Title</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UpdateSource</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ArrivalDate</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateClassification</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>Title</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>Description</Label>
            <Width>40</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>ArrivalDate</Label>
            <Width>25</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>Title</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>Description</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>ArrivalDate</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Administration.UpdateFile</TypeName>
		  </ViewSelectedBy>
		  <TableControl>
			  <TableHeaders>
				  <TableColumnHeader>
					  <Label>KnowledgeBaseArticles</Label>
					  <Width>25</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>Name</Label>
					  <Width>63</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>FileURI</Label>
					  <Width>30</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>TotalBytes</Label>
					  <Width>10</Width>
					  <Alignment>Right</Alignment>
				  </TableColumnHeader>
			  </TableHeaders>
			  <TableRowEntries>
				  <TableRowEntry>
					  <TableColumnItems>
						  <TableColumnItem>
							  <PropertyName>KnowledgeBaseArticles</PropertyName>
						  </TableColumnItem>						  
						  <TableColumnItem>
							  <PropertyName>Name</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>FileURI</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>TotalBytes</PropertyName>
						  </TableColumnItem>
					  </TableColumnItems>
				  </TableRowEntry>
			  </TableRowEntries>
		  </TableControl>
	  </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Administration.UpdateFile</TypeName>
		  </ViewSelectedBy>
		  <ListControl>
			  <ListEntries>
				  <ListEntry>
					  <ListItems>
						  <ListItem>
							  <PropertyName>Name</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>FileUri</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>OriginUri</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>TotalBytes</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Modified</PropertyName>
						  </ListItem>
					  </ListItems>
				  </ListEntry>
			  </ListEntries>
		  </ListControl>
	  </View>
	<View>
		<Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.InstallableItem</TypeName>
		  </ViewSelectedBy>
		  <TableControl>
			  <TableHeaders>
				  <TableColumnHeader>
					  <Label>KnowledgeBaseArticles</Label>
					  <Width>25</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>Title</Label>
					  <Width>63</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>Files</Label>
					  <Width>25</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>Languages</Label>
					  <Width>10</Width>
					  <Alignment>Right</Alignment>
				  </TableColumnHeader>				  
			  </TableHeaders>
			  <TableRowEntries>
				  <TableRowEntry>
					  <TableColumnItems>
						  <TableColumnItem>
							  <PropertyName>KnowledgeBaseArticles</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>Title</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>Files</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>Languages</PropertyName>
						  </TableColumnItem>						  
					  </TableColumnItems>
				  </TableRowEntry>
			  </TableRowEntries>
		  </TableControl>
	  </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.InstallableItem</TypeName>
		  </ViewSelectedBy>
		  <ListControl>
			  <ListEntries>
				  <ListEntry>
					  <ListItems>
						  <ListItem>
							  <PropertyName>KnowledgeBaseArticles</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Title</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Files</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Languages</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Id</PropertyName>
						  </ListItem>
					  </ListItems>
				  </ListEntry>
			  </ListEntries>
		  </ListControl>
	  </View>
	<View>
		<Name>Update</Name>
		<ViewSelectedBy>
			<TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Update</TypeName>
		</ViewSelectedBy>
		<TableControl>
			<TableHeaders>
				<TableColumnHeader>
					<Label>UpdateTitle</Label>
					<Width>46</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Installed</Label>
					<Width>9</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Needed</Label>
					<Width>6</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Failed</Label>
					<Width>6</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>PendingReboot</Label>
					<Width>13</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
			</TableHeaders>
			<TableRowEntries>
				<TableRowEntry>
					<TableColumnItems>
						<TableColumnItem>
							<PropertyName>UpdateTitle</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Installed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Needed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Failed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>PendingReboot</PropertyName>
						</TableColumnItem>
					</TableColumnItems>
				</TableRowEntry>
			</TableRowEntries>
		</TableControl>
	</View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Update</TypeName>
		  </ViewSelectedBy>
		  <ListControl>
			  <ListEntries>
				  <ListEntry>
					  <ListItems>
						  <ListItem>
							  <PropertyName>UpdateTitle</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateKB</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Installed</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>NotInstalled</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Needed</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Failed</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>PendingReboot</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>LastUpdated</PropertyName>
						  </ListItem>
					  </ListItems>
				  </ListEntry>
			  </ListEntries>
		  </ListControl>
	  </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateInstallationInfo</TypeName>
		  </ViewSelectedBy>
		  <TableControl>
			  <TableHeaders>
				  <TableColumnHeader>
					  <Label>Computername</Label>
					  <Width>20</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>UpdateKB</Label>
					  <Width>10</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>UpdateTitle</Label>
					  <Width>40</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>UpdateInstallationState</Label>
					  <Width>20</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>UpdateApprovalAction</Label>
					  <Width>15</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
			  </TableHeaders>
			  <TableRowEntries>
				  <TableRowEntry>
					  <TableColumnItems>
						  <TableColumnItem>
							  <PropertyName>Computername</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>UpdateKB</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>UpdateTitle</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>UpdateInstallationState</PropertyName>
						  </TableColumnItem>						  
						  <TableColumnItem>
							  <PropertyName>UpdateApprovalAction</PropertyName>
						  </TableColumnItem>
					  </TableColumnItems>
				  </TableRowEntry>
			  </TableRowEntries>
		  </TableControl>
	  </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateInstallationInfo</TypeName>
		  </ViewSelectedBy>
		  <ListControl>
			  <ListEntries>
				  <ListEntry>
					  <ListItems>
						  <ListItem>
							  <PropertyName>Computername</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateKB</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateTitle</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateInstallationState</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateApprovalAction</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateServerName</PropertyName>
						  </ListItem>						  
					  </ListItems>
				  </ListEntry>
			  </ListEntries>
		  </ListControl>
	  </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.ComputerTargetGroup</TypeName>
		  </ViewSelectedBy>
		  <TableControl>
			  <TableHeaders>
				  <TableColumnHeader>
					  <Label>FullDomainName</Label>
					  <Width>30</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>Name</Label>
					  <Width>30</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>ParentGroup</Label>
					  <Width>40</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
			  </TableHeaders>
			  <TableRowEntries>
				  <TableRowEntry>
					  <TableColumnItems>
						  <TableColumnItem>
							  <PropertyName>FullDomainName</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>Name</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>ParentGroup</PropertyName>
						  </TableColumnItem>
					  </TableColumnItems>
				  </TableRowEntry>
			  </TableRowEntries>
		  </TableControl>
	  </View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateApproval</TypeName>
		  </ViewSelectedBy>
		  <TableControl>
			  <TableHeaders>
				  <TableColumnHeader>
					  <Label>UpdateTitle</Label>
					  <Width>20</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>GoLiveTime</Label>
					  <Width>25</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>Deadline</Label>
					  <Width>25</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>AdministratorName</Label>
					  <Width>20</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>
				  <TableColumnHeader>
					  <Label>TargetGroup</Label>
					  <Width>20</Width>
					  <Alignment>Left</Alignment>
				  </TableColumnHeader>				  
			  </TableHeaders>
			  <TableRowEntries>
				  <TableRowEntry>
					  <TableColumnItems>
						  <TableColumnItem>
							  <PropertyName>UpdateTitle</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>GoLiveTime</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>Deadline</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>AdministratorName</PropertyName>
						  </TableColumnItem>
						  <TableColumnItem>
							  <PropertyName>TargetGroup</PropertyName>
						  </TableColumnItem>
					  </TableColumnItems>
				  </TableRowEntry>
			  </TableRowEntries>
		  </TableControl>
	  </View>	  
	<View>
		<Name>Update</Name>
		<ViewSelectedBy>
			<TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Client</TypeName>
		</ViewSelectedBy>
		<TableControl>
			<TableHeaders>
				<TableColumnHeader>
					<Label>Computer</Label>
					<Width>25</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Installed</Label>
					<Width>9</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Needed</Label>
					<Width>6</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Failed</Label>
					<Width>6</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>PendingReboot</Label>
					<Width>14</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
			</TableHeaders>
			<TableRowEntries>
				<TableRowEntry>
					<TableColumnItems>
						<TableColumnItem>
							<PropertyName>Computer</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Installed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Needed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Failed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>PendingReboot</PropertyName>
						</TableColumnItem>
					</TableColumnItems>
				</TableRowEntry>
			</TableRowEntries>
		</TableControl>
	</View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Client</TypeName>
		  </ViewSelectedBy>
		  <ListControl>
			  <ListEntries>
				  <ListEntry>
					  <ListItems>
						  <ListItem>
							  <PropertyName>InstalledCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>NotInstalledCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>NeededCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>FailedCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>InstalledPendingRebootCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>LastUpdated</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>Computer</PropertyName>
						  </ListItem>
					  </ListItems>
				  </ListEntry>
			  </ListEntries>
		  </ListControl>
	  </View>
	<View>
		<Name>Update</Name>
		<ViewSelectedBy>
			<TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Group</TypeName>
		</ViewSelectedBy>
		<TableControl>
			<TableHeaders>
				<TableColumnHeader>
					<Label>UpdateTitle</Label>
					<Width>30</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>ComputerGroup</Label>
					<Width>20</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Installed</Label>
					<Width>9</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Needed</Label>
					<Width>6</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>Failed</Label>
					<Width>6</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
				<TableColumnHeader>
					<Label>PendingReboot</Label>
					<Width>13</Width>
					<Alignment>Left</Alignment>
				</TableColumnHeader>
			</TableHeaders>
			<TableRowEntries>
				<TableRowEntry>
					<TableColumnItems>
						<TableColumnItem>
							<PropertyName>UpdateTitle</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>ComputerGroup</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Installed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Needed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>Failed</PropertyName>
						</TableColumnItem>
						<TableColumnItem>
							<PropertyName>PendingReboot</PropertyName>
						</TableColumnItem>
					</TableColumnItems>
				</TableRowEntry>
			</TableRowEntries>
		</TableControl>
	</View>
	<View>
		  <Name>Update</Name>
		  <ViewSelectedBy>
			  <TypeName>Microsoft.UpdateServices.Internal.BaseApi.UpdateSummary.Group</TypeName>
		  </ViewSelectedBy>
		  <ListControl>
			  <ListEntries>
				  <ListEntry>
					  <ListItems>
						  <ListItem>
							  <PropertyName>UpdateTitle</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>UpdateKB</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>InstalledCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>NotInstalledCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>NeededCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>FailedCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>InstalledPendingRebootCount</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>LastUpdated</PropertyName>
						  </ListItem>
						  <ListItem>
							  <PropertyName>ComputerGroup</PropertyName>
						  </ListItem>
					  </ListItems>
				  </ListEntry>
			  </ListEntries>
		  </ListControl>
	  </View>
    <View>
      <Name>ComputerTarget</Name>
      <ViewSelectedBy>
        <TypeName>Microsoft.UpdateServices.Administration.UpdateRevisionID</TypeName>
      </ViewSelectedBy>
      <TableControl>
        <TableHeaders>
          <TableColumnHeader>
            <Label>UpdateID</Label>
            <Width>36</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UpdateTitle</Label>
            <Width>20</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>UpdateKB</Label>
            <Width>10</Width>
            <Alignment>Left</Alignment>
          </TableColumnHeader>
          <TableColumnHeader>
            <Label>RevisionNumber</Label>
            <Width>14</Width>
            <Alignment>Right</Alignment>
          </TableColumnHeader>
        </TableHeaders>
        <TableRowEntries>
          <TableRowEntry>
            <TableColumnItems>
              <TableColumnItem>
                <PropertyName>UpdateID</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UpdateTitle</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>UpdateKB</PropertyName>
              </TableColumnItem>
              <TableColumnItem>
                <PropertyName>RevisionNumber</PropertyName>
              </TableColumnItem>
            </TableColumnItems>
          </TableRowEntry>
        </TableRowEntries>
      </TableControl>
    </View>
  </ViewDefinitions>
</Configuration>