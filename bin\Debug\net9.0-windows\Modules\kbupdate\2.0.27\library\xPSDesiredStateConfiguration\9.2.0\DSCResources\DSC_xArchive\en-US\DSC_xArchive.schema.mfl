[AMENDMENT, LOCALE("MS_409")]
class DSC_xArchive : OMI_BaseResource
{
  [Key,Description("The path to the archive file that should be expanded to or removed from the specified destination.") : Amended] String Path;
  [Key,Description("The path where the specified archive file should be expanded to or removed from.") : Amended] String Destination;
  [Description("Specifies whether or not the expanded content of the archive file at the specified path should exist at the specified destination. To update the specified destination to have the expanded content of the archive file at the specified path, specify this property as Present. To remove the expanded content of the archive file at the specified path from the specified destination, specify this property as Absent. The default value is Present.") : Amended] String Ensure;
  [Description("Specifies whether or not to validate that a file at the destination with the same name as a file in the archive actually matches that corresponding file in the archive by the specified checksum method. If the file does not match and Ensure is specified as Present and Force is not specified, the resource will throw an error that the file at the destination cannot be overwritten. If the file does not match and Ensure is specified as Present and Force is specified, the file at the destination will be overwritten. If the file does not match and Ensure is specified as Absent, the file at the destination will not be removed. The default value is false.") : Amended] Boolean Validate;
  [Description("The Checksum method to use to validate whether or not a file at the destination with the same name as a file in the archive actually matches that corresponding file in the archive. An invalid argument exception will be thrown if Checksum is specified while Validate is specified as false. ModifiedDate will check that the LastWriteTime property of the file at the destination matches the LastWriteTime property of the file in the archive. CreatedDate will check that the CreationTime property of the file at the destination matches the CreationTime property of the file in the archive. SHA-1, SHA-256, and SHA-512 will check that the hash of the file at the destination by the specified SHA method matches the hash of the file in the archive by the specified SHA method. The default value is ModifiedDate.") : Amended] String Checksum;
  [Description("The credential of a user account with permissions to access the specified archive path and destination if needed.") : Amended] String Credential;
  [Description("Specifies whether or not any existing files or directories at the destination with the same name as a file or directory in the archive should be overwritten to match the file or directory in the archive. When this property is false, an error will be thrown if an item at the destination needs to be overwritten. The default value is false.") : Amended] Boolean Force;
};
