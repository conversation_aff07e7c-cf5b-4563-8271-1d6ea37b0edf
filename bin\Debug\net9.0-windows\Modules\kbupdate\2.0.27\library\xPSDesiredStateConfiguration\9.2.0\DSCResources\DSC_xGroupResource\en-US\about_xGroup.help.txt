.NAME
    xGroup

.DESCRIPTION
    Provides a mechanism to manage local groups on the target node.
    This resource works on Nano Server.

.PARAMETER GroupName
    Key - String
    The name of the group to create, modify, or remove.

.PARAMETER Ensure
    Write - String
    Allowed values: Present, Absent
    Indicates if the group should exist or not.

.PARAMETER Description
    Write - String
    The description the group should have.

.PARAMETER Members
    Write - StringArray
    The members the group should have. This property will replace all the current group members with the specified members. Members should be specified as strings in the format of their domain qualified name, UPN ,distinguished name or username (for local machine accounts). Using either the MembersToExclude or MembersToInclude properties in the same configuration as this property will generate an error.

.PARAMETER MembersToInclude
    Write - StringArray
    The members the group should include. This property will only add members to a group. Members should be specified as strings in the format of their domain qualified name, UPN ,distinguished name or username (for local machine accounts). Using the Members property in the same configuration as this property will generate an error.

.PARAMETER MembersToExclude
    Write - StringArray
    The members the group should exclude. This property will only remove members from a group. Members should be specified as strings in the format of their domain qualified name, UPN ,distinguished name or username (for local machine accounts). Using the Members property in the same configuration as this property will generate an error.

.PARAMETER Credential
    Write - Instance
    A credential to resolve non-local group members.

.EXAMPLE 1


#Requires -Module xPSDesiredStateConfiguration

<#
    .SYNOPSIS
        Configuration that make sure a group exist and the specified users are
        not member of the group.

    .DESCRIPTION
        Configuration that make sure a group exist and have the correct members.

        If the group does not exist, adds the users and make sure the members of
        the group are only those that are in the configuration. If the group
        already exists and if there are any members not in the configuration,
        those members will be removed from the group, and any missing members
        that are in the configuration will be added to the group.

    .PARAMETER Name
        The name of the group to create or/and remove members from.

    .PARAMETER MembersToExclude
        One or more usernames of the users that should be removed as member of
        the group.

    .EXAMPLE
        xGroup_RemoveMembersConfig -Name 'GroupName1' -MembersToExclude @('Username1', 'Username2')

        Compiles a configuration that creates the group 'GroupName1, if it does
        not already exist, and will the make sure the users with the usernames
        'Username1' or 'Username2' are removed as member from the group if the
        users are ever added as members.
        If the group named GroupName1 already exists, will make sure the users
        with the usernames 'Username1' or 'Username2' are removed as member from
        the group if the users are ever added as members.

    .EXAMPLE
        Start-AzureRmAutomationDscCompilationJob -ResourceGroupName '<resource-group>' -AutomationAccountName '<automation-account>' -ConfigurationName 'xGroup_RemoveMembersConfig' -Parameters @{ Name = 'GroupName1'; MembersToExclude = @('Username1', 'Username2') }

        Compiles a configuration in Azure Automation that creates the group
        'GroupName1, if it does not already exist, and will the make sure the
        users with the usernames 'Username1' or 'Username2' are removed as member
        from the group if the users are ever added as members.
        If the group named GroupName1 already exists, will make sure the users
        with the usernames 'Username1' or 'Username2' are removed as member from
        the group if the users are ever added as members.

        Replace the <resource-group> and <automation-account> with correct values.
#>
Configuration xGroup_RemoveMembersConfig
{
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $Name,

        [Parameter()]
        [System.String[]]
        $MembersToExclude
    )

    Import-DscResource -ModuleName 'xPSDesiredStateConfiguration'

    Node localhost
    {
        xGroup 'RemoveMembers'
        {
            GroupName        = $Name
            Ensure           = 'Present'
            MembersToExclude = $MembersToExclude
        }
    }
}

.EXAMPLE 2


#Requires -Module xPSDesiredStateConfiguration

<#
    .SYNOPSIS
        Configuration that make sure a group exist and have the correct members.

    .DESCRIPTION
        Configuration that make sure a group exist and have the correct members.

        If the group does not exist, adds the users and make sure the members of
        the group are only those that are in the configuration. If the group
        already exists and if there are any members not in the configuration,
        those members will be removed from the group, and any missing members
        that are in the configuration will be added to the group.

    .PARAMETER Name
        The name of the group to create or/and add members to.

    .PARAMETER Members
        One or more usernames of the users that should be the only members of the
        group.

    .EXAMPLE
        xGroup_SetMembersConfig -Name 'GroupName1' -Members @('Username1', 'Username2')

        Compiles a configuration that creates the group 'GroupName1, if it does
        not already exist, and adds the users with the usernames 'Username1'
        and 'Username2' to the group. If the group named GroupName1 already
        exists, removes any users that do not have the usernames Username1 or
        Username2 from the group and adds the users that have the usernames
        Username1 and Username2 to the group.

    .EXAMPLE
        Start-AzureRmAutomationDscCompilationJob -ResourceGroupName '<resource-group>' -AutomationAccountName '<automation-account>' -ConfigurationName 'xGroup_SetMembersConfig' -Parameters @{ Name = 'GroupName1'; Members = @('Username1', 'Username2') }

        Compiles a configuration in Azure Automation that creates the group
        'GroupName1, if it does not already exist, and adds the users with the
        usernames 'Username1' and 'Username2' to the group.
        If the group named GroupName1 already exists, removes any users that do
        not have the usernames Username1 or Username2 from the group and adds
        the users that have the usernames Username1 and Username2 to the group.

        Replace the <resource-group> and <automation-account> with correct values.
#>
Configuration xGroup_SetMembersConfig
{
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $Name,

        [Parameter()]
        [System.String[]]
        $Members
    )

    Import-DscResource -ModuleName 'xPSDesiredStateConfiguration'

    Node localhost
    {
        xGroup 'SetMembers'
        {
            GroupName = $Name
            Ensure    = 'Present'
            Members   = $Members
        }
    }
}

