﻿<?xml version="1.0" encoding="utf-16"?>
<Configuration>
	<SelectionSets>
		<SelectionSet>
			<Name>PSFramework.Temp.TempItem</Name>
			<Types>
				<TypeName>PSFramework.Temp.TempItemDirectory</TypeName>
				<TypeName>PSFramework.Temp.TempItemFile</TypeName>
				<TypeName>PSFramework.Temp.TempItemGeneric</TypeName>
            </Types>
        </SelectionSet>
    </SelectionSets>
	
	<Controls>
        <Control>
            <Name>Configuration-GroupingFormat</Name>
            <CustomControl>
                <CustomEntries>
                    <CustomEntry>
                        <CustomItem>
                            <Frame>
                                <LeftIndent>4</LeftIndent>
                                <CustomItem>
                                    <Text>Module: </Text>
                                    <ExpressionBinding>
                                    	<PropertyName>Module</PropertyName>
                                    </ExpressionBinding>
                                    <NewLine/>
                                </CustomItem>
                            </Frame>
                        </CustomItem>
                    </CustomEntry>
                </CustomEntries>
            </CustomControl>
        </Control>
    </Controls>

	<ViewDefinitions>
		<!-- PSFramework.Configuration.Config -->
		<View>
			<Name>PSFramework.Configuration.Config</Name>
			<ViewSelectedBy>
				<TypeName>PSFramework.Configuration.Config</TypeName>
			</ViewSelectedBy>
			<GroupBy>
                <PropertyName>Module</PropertyName>
                <CustomControlName>Configuration-GroupingFormat</CustomControlName>
            </GroupBy>
			<TableControl>
				<AutoSize/>
				<TableHeaders>
					<TableColumnHeader/>
					<TableColumnHeader>
						<Label>Value</Label>
						<Alignment>Left</Alignment>
					</TableColumnHeader>
					<TableColumnHeader>
						<Alignment>Left</Alignment>
					</TableColumnHeader>
				</TableHeaders>
				<TableRowEntries>
					<TableRowEntry>
						<TableColumnItems>
							<TableColumnItem>
								<PropertyName>FullName</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<ScriptBlock>
			if ($null -ne $_.SafeValue)
			{
              if ($_.SafeValue.ToString().Length -le 20) { $_.SafeValue.ToString() }
              else { $_.SafeValue.ToString().SubString(0,17) + "..." }
			}
								</ScriptBlock>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Description</PropertyName>
							</TableColumnItem>
						</TableColumnItems>
					</TableRowEntry>
				</TableRowEntries>
			</TableControl>
		</View>
		
        <!-- PSFramework.Configuration.PersistedConfig -->
        <View>
            <Name>PSFramework.Configuration.PersistedConfig</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Configuration.PersistedConfig</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Scope</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>FullName</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Value</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>

		<!-- PSFramework.Feature.FeatureItem -->
        <View>
            <Name>PSFramework.Feature.FeatureItem</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Feature.FeatureItem</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader>
						<Label>Module</Label>
					</TableColumnHeader>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Owner</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Global</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>ModuleSpecific</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Description</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>

        <!-- PSFramework.Filter.Condition -->
        <View>
            <Name>PSFramework.Filter.Condition</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Filter.Condition</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Module</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Version</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Type</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
		<!-- PSFramework.Filter.ConditionSet -->
        <View>
            <Name>PSFramework.Filter.ConditionSet</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Filter.ConditionSet</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Module</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Version</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Conditions</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
        <!-- PSFramework.Filter.Expression -->
        <View>
            <Name>PSFramework.Filter.Expression</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Filter.Expression</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>ExpressionString</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>ConditionSet</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
        <!-- PSFramework.FlowControl.Callback -->
        <View>
            <Name>PSFramework.FlowControl.Callback</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.FlowControl.Callback</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader>
                        <Label>Runspace</Label>
                    </TableColumnHeader>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>ModuleName</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>CommandName</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <ScriptBlock>

  if (-not $_.Runspace) { return "[All Runspaces]" }
  $object = $_
  $runspace = [PSFramework.Utility.UtilityHost]::GetRunspaces() | Where-Object InstanceID -eq $object.Runspace
  if ($runspace) {
    if ($runspace.Name) { return $runspace.Name }
    return "Runspace $($runspace.Id)"
  }
  "[Unknown Runspace]"

                                </ScriptBlock>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
		<!-- PSFramework.License.License -->
		<View>
			<Name>PSFramework.License.License</Name>
			<ViewSelectedBy>
				<TypeName>PSFramework.License.License</TypeName>
			</ViewSelectedBy>
			<TableControl>
				<AutoSize/>
				<TableHeaders>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
				</TableHeaders>
				<TableRowEntries>
					<TableRowEntry>
						<TableColumnItems>
							<TableColumnItem>
								<PropertyName>Product</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>ProductVersion</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Manufacturer</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>LicenseType</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>LicenseName</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>LicenseVersion</PropertyName>
							</TableColumnItem>
						</TableColumnItems>
					</TableRowEntry>
				</TableRowEntries>
			</TableControl>
		</View>

		<!-- PSFramework.Logging.Provider -->
		<View>
			<Name>PSFramework.Logging.Provider</Name>
			<ViewSelectedBy>
				<TypeName>PSFramework.Logging.Provider</TypeName>
			</ViewSelectedBy>
			<TableControl>
				<AutoSize/>
				<TableHeaders>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
				</TableHeaders>
				<TableRowEntries>
					<TableRowEntry>
						<TableColumnItems>
							<TableColumnItem>
								<PropertyName>Name</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Enabled</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>ProviderVersion</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>IncludeModules</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>ExcludeModules</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>IncludeTags</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>ExcludeTags</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Initialized</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>InstallationOptional</PropertyName>
							</TableColumnItem>
						</TableColumnItems>
					</TableRowEntry>
				</TableRowEntries>
			</TableControl>
		</View>
		
        <!-- PSFramework.Logging.ProviderInstance -->
        <View>
            <Name>PSFramework.Logging.ProviderInstance</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Logging.ProviderInstance</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Provider</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Enabled</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>IncludeModules</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>ExcludeModules</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>IncludeTags</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>ExcludeTags</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>

        <!-- PSFramework.Message.MessageColorCondition -->
        <View>
            <Name>PSFramework.Message.MessageColorCondition</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Message.MessageColorCondition</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Color</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Priority</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
		<!-- PSFramework.Message.LogEntry -->
		<View>
			<Name>PSFramework.Message.LogEntry</Name>
			<ViewSelectedBy>
				<TypeName>PSFramework.Message.LogEntry</TypeName>
			</ViewSelectedBy>
			<TableControl>
				<AutoSize/>
				<TableHeaders>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader>
						<Label>TargetObject</Label>
					</TableColumnHeader>
					<TableColumnHeader/>
				</TableHeaders>
				<TableRowEntries>
					<TableRowEntry>
						<TableColumnItems>
							<TableColumnItem>
								<PropertyName>Timestamp</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>FunctionName</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Line</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Level</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<ScriptBlock>
			if ($null -ne $_.TargetObject)
			{
              if ($_.TargetObject.ToString().Length -le 20) { $_.TargetObject.ToString() }
              else { $_.TargetObject.ToString().SubString(0,17) + "..." }
			}
								</ScriptBlock>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Message</PropertyName>
							</TableColumnItem>
						</TableColumnItems>
					</TableRowEntry>
				</TableRowEntries>
			</TableControl>
		</View>

		<!-- PSFramework.Message.PsfExceptionRecord -->
		<View>
			<Name>PSFramework.Message.PsfExceptionRecord</Name>
			<ViewSelectedBy>
				<TypeName>PSFramework.Message.PsfExceptionRecord</TypeName>
			</ViewSelectedBy>
			<TableControl>
				<AutoSize/>
				<TableHeaders>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader>
						<Label>TargetObject</Label>
					</TableColumnHeader>
					<TableColumnHeader/>
				</TableHeaders>
				<TableRowEntries>
					<TableRowEntry>
						<TableColumnItems>
							<TableColumnItem>
								<PropertyName>Timestamp</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>FunctionName</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>ExceptionType</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<ScriptBlock>
			if ($null -ne $_.TargetObject)
			{
              if ($_.TargetObject.ToString().Length -le 20) { $_.TargetObject.ToString() }
              else { $_.TargetObject.ToString().SubString(0,17) + "..." }
			}
								</ScriptBlock>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Message</PropertyName>
							</TableColumnItem>
						</TableColumnItems>
					</TableRowEntry>
				</TableRowEntries>
			</TableControl>
		</View>

		<!-- PSFramework.TaskEngine.PsfTask -->
		<View>
			<Name>PSFramework.TaskEngine.PsfTask</Name>
			<ViewSelectedBy>
				<TypeName>PSFramework.TaskEngine.PsfTask</TypeName>
			</ViewSelectedBy>
			<TableControl>
				<AutoSize/>
				<TableHeaders>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
					<TableColumnHeader/>
				</TableHeaders>
				<TableRowEntries>
					<TableRowEntry>
						<TableColumnItems>
							<TableColumnItem>
								<PropertyName>Priority</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Name</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Pending</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>LastExecution</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Interval</PropertyName>
							</TableColumnItem>
							<TableColumnItem>
								<PropertyName>Description</PropertyName>
							</TableColumnItem>
						</TableColumnItems>
					</TableRowEntry>
				</TableRowEntries>
			</TableControl>
		</View>
		
		<!-- PSFramework.Temp.TempItem -->
        <View>
            <Name>PSFramework.Temp.TempItem</Name>
            <ViewSelectedBy>
  				<SelectionSetName>PSFramework.Temp.TempItem</SelectionSetName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>ProviderName</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Module</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Path</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
        <!-- PSFramework.Utility.PsfScriptBlock -->
        <View>
            <Name>PSFramework.Utility.PsfScriptBlock</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Utility.PsfScriptBlock</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader>
                        <Label>Length</Label>
                    </TableColumnHeader>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Id</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <ScriptBlock>
 $_.StartPosition.Length 
                                </ScriptBlock>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Module</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>LanguageMode</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Attributes</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
		
        <!-- PSFramework.Utility.ScriptBlockItem -->
        <View>
            <Name>PSFramework.Utility.ScriptBlockItem</Name>
            <ViewSelectedBy>
                <TypeName>PSFramework.Utility.ScriptBlockItem</TypeName>
            </ViewSelectedBy>
            <TableControl>
                <AutoSize/>
                <TableHeaders>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>Name</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>LastRetrieved</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>CountRetrieved</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>
	</ViewDefinitions>
</Configuration>
