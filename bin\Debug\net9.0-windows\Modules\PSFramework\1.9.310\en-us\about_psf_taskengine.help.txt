﻿TOPIC
	about_psf_taskengine
	
SHORT DESCRIPTION
	Explains the PSFramework's Task Engine component
	
LONG DESCRIPTION
	#-------------------------------------------------------------------------#
	#                           Component Commands                            #
	#-------------------------------------------------------------------------#

	- Disable-PSFTaskEngineTask
	- Enable-PSFTaskEngineTask
	- Get-PSFTaskEngineCache
	- Get-PSFTaskEngineTask
	- Register-PSFTaskEngineTask
	- Set-PSFTaskEngineCache
	- Test-PSFTaskEngineCache
	- Test-PSFTaskEngineTask
	
	
	#-------------------------------------------------------------------------#
	#                            Table of Contents                            #
	#-------------------------------------------------------------------------#

	- Introduction
	- Running a task
	- Exchanging Information
	
	
	#-------------------------------------------------------------------------#
	#                               Introduction                              #
	#-------------------------------------------------------------------------#
	
	The Task Engine system of the PSFramework module is designed to enable
	module developers to offload slow code from their import process, as well
	as to schedule repeating maintenance tasks that run in the background.
	
	It also processes registered task sequentially, preventing spontaneous
	bursts of CPU loads due to too many tasks executing at once (as would be
	the case if each module provided its own solution).
	
	Finally, it is runspace-safe, enabling modules to schedule and manage tasks
	without having to worry about code duplication when that module is used in
	a runspace scenario.
	
	
	#-------------------------------------------------------------------------#
	#                              Running a task                             #
	#-------------------------------------------------------------------------#
	
	The primary command to use with the task engine is the function:
	  Register-PSFTaskEngineTask
	
	In order to function, it needs a few pieces of information:
	- A unique name.
	  Best prefix it with a module name (eg: 'mymodule.maintenance')
	- A Description
	  This is optional, but helps with understanding what is happening in the
	  background. A piece of self-documentation and highly recommended.
	- A scriptblock
	  Because a task without something to do wouldn't be much of a task.
	- A schedule
	  Either set it to run only once, or set it to run in a given interval.
	
	Example:
	
	  Register-PSFTaskEngineTask -Name 'mymodule.buildcache' -ScriptBlock $ScriptBlock -Once -Description 'Builds the object cache used by the mymodule module'
	
	This will register the task to run the scriptblock stored in $ScriptBlock.
	It will only be run once at average priority.
	
	Example 2:
	
	  Register-PSFTaskEngineTask -Name 'mymodule.maintenance' -ScriptBlock $ScriptBlock -Interval "00:05:00" -Delay "00:01:00" -Priority Critical -Description 'Performs critical system maintenance in order for the mymodule module to function'
	
	Registers the task contained in $ScriptBlock under the name 'mymodule.maintenance'
	- Sets it to execute every 5 minutes
	- Sets it to wait for 1 minute after registration before starting the
	  first execution
	- Sets it to priority "Critical", ensuring it takes precedence over most
	  other tasks.
	
	# The ScriptBlock #
	#-----------------#
	
	You can put pretty much anything you want to into the scriptblock that is
	registered to execute. You can access any module available on the computer,
	including the very module that registered the task!
	However no module is imported by default! Keep in mind, that not
	necessarily all users will place your module in one of the default paths,
	relying on implicit import may not work in that case! Both the main
	runspace and the task share the same configuration system however, so it
	is possible to share the modulepath by registering the modulebase in the
	configuration system.
	
	Note: The task should NOT use the configuration system to return
	information to the main runspace. See the next chapter on details, of how
	a task can offer information back to its parent.
	
	
	#-------------------------------------------------------------------------#
	#                          Exchanging Information                         #
	#-------------------------------------------------------------------------#
	
	Since the task is executed on another runspace - one you have no control
	over - you cannot easily share information across this boundary. In order
	to help with this issue, the PSFramework provides a cache that can be used
	to provide and update information by the task.
	The functions in the main runspace (what the user runs directly) can then
	access the information stored there.
	
	Example (Place this in your background task):
	
	  Set-PSFTaskEngineCache -Module 'mymodule' -Name 'database-cache' -Value $results
	
	This will set the values retrieved as part of the task and stored in
	$results and store them under the modulename 'mymodule' and the cache name
	'database-cache' (The names are arbitrary and not case sensitive).
	
	Example (Place this in your main function):
	
	  $data = Get-PSFTaskEngineCache -Module 'mymodule' -Name 'database-cache'
	
	This will retrieve the values stored using the previous example. If the
	task hasn't run yet (or has failed), it will instead return $null. You can
	check, whether the task has run, by using the Test-PSFTaskEngineTask
	command. In cases where it is not certain, whether the task will store a
	result - or where the task may fail - you can also use
	Test-PSFTaskEngineCache, in order to test whether anything was written to
	the cache (note: It will return true, when 'anything' - including $null -
	was written to the cache by the task).
	
	
	#-------------------------------------------------------------------------#
	#                                  Notes:                                 #
	#-------------------------------------------------------------------------#
	
	Tasks can be temporarily disabled or enabled by running:
	- Disable-PSFTaskEngineTask
	- Enable-PSFTaskEngineTask
	Tasks are enabled by default
	
	The runspace executing tasks is a managed runspace and can be controlled
	using those functions (*-PSFRunspace).
	
	The task engine runspace will self-terminate when there will be no more
	tasks to execute, in order to conserve resources.
	
	Registering a task or enabling a task will enable the task engine runspace,
	if it is stopped at the time.
	
	
KEYWORDS
	psframework taskengine