# PowerShell script to install the kbupdate module
Write-Host "Installing kbupdate PowerShell module..." -ForegroundColor Green

try {
    # Check if running as Administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    $isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    
    if (-not $isAdmin) {
        Write-Host "Warning: Not running as Administrator. Installing for current user only." -ForegroundColor Yellow
        Install-Module kbupdate -Force -Scope CurrentUser
    } else {
        Write-Host "Running as Administrator. Installing for all users." -ForegroundColor Green
        Install-Module kbupdate -Force -Scope AllUsers
    }
    
    # Test the module
    Import-Module kbupdate
    Write-Host "kbupdate module installed and imported successfully!" -ForegroundColor Green
    
    # Test basic functionality
    Write-Host "Testing basic functionality..." -ForegroundColor Yellow
    $testUpdate = Get-KbUpdate -Pattern "Windows 11" -Architecture x64 | Select-Object -First 1
    if ($testUpdate) {
        Write-Host "✓ Successfully found Windows 11 updates in Microsoft Update Catalog" -ForegroundColor Green
        Write-Host "Sample update: $($testUpdate.Title)" -ForegroundColor Cyan
    } else {
        Write-Host "⚠ No Windows 11 updates found, but module is working" -ForegroundColor Yellow
    }
    
    Write-Host "`nkbupdate module is ready to use!" -ForegroundColor Green
    Write-Host "You can now run the Windows Update Tool." -ForegroundColor Green
    
} catch {
    Write-Host "Error installing kbupdate module: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`nTroubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Make sure you have an internet connection" -ForegroundColor White
    Write-Host "2. Try running PowerShell as Administrator" -ForegroundColor White
    Write-Host "3. Run: Set-ExecutionPolicy RemoteSigned -Force" -ForegroundColor White
    Write-Host "4. Run: Install-Module kbupdate -Force" -ForegroundColor White
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
