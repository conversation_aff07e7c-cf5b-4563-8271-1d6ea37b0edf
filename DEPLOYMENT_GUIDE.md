# Windows Update Tool - Deployment Guide

This guide explains how to prepare and deploy the Windows Update Tool with the bundled kbupdate module for use on multiple systems.

## 🚀 Quick Deployment (Recommended)

### Step 1: Build and Bundle
Run the automated build script:
```powershell
.\build_and_bundle.ps1
```

This will:
- Build the application in Release mode
- Download and bundle the kbupdate module
- Create a complete distribution in the `dist` folder
- Include all necessary files for deployment

### Step 2: Deploy
Copy the entire `dist` folder to target systems. The application is now portable and self-contained.

## 📦 Manual Deployment

If you prefer manual control over the process:

### Step 1: Build the Application
```bash
dotnet build --configuration Release
```

### Step 2: Bundle kbupdate Module
```powershell
.\bundle_kbupdate.ps1
```

### Step 3: Copy Files
Copy these files/folders to target systems:
- `WindowsUpdateTool.exe`
- All DLL files from the build output
- `Modules\kbupdate\` folder (entire directory)
- `README.md` (optional)

## 🔧 How the Bundled Module Works

### Automatic Detection
The application automatically:
1. **Checks for bundled module** in `Modules\kbupdate\` folder
2. **Adds to PowerShell path** if found
3. **Falls back to system installation** if bundled version not available
4. **Auto-installs from PowerShell Gallery** as last resort

### Module Loading Priority
1. **Bundled module** (in application folder)
2. **User-installed module** (PowerShell Gallery)
3. **System-installed module** (if available)
4. **Auto-download** (if internet available)

## 📋 Distribution Checklist

### For System Administrators
- [ ] Run `build_and_bundle.ps1` on development machine
- [ ] Test the `dist` folder on a clean system
- [ ] Verify no internet connection required for basic operation
- [ ] Package for deployment (ZIP, MSI, etc.)

### For End Users
- [ ] Copy application folder to target system
- [ ] Run `WindowsUpdateTool.exe` or `Start_WindowsUpdateTool.bat`
- [ ] No additional installation required
- [ ] Administrator privileges needed only for installing updates

## 🌐 Network Requirements

### Online Mode (Recommended)
- Internet access for downloading updates from Microsoft Update Catalog
- No proxy configuration needed (uses system settings)

### Offline Mode
- Bundled kbupdate module works offline for basic functionality
- Cannot download new updates without internet
- Can install previously downloaded MSU files

## 🔒 Security Considerations

### PowerShell Execution Policy
The application may require PowerShell execution policy changes:
```powershell
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Administrator Privileges
- **Not required** for browsing and downloading updates
- **Required** for installing updates on the system
- Application will prompt when elevation is needed

## 🐛 Troubleshooting

### "kbupdate module not found"
1. Verify `Modules\kbupdate\` folder exists in application directory
2. Check PowerShell execution policy
3. Try running as Administrator
4. Manually install: `Install-Module kbupdate -Force`

### "Access denied" errors
1. Run application as Administrator
2. Check antivirus software blocking PowerShell
3. Verify Windows Update service is running

### "No updates found"
1. Check internet connection
2. Verify Microsoft Update Catalog is accessible
3. Try different search criteria or time ranges

## 📁 File Structure

```
WindowsUpdateTool/
├── WindowsUpdateTool.exe          # Main application
├── WindowsUpdateTool.dll          # Application library
├── Newtonsoft.Json.dll           # JSON processing
├── System.Management.dll         # System management
├── Modules/                      # Bundled PowerShell modules
│   └── kbupdate/                # kbupdate module files
│       ├── kbupdate.psd1        # Module manifest
│       ├── kbupdate.psm1        # Module script
│       └── [other module files] # Dependencies
├── README.md                     # User documentation
├── Start_WindowsUpdateTool.bat   # Launcher script
└── [other supporting files]     # Runtime dependencies
```

## 🔄 Updates and Maintenance

### Updating the Bundled Module
To update the bundled kbupdate module:
1. Run `bundle_kbupdate.ps1` to download latest version
2. Rebuild and redistribute the application

### Application Updates
- Replace application files while preserving `Modules` folder
- Or rebuild entire distribution with `build_and_bundle.ps1`

## 💡 Best Practices

### For Deployment
- Test on clean systems before wide deployment
- Include `Start_WindowsUpdateTool.bat` for user convenience
- Document any specific network or security requirements
- Consider creating an installer (MSI) for enterprise deployment

### For Users
- Run as Administrator when installing updates
- Keep application folder intact (don't separate files)
- Regular internet connection recommended for latest updates
- Close other applications before installing system updates

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all files are present in the application folder
3. Test PowerShell module manually: `Import-Module .\Modules\kbupdate\kbupdate.psd1`
4. Check Windows Event Logs for detailed error information
