# PowerShell script to download and bundle the kbupdate module with the application
# Run this script to prepare the application for distribution

Write-Host "Bundling kbupdate module with Windows Update Tool..." -ForegroundColor Green

try {
    # Get the application directory
    $appPath = Split-Path -Parent $MyInvocation.MyCommand.Path
    $modulesPath = Join-Path $appPath "Modules"
    $kbUpdatePath = Join-Path $modulesPath "kbupdate"
    
    Write-Host "Application path: $appPath" -ForegroundColor Cyan
    Write-Host "Modules path: $modulesPath" -ForegroundColor Cyan
    
    # Create modules directory if it doesn't exist
    if (-not (Test-Path $modulesPath)) {
        Write-Host "Creating Modules directory..." -ForegroundColor Yellow
        New-Item -Path $modulesPath -ItemType Directory -Force | Out-Null
    }
    
    # Remove existing kbupdate module if present
    if (Test-Path $kbUpdatePath) {
        Write-Host "Removing existing kbupdate module..." -ForegroundColor Yellow
        Remove-Item -Path $kbUpdatePath -Recurse -Force
    }
    
    # Download the kbupdate module
    Write-Host "Downloading kbupdate module from PowerShell Gallery..." -ForegroundColor Yellow
    Save-Module -Name kbupdate -Path $modulesPath -Force
    
    # Verify the module was downloaded
    if (Test-Path $kbUpdatePath) {
        $moduleFiles = Get-ChildItem -Path $kbUpdatePath -Recurse | Measure-Object
        Write-Host "✓ kbupdate module downloaded successfully!" -ForegroundColor Green
        Write-Host "  Files: $($moduleFiles.Count)" -ForegroundColor Cyan
        Write-Host "  Location: $kbUpdatePath" -ForegroundColor Cyan
        
        # Test the module
        Write-Host "Testing bundled module..." -ForegroundColor Yellow
        $env:PSModulePath = "$modulesPath;" + $env:PSModulePath
        Import-Module kbupdate -Force
        
        # Get module info
        $moduleInfo = Get-Module kbupdate
        Write-Host "✓ Module loaded successfully!" -ForegroundColor Green
        Write-Host "  Version: $($moduleInfo.Version)" -ForegroundColor Cyan
        Write-Host "  Author: $($moduleInfo.Author)" -ForegroundColor Cyan
        
        # Test basic functionality
        Write-Host "Testing basic functionality..." -ForegroundColor Yellow
        $testCommand = Get-Command Get-KbUpdate -Module kbupdate -ErrorAction SilentlyContinue
        if ($testCommand) {
            Write-Host "✓ Get-KbUpdate command available" -ForegroundColor Green
        } else {
            Write-Host "⚠ Get-KbUpdate command not found" -ForegroundColor Red
        }
        
        Write-Host "`n🎉 kbupdate module bundled successfully!" -ForegroundColor Green
        Write-Host "The Windows Update Tool can now be distributed with the kbupdate module included." -ForegroundColor Green
        Write-Host "Users will not need to install the module separately." -ForegroundColor Green
        
    } else {
        throw "Module download failed - directory not found"
    }
    
} catch {
    Write-Host "❌ Error bundling kbupdate module: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "`nTroubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Make sure you have an internet connection" -ForegroundColor White
    Write-Host "2. Try running PowerShell as Administrator" -ForegroundColor White
    Write-Host "3. Run: Set-ExecutionPolicy RemoteSigned -Force" -ForegroundColor White
    Write-Host "4. Check if PowerShell Gallery is accessible: Get-PSRepository" -ForegroundColor White
    exit 1
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
