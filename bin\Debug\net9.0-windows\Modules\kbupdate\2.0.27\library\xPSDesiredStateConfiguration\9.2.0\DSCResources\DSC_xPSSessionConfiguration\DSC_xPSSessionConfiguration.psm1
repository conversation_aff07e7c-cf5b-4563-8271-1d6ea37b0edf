$errorActionPreference = 'Stop'
Set-StrictMode -Version 'Latest'

$modulePath = Join-Path -Path (Split-Path -Path (Split-Path -Path $PSScriptRoot -Parent) -Parent) -ChildPath 'Modules'

# Import the shared modules
Import-Module -Name (Join-Path -Path $modulePath `
    -ChildPath (Join-Path -Path 'xPSDesiredStateConfiguration.Common' `
        -ChildPath 'xPSDesiredStateConfiguration.Common.psm1'))

Import-Module -Name (Join-Path -Path $modulePath -ChildPath 'DscResource.Common')

# Import Localization Strings
$script:localizedData = Get-LocalizedData -DefaultUICulture 'en-US'

<#
    .SYNOPSIS
        Returns the current state of the specified PSSessionConfiguration

    .PARAMETER Name
        Specifies the name of the session configuration.
#>
function Get-TargetResource
{
    [CmdletBinding()]
    [OutputType([System.Collections.Hashtable])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name
    )

    Write-Verbose -Message ($script:localizedData.GetTargetResourceStartMessage -f $Name)

    # Try getting the specified endpoint
    $endpoint = Get-PSSessionConfiguration -Name $Name -ErrorAction SilentlyContinue -Verbose:$false

    # If endpoint is null, it is absent
    if ($null -eq $endpoint)
    {
        $ensure = 'Absent'
    }
    # If endpoint is present, check other properties
    else
    {
        $ensure = 'Present'

        # If runAsUser is specified, return only the username in the credential property
        if ($endpoint.RunAsUser)
        {
            $newCimInstanceParams = @{
                ClassName  = 'MSFT_Credential'

                Property   = @{
                    Username = [System.String] $endpoint.RunAsUser
                    Password = [System.String] $null
                }

                Namespace  = 'root/microsoft/windows/desiredstateconfiguration'
                ClientOnly = $true
            }

            $convertToCimCredential = New-CimInstance @newCimInstanceParams
        }

        $accessMode = Get-EndpointAccessMode -Endpoint $endpoint
    }

    @{
        Name                   = $Name
        RunAsCredential        = [Microsoft.Management.Infrastructure.CimInstance] $convertToCimCredential
        SecurityDescriptorSDDL = $endpoint.Permission
        StartupScript          = $endpoint.StartupScript
        AccessMode             = $accessMode
        Ensure                 = $ensure
    }

    Write-Verbose -Message ($script:localizedData.GetTargetResourceEndMessage -f $Name)
}

<#
    .SYNOPSIS
        Ensures the specified PSSessionConfiguration is in its desired state

    .PARAMETER Name
        Specifies the name of the session configuration.

    .PARAMETER StartupScript
        Specifies the startup script for the configuration.
        Enter the fully qualified path of a Windows PowerShell script.

    .PARAMETER RunAsCredential
        Specifies the credential for commands of this session configuration. By default, commands
        run with the permissions of the current user.

    .PARAMETER SecurityDescriptorSDDL
        Specifies the Security Descriptor Definition Language (SDDL) string for the configuration.
        This string determines the permissions that are required to use the new session configuration.
        To use a session configuration in a session, users must have at least Execute(Invoke)
        permission for the configuration.

    .PARAMETER AccessMode
        Enables and disables the session configuration and determines whether it can be used for
        remote or local sessions on the computer. The default value is 'Remote'.

    .PARAMETER Ensure
        Indicates if the session configuration should exist. The default value is 'Present'.
#>
function Set-TargetResource
{
    [CmdletBinding(SupportsShouldProcess = $true)]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [AllowEmptyString()]
        [System.String]
        $StartupScript,

        [Parameter()]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $RunAsCredential,

        [Parameter()]
        [System.String]
        $SecurityDescriptorSDDL,

        [Parameter()]
        [ValidateSet('Local', 'Remote', 'Disabled')]
        [System.String]
        $AccessMode = 'Remote',

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present'
    )

    Write-Verbose -Message ($script:localizedData.SetTargetResourceStartMessage -f $Name)

    # Check if the session configuration exists
    Write-Verbose -Message ($script:localizedData.CheckEndpointMessage -f $Name)

    # Try to get a named session configuration
    $endpoint = Get-PSSessionConfiguration -Name $Name -ErrorAction SilentlyContinue -Verbose:$false

    if ($PSCmdlet.ShouldProcess(($script:localizedData.EnsureSessionConfigurationMessage -f $Ensure)))
    {
        # If endpoint is present, set ensure correctly
        if ($endpoint)
        {
            Write-Verbose -Message ($script:localizedData.EndpointNameMessage -f $Name, 'present')

            # If the endpoint should be absent, delete the endpoint
            if ($Ensure -eq 'Absent')
            {
                try
                {
                    <#
                        Set the following preference so the functions inside Unregister-PSSessionConfig
                        doesn't get these settings
                    #>
                    $oldDebugPrefernce = $DebugPreference
                    $oldVerbosePreference = $VerbosePreference
                    $DebugPreference = $VerbosePreference = "SilentlyContinue"

                    $unregisterPSSessionConfigParams = @{
                        Name             = $Name
                        Force            = $true
                        NoServiceRestart = $true
                        ErrorAction      = 'Stop'
                    }

                    $null = Unregister-PSSessionConfiguration @unregisterPSSessionConfigParams

                    # Reset the following preference to older values
                    $DebugPreference = $oldDebugPrefernce
                    $VerbosePreference = $oldVerbosePreference

                    Write-Verbose -Message ($script:localizedData.EndpointNameMessage -f $Name, 'absent')

                    $restartNeeded = $true
                }
                catch
                {
                    $invokeThrowErrorHelperParams = @{
                        ErrorId       = 'UnregisterPSSessionConfigurationFailed'
                        ErrorMessage  = $_.Exception
                        ErrorCategory = 'InvalidOperation'
                    }

                    Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
                }

            }

            # else validate endpoint properties and return the result
            else
            {
                # Remove Name and Ensure from the bound Parameters for splatting
                if ($PSBoundParameters.ContainsKey('Name'))
                {
                    $null = $PSBoundParameters.Remove('Name')
                }

                if ($PSBoundParameters.ContainsKey('Ensure'))
                {
                    $null = $PSBoundParameters.Remove('Ensure')
                }

                [System.Collections.Hashtable] $validatedProperties = (
                    Get-ValidatedResourcePropertyTable -Endpoint $endpoint @PSBoundParameters -Apply
                )
                $null = $validatedProperties.Add('Name', $Name)

                # If the $validatedProperties contain more than 1 key, something needs to be changed
                if ($validatedProperties.count -gt 1)
                {
                    try
                    {
                        $setPSSessionConfigurationParams = $validatedProperties.psobject.Copy()
                        $setPSSessionConfigurationParams['Force'] = $true
                        $setPSSessionConfigurationParams['NoServiceRestart'] = $true
                        $setPSSessionConfigurationParams['Verbose'] = $false
                        $null = Set-PSSessionConfiguration @setPSSessionConfigurationParams
                        $restartNeeded = $true

                        # Write verbose message for all the properties, except Name, that are changing
                        Write-EndpointMessage -Parameters $validatedProperties -keysToSkip 'Name'
                    }
                    catch
                    {
                        $invokeThrowErrorHelperParams = @{
                            ErrorId       = 'SetPSSessionConfigurationFailed'
                            ErrorMessage  = $_.Exception
                            ErrorCategory = 'InvalidOperation'
                        }

                        Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
                    }
                }
            }
        }
        else
        {
            # Named session configuration is absent
            Write-Verbose -Message ($script:localizedData.EndpointNameMessage -f $Name, 'absent')

            # If the endpoint should have been present, create it
            if ($Ensure -eq 'Present')
            {
                # Remove Ensure,Verbose,Debug from the bound Parameters for splatting
                foreach ($key in @('Ensure', 'Verbose', 'Debug'))
                {
                    if ($PSBoundParameters.ContainsKey($key))
                    {
                        $null = $PSBoundParameters.Remove($key)
                    }
                }

                # Register the endpoint with specified properties
                try
                {
                    <#
                        Set the following preference so the functions inside
                        Unregister-PSSessionConfig doesn't get these settings
                    #>
                    $oldDebugPrefernce = $DebugPreference
                    $oldVerbosePreference = $VerbosePreference
                    $DebugPreference = $VerbosePreference = "SilentlyContinue"

                    $null = Register-PSSessionConfiguration @PSBoundParameters -Force -NoServiceRestart

                    # Reset the following preference to older values
                    $DebugPreference = $oldDebugPrefernce
                    $VerbosePreference = $oldVerbosePreference

                    # If access mode is specified, set it on the endpoint
                    if ($PSBoundParameters.ContainsKey('AccessMode') -and $AccessMode -ne 'Remote')
                    {
                        $setPSSessionConfigurationParams = @{
                            Name             = $Name
                            AccessMode       = $AccessMode
                            Force            = $true
                            NoServiceRestart = $true
                            Verbose          = $false
                        }

                        $null = Set-PSSessionConfiguration @setPSSessionConfigurationParams
                    }

                    $restartNeeded = $true

                    Write-Verbose -Message ($script:localizedData.EndpointNameMessage -f $Name, 'present')
                }
                catch
                {
                    $invokeThrowErrorHelperParams = @{
                        ErrorId       = 'RegisterOrSetPSSessionConfigurationFailed'
                        ErrorMessage  = $_.Exception
                        ErrorCategory = 'InvalidOperation'
                    }

                    Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
                }
            }
        }

        <#
            Any change to existing endpoint or creating new endpoint requires WinRM restart.
            Since DSC(CIM) uses WSMan as well it will stop responding.
            Hence telling the DSC Engine to restart the machine
        #>
        if ($restartNeeded)
        {
            Set-DscMachineRebootRequired
        }
    }

    Write-Verbose -Message ($script:localizedData.SetTargetResourceEndMessage -f $Name)
}

<#
    .SYNOPSIS
        Tests if the specified PSSessionConfiguration is in its desired state

    .PARAMETER Name
        Specifies the name of the session configuration.

    .PARAMETER StartupScript
        Specifies the startup script for the configuration.
        Enter the fully qualified path of a Windows PowerShell script.

    .PARAMETER RunAsCredential
        Specifies the credential for commands of this session configuration. By default, commands
        run with the permissions of the current user.

    .PARAMETER SecurityDescriptorSDDL
        Specifies the Security Descriptor Definition Language (SDDL) string for the configuration.
        This string determines the permissions that are required to use the new session configuration.
        To use a session configuration in a session, users must have at least Execute(Invoke)
        permission for the configuration.

    .PARAMETER AccessMode
        Enables and disables the session configuration and determines whether it can be used for
        remote or local sessions on the computer. The default value is 'Remote'.

    .PARAMETER Ensure
        Indicates if the session configuration should exist. The default value is 'Present'.
#>
function Test-TargetResource
{
    [CmdletBinding()]
    [OutputType([System.Boolean])]
    param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [System.String]
        $Name,

        [Parameter()]
        [AllowEmptyString()]
        [System.String]
        $StartupScript,

        [Parameter()]
        [System.Management.Automation.PSCredential]
        [System.Management.Automation.Credential()]
        $RunAsCredential,

        [Parameter()]
        [System.String]
        $SecurityDescriptorSDDL,

        [Parameter()]
        [ValidateSet('Local', 'Remote', 'Disabled')]
        [System.String]
        $AccessMode = 'Remote',

        [Parameter()]
        [ValidateSet('Present', 'Absent')]
        [System.String]
        $Ensure = 'Present'
    )

    Write-Verbose -Message ($script:localizedData.TestTargetResourceStartMessage -f $Name)

    #region Input Validation
    # Check if the endpoint name is blank/whitespaced string
    if ([System.String]::IsNullOrWhiteSpace($Name))
    {
        $invokeThrowErrorHelperParams = @{
            ErrorId       = 'BlankString'
            ErrorMessage  = $script:localizedData.WhitespacedStringMessage -f 'name'
            ErrorCategory = 'SyntaxError'
        }

        Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
    }

    # Check for Startup script path and extension
    if ($PSBoundParameters.ContainsKey('StartupScript'))
    {
        # Check if startup script path is valid
        if (!(Test-Path -Path $StartupScript))
        {
            $invokeThrowErrorHelperParams = @{
                ErrorId       = 'PathNotFound'
                ErrorMessage  = $script:localizedData.StartupPathNotFoundMessage -f $StartupScript
                ErrorCategory = 'ObjectNotFound'
            }

            Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
        }

        # Check the startup script extension
        $startupScriptFileExtension = $StartupScript.Split('.')[-1]

        if ($startupScriptFileExtension -ne 'ps1')
        {
            $invokeThrowErrorHelperParams = @{
                ErrorId       = 'WrongFileExtension'
                ErrorMessage  =
                $script:localizedData.WrongStartupScriptExtensionMessage -f $startupScriptFileExtension
                ErrorCategory = 'InvalidData'
            }

            Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
        }
    }

    # Check if SecurityDescriptorSDDL is whitespaced
    if ($PSBoundParameters.ContainsKey('SecurityDescriptorSDDL') -and
        [System.String]::IsNullOrWhiteSpace($SecurityDescriptorSDDL))
    {
        $invokeThrowErrorHelperParams = @{
            ErrorId       = 'BlankString'
            ErrorMessage  = $script:localizedData.WhitespacedStringMessage -f 'securityDescriptorSddl'
            ErrorCategory = 'SyntaxError'
        }

        Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
    }

    # Check if the RunAsCredential is not empty
    if ($PSBoundParameters.ContainsKey('RunAsCredential') -and
        ($RunAsCredential -eq [System.Management.Automation.PSCredential]::Empty))
    {
        $invokeThrowErrorHelperParams = @{
            ErrorId       = 'EmptyCredential'
            ErrorMessage  = $script:localizedData.EmptyCredentialMessage
            ErrorCategory = 'InvalidArgument'
        }

        Invoke-ThrowErrorHelper @invokeThrowErrorHelperParams
    }
    #endregion

    # Check if the session configuration exists
    Write-Verbose -Message ($script:localizedData.CheckEndpointMessage -f $Name)

    try
    {
        # Try to get a named session configuration
        $endpoint = Get-PSSessionConfiguration -Name $Name -ErrorAction Stop -Verbose:$false

        Write-Verbose -Message ($script:localizedData.EndpointNameMessage -f $Name, 'present')

        # If the endpoint shouldn't be present, return false
        if ($Ensure -eq 'Absent')
        {
            return $false
        }
        # else validate endpoint properties and return the result
        else
        {
            # Remove Name and Ensure from the bound Parameters for splatting
            if ($PSBoundParameters.ContainsKey('Name'))
            {
                $null = $PSBoundParameters.Remove('Name')
            }

            if ($PSBoundParameters.ContainsKey('Ensure'))
            {
                $null = $PSBoundParameters.Remove('Ensure')
            }

            return (Get-ValidatedResourcePropertyTable -Endpoint $endpoint @PSBoundParameters)
        }
    }
    catch [Microsoft.PowerShell.Commands.WriteErrorException]
    {
        Write-Verbose -Message ($script:localizedData.EndpointNameMessage -f $Name, 'absent')

        return ($Ensure -eq 'Absent')
    }

    Write-Verbose -Message ($script:localizedData.TestTargetResourceEndMessage -f $Name)
}

<#
    .SYNOPSIS
        Helper function to translate the endpoint's accessmode
        to the 'Disabled', 'Local', 'Remote' values

    .PARAMETER Endpoint
        Specifies a valid session configuration endpoint object
#>
function Get-EndpointAccessMode
{
    [CmdletBinding()]
    [OutputType([System.String])]
    param
    (
        [Parameter(Mandatory = $true)]
        $Endpoint
    )

    if (-not $endpoint.Enabled)
    {
        return 'Disabled'
    }
    elseif ($endpoint.Permission -and
        ($endpoint.Permission).contains('NT AUTHORITY\NETWORK AccessDenied'))
    {
        return 'Local'
    }
    else
    {
        return 'Remote'
    }
}

<#
    .SYNOPSIS
        Helper function to write verbose messages for collection of properties

    .PARAMETER Parameters
        Specifies a properties Hashtable.

    .PARAMETER KeysToSkip
        Specifies an array of Hashtable keys to ignore.
#>
function Write-EndpointMessage
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [System.Collections.Hashtable]
        $Parameters,

        [Parameter(Mandatory = $true)]
        [System.String[]]
        $KeysToSkip
    )

    foreach ($key in $Parameters.keys)
    {
        if ($KeysToSkip -notcontains $key)
        {
            Write-Verbose -Message ($script:localizedData.SetPropertyMessage -f $key, $Parameters[$key])
        }
    }
}

<#
    .SYNOPSIS
        Helper function to get a Hashtable of validated endpoint properties

    .PARAMETER Endpoint
        Specifies a valid session configuration endpoint.

    .PARAMETER StartupScript
        Specifies the startup script for the configuration.
        Enter the fully qualified path of a Windows PowerShell script.

    .PARAMETER RunAsCredential
        Specifies the credential for commands of this session configuration.

    .PARAMETER SecurityDescriptorSDDL
        Specifies the Security Descriptor Definition Language (SDDL) string for the configuration.

    .PARAMETER AccessMode
        Enables and disables the session configuration and determines whether it can be used for
        remote or local sessions on the computer.

        The acceptable values for this parameter are:
        - Disabled
        - Local
        - Remote

    .PARAMETER Apply
        Indicates that this function should return a hashtable of validated endpoint properties.
        By default, this function returns the value $false.
#>
function Get-ValidatedResourcePropertyTable
{
    [CmdletBinding()]
    [OutputType([System.Boolean])]
    param
    (
        [Parameter(Mandatory = $true)]
        $Endpoint,

        [Parameter()]
        [System.String]
        $StartupScript,

        [Parameter()]
        [System.Management.Automation.PSCredential]
        $RunAsCredential,

        [Parameter()]
        [System.String]
        $SecurityDescriptorSDDL,

        [Parameter()]
        [ValidateSet('Local', 'Remote', 'Disabled')]
        [System.String]
        $AccessMode,

        [Parameter()]
        [System.Management.Automation.SwitchParameter]
        $Apply
    )

    if ($Apply)
    {
        $validatedProperties = @{}
    }

    # Check if the SDDL is same as specified
    if ($PSBoundParameters.ContainsKey('SecurityDescriptorSDDL'))
    {
        Write-Verbose -Message ($script:localizedData.CheckPropertyMessage -f 'SDDL',
            $SecurityDescriptorSDDL)

        # If endpoint SDDL is not same as specified
        if ($endpoint.SecurityDescriptorSddl -and
            ($endpoint.SecurityDescriptorSddl -ne $SecurityDescriptorSDDL))
        {
            $notDesiredSDDLMessage = $script:localizedData.NotDesiredPropertyMessage -f 'SDDL',
            $SecurityDescriptorSDDL, $endpoint.SecurityDescriptorSddl
            Write-Verbose -Message $notDesiredSDDLMessage

            if ($Apply)
            {
                $validatedProperties['SecurityDescriptorSddl'] = $SecurityDescriptorSDDL
            }
            else
            {
                return $false
            }
        }
        # If endpoint SDDL is same as specified
        else
        {
            Write-Verbose -Message ($script:localizedData.DesiredPropertyMessage -f 'SDDL',
                $SecurityDescriptorSDDL)
        }
    }

    # Check the RunAs user is same as specified
    if ($PSBoundParameters.ContainsKey('RunAsCredential'))
    {
        Write-Verbose -Message ($script:localizedData.CheckPropertyMessage -f 'RunAs user',
            $RunAsCredential.UserName)

        # If endpoint RunAsUser is not same as specified
        if ($endpoint.RunAsUser -ne $RunAsCredential.UserName)
        {
            Write-Verbose -Message ($script:localizedData.NotDesiredPropertyMessage -f 'RunAs user',
                $RunAsCredential.UserName, $endpoint.RunAsUser)

            if ($Apply)
            {
                $validatedProperties['RunAsCredential'] = $RunAsCredential
            }
            else
            {
                return $false
            }
        }
        # If endpoint RunAsUser is same as specified
        else
        {
            Write-Verbose -Message ($script:localizedData.DesiredPropertyMessage -f 'RunAs user',
                $RunAsCredential.UserName)
        }
    }

    # Check if the StartupScript is same as specified
    if ($PSBoundParameters.ContainsKey('StartupScript'))
    {
        Write-Verbose -Message ($script:localizedData.CheckPropertyMessage -f 'startup script',
            $StartupScript)

        # If endpoint StartupScript is not same as specified
        if ($endpoint.StartupScript -ne $StartupScript)
        {
            Write-Verbose -Message ($script:localizedData.NotDesiredPropertyMessage -f 'startup script',
                $StartupScript, $endpoint.StartupScript)

            if ($Apply)
            {
                $validatedProperties['StartupScript'] = $StartupScript
            }
            else
            {
                return $false
            }
        }
        # If endpoint StartupScript is same as specified
        else
        {
            Write-Verbose -Message ($script:localizedData.DesiredPropertyMessage -f 'startup script',
                $StartupScript)
        }
    }

    # Check if AccessMode is same as specified
    if ($PSBoundParameters.ContainsKey('AccessMode'))
    {
        Write-Verbose -Message ($script:localizedData.CheckPropertyMessage -f 'acess mode', $AccessMode)

        $curAccessMode = Get-EndpointAccessMode -Endpoint $Endpoint

        # If endpoint access mode is not same as specified
        if ($curAccessMode -ne $AccessMode)
        {
            Write-Verbose -Message ($script:localizedData.NotDesiredPropertyMessage -f 'access mode',
                $AccessMode, $curAccessMode)

            if ($Apply)
            {
                $validatedProperties['AccessMode'] = $AccessMode
            }
            else
            {
                return $false
            }
        }
        # If endpoint access mode is same as specified
        else
        {
            Write-Verbose -Message ($script:localizedData.DesiredPropertyMessage -f 'access mode',
                $AccessMode)
        }
    }

    if ($Apply)
    {
        return $validatedProperties
    }
    else
    {
        return ($Ensure -eq 'Present')
    }
}

<#
    .SYNOPSIS
        Invoke this helper function to throw a terminating error.

    .PARAMETER ErrorId
        Specifies a developer-defined identifier of the error.
        This identifier must be a non-localized string for a specific error type.

    .PARAMETER ExceptionMessage
        Specifies the message that describes the error.

    .PARAMETER ErrorCategory
        Specifies the category of the error.
#>
function Invoke-ThrowErrorHelper
{
    [CmdletBinding()]
    param
    (
        [Parameter(Mandatory = $true)]
        [System.String]
        $ErrorId,

        [Parameter(Mandatory = $true)]
        [System.String]
        $ErrorMessage,

        [Parameter(Mandatory = $true)]
        [System.Management.Automation.ErrorCategory]
        $ErrorCategory
    )

    $exception = New-Object System.InvalidOperationException $ErrorMessage
    $errorRecord = New-Object System.Management.Automation.ErrorRecord $exception, $ErrorId,
    $ErrorCategory, $null

    throw $errorRecord
}

Export-ModuleMember -Function Get-TargetResource, Set-TargetResource, Test-TargetResource

# SIG # Begin signature block
# MIIjYAYJKoZIhvcNAQcCoIIjUTCCI00CAQExDzANBglghkgBZQMEAgEFADB5Bgor
# BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG
# KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCBZNitNPHKXDdK/
# 1MIm7rIDpe1BS1TJ/9KXZ7q3QaFcuKCCHVkwggUaMIIEAqADAgECAhADBbuGIbCh
# Y1+/3q4SBOdtMA0GCSqGSIb3DQEBCwUAMHIxCzAJBgNVBAYTAlVTMRUwEwYDVQQK
# EwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5jb20xMTAvBgNV
# BAMTKERpZ2lDZXJ0IFNIQTIgQXNzdXJlZCBJRCBDb2RlIFNpZ25pbmcgQ0EwHhcN
# MjAwNTEyMDAwMDAwWhcNMjMwNjA4MTIwMDAwWjBXMQswCQYDVQQGEwJVUzERMA8G
# A1UECBMIVmlyZ2luaWExDzANBgNVBAcTBlZpZW5uYTERMA8GA1UEChMIZGJhdG9v
# bHMxETAPBgNVBAMTCGRiYXRvb2xzMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
# CgKCAQEAvL9je6vjv74IAbaY5rXqHxaNeNJO9yV0ObDg+kC844Io2vrHKGD8U5hU
# iJp6rY32RVprnAFrA4jFVa6P+sho7F5iSVAO6A+QZTHQCn7oquOefGATo43NAadz
# W2OWRro3QprMPZah0QFYpej9WaQL9w/08lVaugIw7CWPsa0S/YjHPGKQ+bYgI/kr
# EUrk+asD7lvNwckR6pGieWAyf0fNmSoevQBTV6Cd8QiUfj+/qWvLW3UoEX9ucOGX
# 2D8vSJxL7JyEVWTHg447hr6q9PzGq+91CO/c9DWFvNMjf+1c5a71fEZ54h1mNom/
# XoWZYoKeWhKnVdv1xVT1eEimibPEfQIDAQABo4IBxTCCAcEwHwYDVR0jBBgwFoAU
# WsS5eyoKo6XqcQPAYPkt9mV1DlgwHQYDVR0OBBYEFPDAoPu2A4BDTvsJ193ferHL
# 454iMA4GA1UdDwEB/wQEAwIHgDATBgNVHSUEDDAKBggrBgEFBQcDAzB3BgNVHR8E
# cDBuMDWgM6Axhi9odHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc2hhMi1hc3N1cmVk
# LWNzLWcxLmNybDA1oDOgMYYvaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NoYTIt
# YXNzdXJlZC1jcy1nMS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAwEwKjAoBggr
# BgEFBQcCARYcaHR0cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBBAEw
# gYQGCCsGAQUFBwEBBHgwdjAkBggrBgEFBQcwAYYYaHR0cDovL29jc3AuZGlnaWNl
# cnQuY29tME4GCCsGAQUFBzAChkJodHRwOi8vY2FjZXJ0cy5kaWdpY2VydC5jb20v
# RGlnaUNlcnRTSEEyQXNzdXJlZElEQ29kZVNpZ25pbmdDQS5jcnQwDAYDVR0TAQH/
# BAIwADANBgkqhkiG9w0BAQsFAAOCAQEAj835cJUMH9Y2pBKspjznNJwcYmOxeBcH
# Ji+yK0y4bm+j44OGWH4gu/QJM+WjZajvkydJKoJZH5zrHI3ykM8w8HGbYS1WZfN4
# oMwi51jKPGZPw9neGS2PXrBcKjzb7rlQ6x74Iex+gyf8z1ZuRDitLJY09FEOh0BM
# LaLh+UvJ66ghmfIyjP/g3iZZvqwgBhn+01fObqrAJ+SagxJ/21xNQJchtUOWIlxR
# kuUn9KkuDYrMO70a2ekHODcAbcuHAGI8wzw4saK1iPPhVTlFijHS+7VfIt/d/18p
# MLHHArLQQqe1Z0mTfuL4M4xCUKpebkH8rI3Fva62/6osaXLD0ymERzCCBTAwggQY
# oAMCAQICEAQJGBtf1btmdVNDtW+VUAgwDQYJKoZIhvcNAQELBQAwZTELMAkGA1UE
# BhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2lj
# ZXJ0LmNvbTEkMCIGA1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4X
# DTEzMTAyMjEyMDAwMFoXDTI4MTAyMjEyMDAwMFowcjELMAkGA1UEBhMCVVMxFTAT
# BgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEx
# MC8GA1UEAxMoRGlnaUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBD
# QTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAPjTsxx/DhGvZ3cH0wsx
# SRnP0PtFmbE620T1f+Wondsy13Hqdp0FLreP+pJDwKX5idQ3Gde2qvCchqXYJawO
# eSg6funRZ9PG+yknx9N7I5TkkSOWkHeC+aGEI2YSVDNQdLEoJrskacLCUvIUZ4qJ
# RdQtoaPpiCwgla4cSocI3wz14k1gGL6qxLKucDFmM3E+rHCiq85/6XzLkqHlOzEc
# z+ryCuRXu0q16XTmK/5sy350OTYNkO/ktU6kqepqCquE86xnTrXE94zRICUj6whk
# PlKWwfIPEvTFjg/BougsUfdzvL2FsWKDc0GCB+Q4i2pzINAPZHM8np+mM6n9Gd8l
# k9ECAwEAAaOCAc0wggHJMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYDVR0PAQH/BAQD
# AgGGMBMGA1UdJQQMMAoGCCsGAQUFBwMDMHkGCCsGAQUFBwEBBG0wazAkBggrBgEF
# BQcwAYYYaHR0cDovL29jc3AuZGlnaWNlcnQuY29tMEMGCCsGAQUFBzAChjdodHRw
# Oi8vY2FjZXJ0cy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0Eu
# Y3J0MIGBBgNVHR8EejB4MDqgOKA2hjRodHRwOi8vY3JsNC5kaWdpY2VydC5jb20v
# RGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsMDqgOKA2hjRodHRwOi8vY3JsMy5k
# aWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290Q0EuY3JsME8GA1UdIARI
# MEYwOAYKYIZIAYb9bAACBDAqMCgGCCsGAQUFBwIBFhxodHRwczovL3d3dy5kaWdp
# Y2VydC5jb20vQ1BTMAoGCGCGSAGG/WwDMB0GA1UdDgQWBBRaxLl7KgqjpepxA8Bg
# +S32ZXUOWDAfBgNVHSMEGDAWgBRF66Kv9JLLgjEtUYunpyGd823IDzANBgkqhkiG
# 9w0BAQsFAAOCAQEAPuwNWiSz8yLRFcgsfCUpdqgdXRwtOhrE7zBh134LYP3DPQ/E
# r4v97yrfIFU3sOH20ZJ1D1G0bqWOWuJeJIFOEKTuP3GOYw4TS63XX0R58zYUBor3
# nEZOXP+QsRsHDpEV+7qvtVHCjSSuJMbHJyqhKSgaOnEoAjwukaPAJRHinBRHoXpo
# aK+bp1wgXNlxsQyPu6j4xRJon89Ay0BEpRPw5mQMJQhCMrI2iiQC/i9yfhzXSUWW
# 6Fkd6fp0ZGuy62ZD2rOwjNXpDd32ASDOmTFjPQgaGLOBm0/GkxAG/AeB+ova+YJJ
# 92JuoVP6EpQYhS6SkepobEQysmah5xikmmRR7zCCBY0wggR1oAMCAQICEA6bGI75
# 0C3n79tQ4ghAGFowDQYJKoZIhvcNAQEMBQAwZTELMAkGA1UEBhMCVVMxFTATBgNV
# BAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEkMCIG
# A1UEAxMbRGlnaUNlcnQgQXNzdXJlZCBJRCBSb290IENBMB4XDTIyMDgwMTAwMDAw
# MFoXDTMxMTEwOTIzNTk1OVowYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGln
# aUNlcnQgVHJ1c3RlZCBSb290IEc0MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIIC
# CgKCAgEAv+aQc2jeu+RdSjwwIjBpM+zCpyUuySE98orYWcLhKac9WKt2ms2uexuE
# DcQwH/MbpDgW61bGl20dq7J58soR0uRf1gU8Ug9SH8aeFaV+vp+pVxZZVXKvaJNw
# wrK6dZlqczKU0RBEEC7fgvMHhOZ0O21x4i0MG+4g1ckgHWMpLc7sXk7Ik/ghYZs0
# 6wXGXuxbGrzryc/NrDRAX7F6Zu53yEioZldXn1RYjgwrt0+nMNlW7sp7XeOtyU9e
# 5TXnMcvak17cjo+A2raRmECQecN4x7axxLVqGDgDEI3Y1DekLgV9iPWCPhCRcKtV
# gkEy19sEcypukQF8IUzUvK4bA3VdeGbZOjFEmjNAvwjXWkmkwuapoGfdpCe8oU85
# tRFYF/ckXEaPZPfBaYh2mHY9WV1CdoeJl2l6SPDgohIbZpp0yt5LHucOY67m1O+S
# kjqePdwA5EUlibaaRBkrfsCUtNJhbesz2cXfSwQAzH0clcOP9yGyshG3u3/y1Yxw
# LEFgqrFjGESVGnZifvaAsPvoZKYz0YkH4b235kOkGLimdwHhD5QMIR2yVCkliWzl
# DlJRR3S+Jqy2QXXeeqxfjT/JvNNBERJb5RBQ6zHFynIWIgnffEx1P2PsIV/EIFFr
# b7GrhotPwtZFX50g/KEexcCPorF+CiaZ9eRpL5gdLfXZqbId5RsCAwEAAaOCATow
# ggE2MA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFOzX44LScV1kTN8uZz/nupiu
# HA9PMB8GA1UdIwQYMBaAFEXroq/0ksuCMS1Ri6enIZ3zbcgPMA4GA1UdDwEB/wQE
# AwIBhjB5BggrBgEFBQcBAQRtMGswJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRp
# Z2ljZXJ0LmNvbTBDBggrBgEFBQcwAoY3aHR0cDovL2NhY2VydHMuZGlnaWNlcnQu
# Y29tL0RpZ2lDZXJ0QXNzdXJlZElEUm9vdENBLmNydDBFBgNVHR8EPjA8MDqgOKA2
# hjRodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRBc3N1cmVkSURSb290
# Q0EuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQwFAAOCAQEAcKC/
# Q1xV5zhfoKN0Gz22Ftf3v1cHvZqsoYcs7IVeqRq7IviHGmlUIu2kiHdtvRoU9BNK
# ei8ttzjv9P+Aufih9/Jy3iS8UgPITtAq3votVs/59PesMHqai7Je1M/RQ0SbQyHr
# lnKhSLSZy51PpwYDE3cnRNTnf+hZqPC/Lwum6fI0POz3A8eHqNJMQBk1RmppVLC4
# oVaO7KTVPeix3P0c2PR3WlxUjG/voVA9/HYJaISfb8rbII01YBwCA8sgsKxYoA5A
# Y8WYIsGyWfVVa88nq2x2zm8jLfR+cWojayL/ErhULSd+2DrZ8LaHlv1b0VysGMNN
# n3O3AamfV6peKOK5lDCCBq4wggSWoAMCAQICEAc2N7ckVHzYR6z9KGYqXlswDQYJ
# KoZIhvcNAQELBQAwYjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IElu
# YzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTEhMB8GA1UEAxMYRGlnaUNlcnQg
# VHJ1c3RlZCBSb290IEc0MB4XDTIyMDMyMzAwMDAwMFoXDTM3MDMyMjIzNTk1OVow
# YzELMAkGA1UEBhMCVVMxFzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQD
# EzJEaWdpQ2VydCBUcnVzdGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGlu
# ZyBDQTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAMaGNQZJs8E9cklR
# VcclA8TykTepl1Gh1tKD0Z5Mom2gsMyD+Vr2EaFEFUJfpIjzaPp985yJC3+dH54P
# Mx9QEwsmc5Zt+FeoAn39Q7SE2hHxc7Gz7iuAhIoiGN/r2j3EF3+rGSs+QtxnjupR
# PfDWVtTnKC3r07G1decfBmWNlCnT2exp39mQh0YAe9tEQYncfGpXevA3eZ9drMvo
# hGS0UvJ2R/dhgxndX7RUCyFobjchu0CsX7LeSn3O9TkSZ+8OpWNs5KbFHc02DVzV
# 5huowWR0QKfAcsW6Th+xtVhNef7Xj3OTrCw54qVI1vCwMROpVymWJy71h6aPTnYV
# VSZwmCZ/oBpHIEPjQ2OAe3VuJyWQmDo4EbP29p7mO1vsgd4iFNmCKseSv6De4z6i
# c/rnH1pslPJSlRErWHRAKKtzQ87fSqEcazjFKfPKqpZzQmiftkaznTqj1QPgv/Ci
# PMpC3BhIfxQ0z9JMq++bPf4OuGQq+nUoJEHtQr8FnGZJUlD0UfM2SU2LINIsVzV5
# K6jzRWC8I41Y99xh3pP+OcD5sjClTNfpmEpYPtMDiP6zj9NeS3YSUZPJjAw7W4oi
# qMEmCPkUEBIDfV8ju2TjY+Cm4T72wnSyPx4JduyrXUZ14mCjWAkBKAAOhFTuzuld
# yF4wEr1GnrXTdrnSDmuZDNIztM2xAgMBAAGjggFdMIIBWTASBgNVHRMBAf8ECDAG
# AQH/AgEAMB0GA1UdDgQWBBS6FtltTYUvcyl2mi91jGogj57IbzAfBgNVHSMEGDAW
# gBTs1+OC0nFdZEzfLmc/57qYrhwPTzAOBgNVHQ8BAf8EBAMCAYYwEwYDVR0lBAww
# CgYIKwYBBQUHAwgwdwYIKwYBBQUHAQEEazBpMCQGCCsGAQUFBzABhhhodHRwOi8v
# b2NzcC5kaWdpY2VydC5jb20wQQYIKwYBBQUHMAKGNWh0dHA6Ly9jYWNlcnRzLmRp
# Z2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRSb290RzQuY3J0MEMGA1UdHwQ8MDow
# OKA2oDSGMmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFRydXN0ZWRS
# b290RzQuY3JsMCAGA1UdIAQZMBcwCAYGZ4EMAQQCMAsGCWCGSAGG/WwHATANBgkq
# hkiG9w0BAQsFAAOCAgEAfVmOwJO2b5ipRCIBfmbW2CFC4bAYLhBNE88wU86/GPvH
# UF3iSyn7cIoNqilp/GnBzx0H6T5gyNgL5Vxb122H+oQgJTQxZ822EpZvxFBMYh0M
# CIKoFr2pVs8Vc40BIiXOlWk/R3f7cnQU1/+rT4osequFzUNf7WC2qk+RZp4snuCK
# rOX9jLxkJodskr2dfNBwCnzvqLx1T7pa96kQsl3p/yhUifDVinF2ZdrM8HKjI/rA
# J4JErpknG6skHibBt94q6/aesXmZgaNWhqsKRcnfxI2g55j7+6adcq/Ex8HBanHZ
# xhOACcS2n82HhyS7T6NJuXdmkfFynOlLAlKnN36TU6w7HQhJD5TNOXrd/yVjmScs
# PT9rp/Fmw0HNT7ZAmyEhQNC3EyTN3B14OuSereU0cZLXJmvkOHOrpgFPvT87eK1M
# rfvElXvtCl8zOYdBeHo46Zzh3SP9HSjTx/no8Zhf+yvYfvJGnXUsHicsJttvFXse
# GYs2uJPU5vIXmVnKcPA3v5gA3yAWTyf7YGcWoWa63VXAOimGsJigK+2VQbc61RWY
# MbRiCQ8KvYHZE/6/pNHzV9m8BPqC3jLfBInwAM1dwvnQI38AC+R2AibZ8GV2QqYp
# hwlHK+Z/GqSFD/yYlvZVVCsfgPrA8g4r5db7qS9EFUrnEw4d2zc4GqEr9u3WfPww
# ggbAMIIEqKADAgECAhAMTWlyS5T6PCpKPSkHgD1aMA0GCSqGSIb3DQEBCwUAMGMx
# CzAJBgNVBAYTAlVTMRcwFQYDVQQKEw5EaWdpQ2VydCwgSW5jLjE7MDkGA1UEAxMy
# RGlnaUNlcnQgVHJ1c3RlZCBHNCBSU0E0MDk2IFNIQTI1NiBUaW1lU3RhbXBpbmcg
# Q0EwHhcNMjIwOTIxMDAwMDAwWhcNMzMxMTIxMjM1OTU5WjBGMQswCQYDVQQGEwJV
# UzERMA8GA1UEChMIRGlnaUNlcnQxJDAiBgNVBAMTG0RpZ2lDZXJ0IFRpbWVzdGFt
# cCAyMDIyIC0gMjCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAM/spSY6
# xqnya7uNwQ2a26HoFIV0MxomrNAcVR4eNm28klUMYfSdCXc9FZYIL2tkpP0GgxbX
# kZI4HDEClvtysZc6Va8z7GGK6aYo25BjXL2JU+A6LYyHQq4mpOS7eHi5ehbhVsbA
# umRTuyoW51BIu4hpDIjG8b7gL307scpTjUCDHufLckkoHkyAHoVW54Xt8mG8qjoH
# ffarbuVm3eJc9S/tjdRNlYRo44DLannR0hCRRinrPibytIzNTLlmyLuqUDgN5YyU
# XRlav/V7QG5vFqianJVHhoV5PgxeZowaCiS+nKrSnLb3T254xCg/oxwPUAY3ugjZ
# Naa1Htp4WB056PhMkRCWfk3h3cKtpX74LRsf7CtGGKMZ9jn39cFPcS6JAxGiS7uY
# v/pP5Hs27wZE5FX/NurlfDHn88JSxOYWe1p+pSVz28BqmSEtY+VZ9U0vkB8nt9Kr
# FOU4ZodRCGv7U0M50GT6Vs/g9ArmFG1keLuY/ZTDcyHzL8IuINeBrNPxB9Thvdld
# S24xlCmL5kGkZZTAWOXlLimQprdhZPrZIGwYUWC6poEPCSVT8b876asHDmoHOWIZ
# ydaFfxPZjXnPYsXs4Xu5zGcTB5rBeO3GiMiwbjJ5xwtZg43G7vUsfHuOy2SJ8bHE
# uOdTXl9V0n0ZKVkDTvpd6kVzHIR+187i1Dp3AgMBAAGjggGLMIIBhzAOBgNVHQ8B
# Af8EBAMCB4AwDAYDVR0TAQH/BAIwADAWBgNVHSUBAf8EDDAKBggrBgEFBQcDCDAg
# BgNVHSAEGTAXMAgGBmeBDAEEAjALBglghkgBhv1sBwEwHwYDVR0jBBgwFoAUuhbZ
# bU2FL3MpdpovdYxqII+eyG8wHQYDVR0OBBYEFGKK3tBh/I8xFO2XC809KpQU31Kc
# MFoGA1UdHwRTMFEwT6BNoEuGSWh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9EaWdp
# Q2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcmwwgZAG
# CCsGAQUFBwEBBIGDMIGAMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2Vy
# dC5jb20wWAYIKwYBBQUHMAKGTGh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9E
# aWdpQ2VydFRydXN0ZWRHNFJTQTQwOTZTSEEyNTZUaW1lU3RhbXBpbmdDQS5jcnQw
# DQYJKoZIhvcNAQELBQADggIBAFWqKhrzRvN4Vzcw/HXjT9aFI/H8+ZU5myXm93KK
# mMN31GT8Ffs2wklRLHiIY1UJRjkA/GnUypsp+6M/wMkAmxMdsJiJ3HjyzXyFzVOd
# r2LiYWajFCpFh0qYQitQ/Bu1nggwCfrkLdcJiXn5CeaIzn0buGqim8FTYAnoo7id
# 160fHLjsmEHw9g6A++T/350Qp+sAul9Kjxo6UrTqvwlJFTU2WZoPVNKyG39+Xgmt
# dlSKdG3K0gVnK3br/5iyJpU4GYhEFOUKWaJr5yI+RCHSPxzAm+18SLLYkgyRTzxm
# lK9dAlPrnuKe5NMfhgFknADC6Vp0dQ094XmIvxwBl8kZI4DXNlpflhaxYwzGRkA7
# zl011Fk+Q5oYrsPJy8P7mxNfarXH4PMFw1nfJ2Ir3kHJU7n/NBBn9iYymHv+XEKU
# gZSCnawKi8ZLFUrTmJBFYDOA4CPe+AOk9kVH5c64A0JH6EE2cXet/aLol3ROLtoe
# HYxayB6a1cLwxiKoT5u92ByaUcQvmvZfpyeXupYuhVfAYOd4Vn9q78KVmksRAsiC
# nMkaBXy6cbVOepls9Oie1FqYyJ+/jbsYXEP10Cro4mLueATbvdH7WwqocH7wl4R4
# 4wgDXUcsY6glOJcB0j862uXl9uab3H4szP8XTE0AotjWAQ64i+7m4HJViSwnGWH2
# dwGMMYIFXTCCBVkCAQEwgYYwcjELMAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lD
# ZXJ0IEluYzEZMBcGA1UECxMQd3d3LmRpZ2ljZXJ0LmNvbTExMC8GA1UEAxMoRGln
# aUNlcnQgU0hBMiBBc3N1cmVkIElEIENvZGUgU2lnbmluZyBDQQIQAwW7hiGwoWNf
# v96uEgTnbTANBglghkgBZQMEAgEFAKCBhDAYBgorBgEEAYI3AgEMMQowCKACgACh
# AoAAMBkGCSqGSIb3DQEJAzEMBgorBgEEAYI3AgEEMBwGCisGAQQBgjcCAQsxDjAM
# BgorBgEEAYI3AgEVMC8GCSqGSIb3DQEJBDEiBCBjjkmUbJUyjm96RG0HcDqsqDuH
# 3lVKndC59zNIh5x9yTANBgkqhkiG9w0BAQEFAASCAQBjXQYAWoqhOMqPjmAF8ark
# 8XuzDGtyaQ8oTbfTIO8eSJjECe7J2P7NCRHDDRj/Nof1ww5Y9YzXE71+Ia48LnH5
# W5CyzpECwa+OXD89WP/yOgyAi/WUxSlIyu10j4kxT+8ibdACiShurveWqYipkzvo
# i10sKjJkqgvF+aKcRv9rz8Huv8kIpDsP762hBrKlaz0dnGzOgK3GfSokj+AzmLxt
# qzne19HTCcq5acatN9rRwhm5hsUurPwUMwbX4+qdjeoGPjFAwbXdhC0U2EBaZx9o
# kYTt2uKW34tuF43xOSayhub8SsCIdGyB7DK9nzynbm6DgIOzfpdoTcb97deYd04m
# oYIDIDCCAxwGCSqGSIb3DQEJBjGCAw0wggMJAgEBMHcwYzELMAkGA1UEBhMCVVMx
# FzAVBgNVBAoTDkRpZ2lDZXJ0LCBJbmMuMTswOQYDVQQDEzJEaWdpQ2VydCBUcnVz
# dGVkIEc0IFJTQTQwOTYgU0hBMjU2IFRpbWVTdGFtcGluZyBDQQIQDE1pckuU+jwq
# Sj0pB4A9WjANBglghkgBZQMEAgEFAKBpMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0B
# BwEwHAYJKoZIhvcNAQkFMQ8XDTIzMDUxMjAwMjUyNlowLwYJKoZIhvcNAQkEMSIE
# IJg6nOKCm/9Ht1biMTZgb6n62oAJdzJzzUL4+PHmDmtGMA0GCSqGSIb3DQEBAQUA
# BIICACPLJMxWMWJABo78HEnAl5U1ysT2qk9J57AacjXR1WKmDcHli/EN11r2JhJa
# S4sF5gzpD6X4+XBgRFv2kJnkEb0H2ZMtFAs3vk8EuFeLC1KB3VNl0Rpftcq2HqON
# KGO2Licftp6Oswqz4MfSAQj3gEKhj4dbualHD8+gsm0DigQfKveasr4woWceM5UB
# RxhKS03VFRs88XHmFtLYTnXTS+VGuH22YMW6TvwExqANdUUSUB6edhW26qrRSx91
# peSIVGOlXElrLuinxMcT6gU4/rKmEae0x0oER+zcS/ZvbgRUZJGwPjyuO+u8bemP
# vyi3AseW8qlB7fRr4p8GQpOrDqiNSf5KthNRGVBMmRjsDSNT5LXCnqEHRUoH2PXC
# nCgYMxHh/0vCpqt2MmbthGqmLx3TvbOJOz3vJizTEYSM1w95OvV24gPdpKIpWepb
# qWZ9ia/NLaRnmu0akSpzAOEF2FwBWGX9EnyivEaMt2/1a2bHw+/WAFxd+L+I7qWo
# ncMoBxAsV5Nah2T4ksl2HMSjV66N5jYjAkNhf0fYdaHrLiUAg1s4UCPZSYzmjy7E
# an2lckfheB50HZsTnIfoRyOMAAtnjLKNrjQMiFqkUHDFVQzLZnAlo6jjsDpmEfC/
# nouurCViUb/74PQZx+wb+jCgaoZ4dvew0hUGuBa9uBCjuqxj
# SIG # End signature block
