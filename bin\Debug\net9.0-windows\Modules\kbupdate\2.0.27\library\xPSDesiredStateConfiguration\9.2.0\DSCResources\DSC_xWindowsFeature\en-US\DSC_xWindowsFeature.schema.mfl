[Description("This resource is used to install, uninstall and query roles or features on the node.") : Amended,AMENDMENT, LOCALE("MS_409")]
class DSC_xWindowsFeature : OMI_BaseResource
{
    [Key, Description("Indicates the name of the role or feature that you want to ensure is added or removed. This is the same as the Name property from the Get-WindowsFeature cmdlet, and not the display name of the role or feature.") : Amended] String Name;
    [Write, Description("Specifies whether the feature should be installed (Present) or uninstalled (Absent). Defaults to 'Present'.") : Amended] String Ensure;
    [Write, Description("Set this property to $true to ensure the state of all required subfeatures with the state of the feature you specify with the Name property. The default value is $false.") : Amended] Boolean IncludeAllSubFeature;
    [Write, Description("Indicates the credential to use to add or remove the role or feature if needed."), EmbeddedInstance("MSFT_Credential") : Amended] String Credential;
    [Write, Description("Indicates the path to a log file to log the operation. If not specified, the default log path will be used (%WINDIR%\\logs\\ServerManager.log).") : Amended] String LogPath;
    [Read, Description("The display name of the retrieved role or feature.") : Amended] String DisplayName;
};

