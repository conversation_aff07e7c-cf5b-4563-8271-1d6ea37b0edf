﻿Set-PSFConfig -Module PSFramework -Name 'Logging.MaxErrorCount' -Value 128 -Initialize -Validation "integerpositive" -Handler { [PSFramework.Message.LogHost]::MaxErrorCount = $args[0] } -Description "The maximum number of error records maintained in-memory. This setting is on a per-Process basis. Runspaces share, jobs or other consoles counted separately."
Set-PSFConfig -Module PSFramework -Name 'Logging.MaxMessageCount' -Value 1024 -Initialize -Validation "integerpositive" -Handler { [PSFramework.Message.LogHost]::MaxMessageCount = $args[0] } -Description "The maximum number of messages that can be maintained in the in-memory message queue. This setting is on a per-Process basis. Runspaces share, jobs or other consoles counted separately."
Set-PSFConfig -Module PSFramework -Name 'Logging.MessageLogEnabled' -Value $true -Initialize -Validation "bool" -Handler { [PSFramework.Message.LogHost]::MessageLogEnabled = $args[0] } -Description "Governs, whether a log of recent messages is kept in memory. This setting is on a per-Process basis. Runspaces share, jobs or other consoles counted separately."
Set-PSFConfig -Module PSFramework -Name 'Logging.ErrorLogEnabled' -Value $true -Initialize -Validation "bool" -Handler { [PSFramework.Message.LogHost]::ErrorLogEnabled = $args[0] } -Description "Governs, whether a log of recent errors is kept in memory. This setting is on a per-Process basis. Runspaces share, jobs or other consoles counted separately."
Set-PSFConfig -Module PSFramework -Name 'Logging.DisableLogFlush' -Value $false -Initialize -Validation "bool" -Description "When shutting down the process, PSFramework will by default flush the log. This ensures that all events are properly logged. If this is not desired, it can be turned off with this setting."
Set-PSFConfig -Module PSFramework -Name 'Logging.Interval' -Value 1000 -Initialize -Validation "integerpositive" -Handler { [PSFramework.Message.LogHost]::Interval = $args[0] } -Description 'The interval at which the loging runspace runs. Increase to improve performance, reduce the minimize writing latency.'
Set-PSFConfig -Module PSFramework -Name 'Logging.Interval.Idle' -Value 5000 -Initialize -Validation "integerpositive" -Handler { [PSFramework.Message.LogHost]::IntervalIdle = $args[0] } -Description 'The interval at which the loging runspace runs, when there is nothing to do.'
Set-PSFConfig -Module PSFramework -Name 'Logging.Interval.IdleDuration' -Value (New-TimeSpan -Minutes 2) -Initialize -Validation "timespan" -Handler { [PSFramework.Message.LogHost]::IntervalIdleDuration = $args[0] } -Description 'The time with no message written that needs to occur for the logging runspace to switch to idle mode.'
Set-PSFConfig -Module PSFramework -Name 'Logging.Provider.Source' -Value $null -Initialize -Validation 'uriabsolute' -Description 'Path where PSFramework looks for a provider index file. This file is used to load and configure additional logging providers. See "Get-Help Import-PSFLoggingProvider -Detailed" for more information'
Set-PSFConfig -Module PSFramework -Name 'Logging.Enabled' -Value $true -Initialize -Validation 'bool' -Handler {
	[PSFramework.Message.LogHost]::LoggingEnabled = $args[0]
	if ($args[0]) { Start-PSFRunspace -Name 'psframework.logging' -NoMessage }
	else { Stop-PSFRunspace -Name 'psframework.logging' }
} -Description 'Whether the PSFramework performs any logging at all. Disabling this will stop the background runspace that performs the logging.'