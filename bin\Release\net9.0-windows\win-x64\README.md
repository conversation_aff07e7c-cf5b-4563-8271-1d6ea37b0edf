# Windows Update Tool

A Windows Forms application for downloading and installing Windows updates directly from the Microsoft Update Catalog.

## Features

- **Search Microsoft Update Catalog** - Find the latest cumulative updates for Windows 11
- **Download MSU/EXE files** - Get actual installable Windows Update packages
- **Direct Installation** - Install updates immediately after download
- **Smart Filtering** - Filter updates by title, build number, and architecture
- **Proper Update Management** - Uses Microsoft's official update mechanisms

## What's New (v2.0)

✅ **Replaced UUP Dump with Microsoft Update Catalog** - Now downloads actual installable update files
✅ **Real Windows Updates** - Downloads MSU and EXE files that can be installed directly
✅ **Cumulative Update Focus** - Specifically targets Windows 11 cumulative updates
✅ **PowerShell Integration** - Uses the `kbupdate` module for reliable catalog access
✅ **Proper Installation** - Uses Windows Update Standalone Installer (wusa.exe)

## Requirements

- .NET 9.0 or later
- PowerShell 5.0 or later
- **kbupdate PowerShell module** (replaces aria2c requirement)
- Windows 10/11
- Administrator privileges (for installing updates)

## Setup

### 🚀 Quick Start (Recommended)
1. **Download the complete package** - The application comes with kbupdate module bundled
2. **Extract and run** - No additional installation required
3. **Launch WindowsUpdateTool.exe** - Everything works out of the box

### 📦 For Developers/Distributors
If you're building from source:
```powershell
# Build and bundle everything automatically
.\build_and_bundle.ps1
```

### 🔧 Manual Setup (if needed)
The application will automatically:
- Use the bundled kbupdate module (preferred)
- Install kbupdate module if not bundled
- Prompt for manual installation if auto-install fails

**Manual installation (only if auto-install fails):**
```powershell
# Open PowerShell as Administrator
Install-Module kbupdate -Force
```

## Usage

### Getting Started
1. Run the application
2. Click "Fetch Updates" to load available updates
3. Use filters to narrow down results
4. Select an update and click "Download Selected" or right-click to download

### Creating Filters
1. Click "Add Filter" to create a new filter
2. Configure filter criteria:
   - **Name**: Give your filter a descriptive name
   - **Title Contains**: Show only updates with specific text in title
   - **Title Excludes**: Hide updates with specific text in title
   - **Build Contains**: Filter by build number patterns
   - **Build Excludes**: Exclude specific build numbers
   - **Architecture**: Filter by x64, ARM64, or x86
3. Click "Save Filters" to store your configuration

### Example Filters

**Exclude Insider Builds:**
- Name: "Exclude Insider"
- Title Excludes: "Insider"

**Windows 11 Only:**
- Name: "Windows 11 Only"
- Title Contains: "Windows 11"

**x64 Architecture Only:**
- Name: "x64 Only"
- Architecture: "amd64"

**Cumulative Updates Only:**
- Name: "Cumulative Only"
- Title Contains: "Cumulative Update"

### Downloading Updates
1. Select an update from the list
2. Click "Download Selected" or right-click and select "Download Update"
3. Review the download information dialog
4. Choose save location and filename
5. Monitor download progress
6. File will be saved as a ZIP package

## File Structure

- `Form1.cs` - Main application form with filtering and download logic
- `FilterForm.cs` - Dialog for creating/editing filters
- `FilterPresets.cs` - Predefined filter examples
- `AlternativeApis.cs` - Reference for other Windows Update APIs
- `update_filters.json` - Saved filter configurations
- `example_filters.json` - Example filter file

## Technical Details

### API Endpoint
- Uses UUP Dump API: `https://api.uupdump.net/listid.php`
- Download endpoint: `https://api.uupdump.net/get.php?id={uuid}`

### Filter Logic
- Multiple filters work as AND conditions
- All filter criteria must match for an update to be shown
- Empty filter fields are ignored

### Download Features
- Progress tracking with percentage and file size
- Automatic filename sanitization
- Error handling for network issues
- Confirmation dialog with update details

## Requirements

- .NET 9.0 or later
- Windows Forms support
- Internet connection for API access
- Sufficient disk space for downloads (updates can be several hundred MB)

## Troubleshooting

### No Updates Found
- Check your internet connection
- Verify API endpoint is accessible
- Try removing filters to see all updates

### Download Fails
- Ensure sufficient disk space
- Check network connection
- Verify the update UUID is valid

### Filter Issues
- Make sure filter names are unique
- Check that filter criteria are correctly entered
- Try loading saved filters if custom ones don't work

## 📦 Portable & Self-Contained

### No Installation Required
- **Bundled kbupdate module** - No separate PowerShell module installation needed
- **Portable application** - Copy to any Windows system and run
- **Auto-detection** - Automatically uses bundled module or installs if needed
- **Offline capable** - Basic functionality works without internet

### Smart Module Management
1. **Bundled First** - Uses included kbupdate module in `Modules` folder
2. **Auto-Install** - Downloads and installs module if not bundled
3. **Fallback** - Uses system-installed module if available
4. **User Prompt** - Asks for manual installation if all else fails

## Advantages Over Previous Version

| Old (UUP Dump) | New (Microsoft Update Catalog) |
|---|---|
| Downloaded UUP files for building Windows images | Downloads actual installable update packages |
| Required complex ISO creation process | Direct installation with MSU/EXE files |
| Files couldn't be installed on running system | Proper Windows Update installation |
| Used unofficial UUP Dump API | Uses official Microsoft Update Catalog |
| Required aria2c.exe | Uses PowerShell kbupdate module (bundled) |
| Manual dependency management | Self-contained with auto-installation |

## Technical Details

- **Bundled Dependencies** - Includes kbupdate PowerShell module by @potatoqualitee
- **Microsoft Update Catalog** - Searches official catalog for cumulative updates
- **MSU Download** - Downloads actual installable files using `Save-KbUpdate` cmdlet
- **Proper Installation** - Uses Windows Update Standalone Installer (wusa.exe)
- **Multi-format Support** - Handles MSU, CAB (DISM), and MSIX (PowerShell) packages
- **Portable Design** - No registry changes or system-wide installations required