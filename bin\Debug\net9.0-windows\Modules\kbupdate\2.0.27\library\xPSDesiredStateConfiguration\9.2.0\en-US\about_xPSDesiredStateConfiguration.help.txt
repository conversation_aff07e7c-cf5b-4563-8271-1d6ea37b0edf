TOPIC
    about_xPSDesiredStateConfiguration

SHORT DESCRIPTION
    DSC resources for configuring common operating systems features, files and
    settings.

LONG DESCRIPTION
    This module contains DSC resources for configuring common operating systems
    features, files and settings.

EXAMPLES
    PS C:\> Get-DscResource -Module xPSDesiredStateConfiguration

NOTE:
    Thank you to the DSC Community contributors who contributed to this module by
    writing code, sharing opinions, and provided feedback.

TROUBLESHOOTING NOTE:
    Go to the Github repository for read about issues, submit a new issue, and read
    about new releases. https://github.com/dsccommunity/xPSDesiredStateConfiguration

SEE ALSO
  - https://github.com/dsccommunity/xPSDesiredStateConfiguration

KEYWORDS
      DSC, DscResource, Archive, Environment, Group, MSI, Package, File,
      RemoteFile, Registry, Script, Service, User,
      WindowsFeature, WindowsOptionalFeature, WindowsPackageCab, WindowsProcess
