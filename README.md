# Windows Update Tool

A GUI application for browsing and downloading Windows updates using the UUP Dump API.

## Features

### 🔍 **Update Browsing**
- Fetch Windows 11/10 updates from UUP Dump API
- View update details: Title, UUID, Created Date, Build Number
- Real-time status updates and progress indicators

### 🎯 **Advanced Filtering**
- **Custom Filters**: Create filters to include/exclude specific updates
- **Filter Options**:
  - Title Contains/Excludes
  - Build Number Contains/Excludes
  - Architecture (x64, ARM64, x86)
- **Save/Load Filters**: Persist your filter configurations
- **Multiple Filters**: Combine filters with AND logic

### 💾 **Download Functionality**
- **Download Button**: Click "Download Selected" to download updates
- **Right-Click Menu**: Right-click on any update to download
- **Progress Tracking**: Real-time download progress with file size
- **Smart Naming**: Automatic filename generation based on update title
- **Confirmation Dialog**: Shows update details before downloading

## Usage

### Getting Started
1. Run the application
2. Click "Fetch Updates" to load available updates
3. Use filters to narrow down results
4. Select an update and click "Download Selected" or right-click to download

### Creating Filters
1. Click "Add Filter" to create a new filter
2. Configure filter criteria:
   - **Name**: Give your filter a descriptive name
   - **Title Contains**: Show only updates with specific text in title
   - **Title Excludes**: Hide updates with specific text in title
   - **Build Contains**: Filter by build number patterns
   - **Build Excludes**: Exclude specific build numbers
   - **Architecture**: Filter by x64, ARM64, or x86
3. Click "Save Filters" to store your configuration

### Example Filters

**Exclude Insider Builds:**
- Name: "Exclude Insider"
- Title Excludes: "Insider"

**Windows 11 Only:**
- Name: "Windows 11 Only"
- Title Contains: "Windows 11"

**x64 Architecture Only:**
- Name: "x64 Only"
- Architecture: "amd64"

**Cumulative Updates Only:**
- Name: "Cumulative Only"
- Title Contains: "Cumulative Update"

### Downloading Updates
1. Select an update from the list
2. Click "Download Selected" or right-click and select "Download Update"
3. Review the download information dialog
4. Choose save location and filename
5. Monitor download progress
6. File will be saved as a ZIP package

## File Structure

- `Form1.cs` - Main application form with filtering and download logic
- `FilterForm.cs` - Dialog for creating/editing filters
- `FilterPresets.cs` - Predefined filter examples
- `AlternativeApis.cs` - Reference for other Windows Update APIs
- `update_filters.json` - Saved filter configurations
- `example_filters.json` - Example filter file

## Technical Details

### API Endpoint
- Uses UUP Dump API: `https://api.uupdump.net/listid.php`
- Download endpoint: `https://api.uupdump.net/get.php?id={uuid}`

### Filter Logic
- Multiple filters work as AND conditions
- All filter criteria must match for an update to be shown
- Empty filter fields are ignored

### Download Features
- Progress tracking with percentage and file size
- Automatic filename sanitization
- Error handling for network issues
- Confirmation dialog with update details

## Requirements

- .NET 9.0 or later
- Windows Forms support
- Internet connection for API access
- Sufficient disk space for downloads (updates can be several hundred MB)

## Troubleshooting

### No Updates Found
- Check your internet connection
- Verify API endpoint is accessible
- Try removing filters to see all updates

### Download Fails
- Ensure sufficient disk space
- Check network connection
- Verify the update UUID is valid

### Filter Issues
- Make sure filter names are unique
- Check that filter criteria are correctly entered
- Try loading saved filters if custom ones don't work 