# Test UUP Dump Download API
param(
    [string]$UpdateId = "f5b8c5a1-1234-5678-9abc-def012345678"  # Example UUID
)

Write-Host "Testing UUP Dump Download API..." -ForegroundColor Green
Write-Host "Update ID: $UpdateId" -ForegroundColor Yellow

$url = "https://api.uupdump.net/get.php?id=$UpdateId"
Write-Host "URL: $url" -ForegroundColor Cyan

try {
    Write-Host "`nMaking request..." -ForegroundColor Green
    
    # Make the request
    $response = Invoke-WebRequest -Uri $url -Method Get -UseBasicParsing
    
    Write-Host "`nResponse Details:" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor White
    Write-Host "Content Type: $($response.Headers.'Content-Type')" -ForegroundColor White
    Write-Host "Content Length: $($response.Headers.'Content-Length')" -ForegroundColor White
    Write-Host "Response Length: $($response.Content.Length) bytes" -ForegroundColor White
    
    # Check if it's a ZIP file
    if ($response.Headers.'Content-Type' -like "*zip*" -or $response.Headers.'Content-Type' -like "*octet-stream*") {
        Write-Host "`n✅ Valid ZIP file detected!" -ForegroundColor Green
    } else {
        Write-Host "`n⚠️  Not a ZIP file. Content-Type: $($response.Headers.'Content-Type')" -ForegroundColor Yellow
        
        # Show first 500 characters of response
        $preview = $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length))
        Write-Host "`nFirst 500 characters of response:" -ForegroundColor Yellow
        Write-Host $preview -ForegroundColor Gray
    }
    
    # Check file size
    $sizeMB = [math]::Round($response.Content.Length / 1MB, 2)
    Write-Host "`nFile Size: $sizeMB MB" -ForegroundColor White
    
    if ($sizeMB -lt 50) {
        Write-Host "⚠️  File seems too small for a Windows update (< 50MB)" -ForegroundColor Red
    } else {
        Write-Host "✅ File size looks reasonable for a Windows update" -ForegroundColor Green
    }
    
} catch {
    Write-Host "`n❌ Error occurred:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host "`nTest completed." -ForegroundColor Green 