﻿function New-PSFTempDirectory {
<#
	.SYNOPSIS
		Create a temporary directory / folder.
	
	.DESCRIPTION
		Create a temporary directory / folder.
	
	.PARAMETER Name
		Name of the temporary directory item.
		Note: The actual foldername will be autogenerated and ignore this value.
	
	.PARAMETER DirectoryName
		Specify the exact name of the temporary directory to generate.
		By default, a generic name is autogenerated instead.
	
	.PARAMETER Timeout
		How long into the future this item is valid.
		Expired temporary items can be conveniently cleaned up using Remove-PSFTempItem.
	
	.PARAMETER ModuleName
		Name of the module the temp item belongs to.
		If called from within a module, this value will be detected automatically and needs not be specified.
	
	.EXAMPLE
		PS C:\> New-PSFTempDirectory -Name 'zipFolder'
		
		Creates a temporary directory item named 'zipFolder'
#>
	[Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSUseShouldProcessForStateChangingFunctions", "")]
	[OutputType([string])]
	[CmdletBinding()]
	param (
		[Parameter(Mandatory = $true)]
		[PsfValidateScript('PSFramework.Validate.SafeName', ErrorString = 'PSFramework.Validate.SafeName')]
		[string]
		$Name,
		
		[string]
		$DirectoryName,
		
		[PSFDateTime]
		$Timeout,
		
		[string]
		$ModuleName = [PSFramework.Utility.UtilityHost]::GetCallerInfo(1).CallerModule
	)
	
	begin {
		$tempPath = Get-PSFPath -Name Temp
	}
	process {
		$newPath = Join-Path -Path $tempPath -ChildPath "PSF_Temp_$(New-Guid)"
		if ($DirectoryName) { $newPath = Join-Path -Path $tempPath -ChildPath $DirectoryName }
		try { $null = New-Item -Path $newPath -ItemType Directory -Force -ErrorAction Stop }
		catch { $PSCmdlet.ThrowTerminatingError($_) }
		
		$tempItem = [PSFramework.Temp.TempItemDirectory]::new($Name, $ModuleName, $newPath, $script:tempItems)
		if ($Timeout) { $tempItem.Timeout = $Timeout }
		$newPath
	}
}